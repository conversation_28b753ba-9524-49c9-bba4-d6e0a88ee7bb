/****************************************************************************
** Copyright (c) 2006 - 2011, the LibQxt project.
** See the Qxt AUTHORS file for a list of authors and copyright holders.
** All rights reserved.
**
** Redistribution and use in source and binary forms, with or without
** modification, are permitted provided that the following conditions are met:
**     * Redistributions of source code must retain the above copyright
**       notice, this list of conditions and the following disclaimer.
**     * Redistributions in binary form must reproduce the above copyright
**       notice, this list of conditions and the following disclaimer in the
**       documentation and/or other materials provided with the distribution.
**     * Neither the name of the LibQxt project nor the
**       names of its contributors may be used to endorse or promote products
**       derived from this software without specific prior written permission.
**
** THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
** ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
** WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
** DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY
** DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
** (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
** LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
** ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
** (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
** SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
**
** <http://libqxt.org>  <<EMAIL>>
**
** This is a derived work of PictureFlow (http://pictureflow.googlecode.com)
**
** The original code was distributed under the following terms:
**
** Copyright (C) 2008 Ariya Hidayat (<EMAIL>)
** Copyright (C) 2007 Ariya Hidayat (<EMAIL>)
**
** Permission is hereby granted, free of charge, to any person obtaining a copy
** of this software and associated documentation files (the "Software"), to deal
** in the Software without restriction, including without limitation the rights
** to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
** copies of the Software, and to permit persons to whom the Software is
** furnished to do so, subject to the following conditions:
**
** The above copyright notice and this permission notice shall be included in
** all copies or substantial portions of the Software.
**
** THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
** IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
** FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
** AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
** LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
** OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
** THE SOFTWARE.
****************************************************************************/

#pragma once

#include <QWidget>
#include <QAbstractItemModel>


#include <QApplication>
#include <QCache>
#include <QHash>
#include <QImage>
#include <QKeyEvent>
#include <QPainter>
#include <QPixmap>
#include <QTimer>
#include <QWidget>
#include <QAbstractItemModel>
#include <QList>


/* ----------------------------------------------------------

QxtFlowViewState stores the state of all slides, i.e. all the necessary
information to be able to render them.

QxtFlowViewAnimator is responsible to move the slides during the
transition between slides, to achieve the effect similar to Cover Flow,
by changing the state.

QxtFlowViewSoftwareRenderer (or QxtFlowViewOpenGLRenderer) is
the actual 3-d renderer. It should render all slides given the state
(an instance of QxtFlowViewState).

Instances of all the above three classes are stored in
QxtFlowViewPrivate.

------------------------------------------------------- */

struct SlideInfo
{
    int  slideIndex;
    int  angle;
    long cx;
    long cy;
    int  blend;
};


class QxtFlowView : public QWidget
{
    Q_OBJECT

public:

    enum ReflectionEffect
    {
        NoReflection,
        PlainReflection,
        BlurredReflection
    };

    QxtFlowView( const QAbstractItemModel& model, int pictureColumn, int pictureRole, ReflectionEffect reflectionEffect, QWidget* parent = nullptr );
    ~QxtFlowView();

    void setSlideSize( const QSize size);

    QModelIndex currentIndex() const;

public Q_SLOTS:

    void onColorSchemeChanged( Qt::ColorScheme colorScheme );
    void setCurrentIndex( const QModelIndex& index );

    void showPrevious();
    void showNext();

    void showSlide(const QModelIndex& index );

    void render();
    void triggerRender();

Q_SIGNALS:
    void currentIndexChanged( const QModelIndex& index);

protected:
    virtual void paintEvent( QPaintEvent *event );
    virtual void keyPressEvent( QKeyEvent* event );
    virtual void mousePressEvent( QMouseEvent* event );
    virtual void mouseMoveEvent( QMouseEvent * event );
    virtual void mouseReleaseEvent( QMouseEvent* event );
    virtual void resizeEvent( QResizeEvent* event );
    virtual void wheelEvent (QWheelEvent * event );

private Q_SLOTS:
    void updateAnimation();


// QxtFlowViewPrivate
public:
    QTimer QxtFlowViewPrivate_triggerTimer;
    const QAbstractItemModel& QxtFlowViewPrivate_model;

    void QxtFlowViewPrivate_showSlide(int index);

    const int QxtFlowViewPrivate_picrole{ Qt::DecorationRole };
    const int QxtFlowViewPrivate_piccolumn{ 0 };


    int QxtFlowViewPrivate_currentcenter{ -1 };

    QPoint QxtFlowViewPrivate_lastgrabpos;
    const QModelIndex QxtFlowViewPrivate_rootindex;

public Q_SLOTS:
    void QxtFlowViewPrivate_dataChanged(const QModelIndex & topLeft, const QModelIndex & bottomRight);
    void QxtFlowViewPrivate_layoutChanged();
    void QxtFlowViewPrivate_reset();
    void QxtFlowViewPrivate_rowsInserted(const QModelIndex & parent, int start, int end);
    void QxtFlowViewPrivate_rowsRemoved(const QModelIndex & parent, int start, int end);

// QxtFlowViewSoftwareRenderer renderer;
public:
    bool QxtFlowViewSoftwareRenderer_dirty{ false };

    void QxtFlowViewSoftwareRenderer_init();
    void QxtFlowViewSoftwareRenderer_paint();

private:
    QSize QxtFlowViewSoftwareRenderer_size{ 0, 0 };
    QRgb QxtFlowViewSoftwareRenderer_bgcolor{ 0 };
    QImage QxtFlowViewSoftwareRenderer_buffer;
    QVector<long> QxtFlowViewSoftwareRenderer_rays;
    QImage* QxtFlowViewSoftwareRenderer_blankSurface{ nullptr };

    void QxtFlowViewSoftwareRenderer_render();
    void QxtFlowViewSoftwareRenderer_renderSlides();
    QRect QxtFlowViewSoftwareRenderer_renderSlide(const SlideInfo &slide, int col1 = -1, int col2 = -1);
    QImage* QxtFlowViewSoftwareRenderer_surface(int slideIndex);

// QxtFlowViewAnimator
protected:
    void QxtFlowViewAnimator_start(int slide);
    void QxtFlowViewAnimator_stop(int slide);
    void QxtFlowViewAnimator_update();

    int QxtFlowViewAnimator_target{ 0 };
    int QxtFlowViewAnimator_step{ 0 };
    int QxtFlowViewAnimator_frame{ 0 };
    QTimer QxtFlowViewAnimator_animateTimer;

// QxtFlowViewState
protected:
    void QxtFlowViewState_reposition();
    void QxtFlowViewState_reset();

//    QSize QxtFlowViewState_slideSize{ 150, 200 };
    QSize QxtFlowViewState_slideSize{ 600, 1200 };
    const QxtFlowView::ReflectionEffect QxtFlowViewState_reflectionEffect { QxtFlowView::BlurredReflection };


    long QxtFlowViewState_offsetX;
    long QxtFlowViewState_offsetY;

    SlideInfo QxtFlowViewState_centerSlide;
    QList<SlideInfo> QxtFlowViewState_leftSlides;
    QList<SlideInfo> QxtFlowViewState_rightSlides;
    int QxtFlowViewState_centerIndex{ 0 };
};
