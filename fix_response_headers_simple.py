#!/usr/bin/env python3
"""
Simple script to add getResult() declarations to response headers.
"""

import os
import re
from pathlib import Path

def fix_response_header_simple(file_path):
    """Add getResult() declaration to response header."""
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Check if getResult is already declared
    if 'getResult() const override' in content:
        return
    
    # Find the line with "// LspMessage interface" or similar and replace
    content = re.sub(
        r'    // LspMessage interface\s*\n\s*QJsonObject toJson\(\) const override;',
        '    // LspResponse interface\n    QJsonValue getResult() const override;',
        content
    )
    
    with open(file_path, 'w') as f:
        f.write(content)

def main():
    language_dir = Path("lsp/messages/language")
    workspace_dir = Path("lsp/messages/workspace")
    
    # Fix response headers
    for file_path in language_dir.glob("*Response.h"):
        if file_path.name not in ["HoverResponse.h", "DefinitionResponse.h"]:  # Already fixed
            fix_response_header_simple(file_path)
            print(f"Fixed: {file_path}")
    
    for file_path in workspace_dir.glob("*Response.h"):
        fix_response_header_simple(file_path)
        print(f"Fixed: {file_path}")

if __name__ == "__main__":
    main()
