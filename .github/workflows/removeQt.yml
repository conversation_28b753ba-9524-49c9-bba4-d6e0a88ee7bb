name: removeQt

on:
  workflow_dispatch:

jobs:
  mac_Qt_remove:
    runs-on: [self-hosted, macOS, ARM64]
    steps:
    - name: cleanup folders
      run: |
        rm -rf qtSource
        rm -rf qtBuild
        rm -rf qtRelease
        rm -rf ${{ github.workspace }}/../qtRelease
        ls -la ./
        
  windows_Qt_remove:
    runs-on: [self-hosted, Windows, X64]
    steps:
    - name: cleanup folders
      run: |
        Remove-Item -Recurse -Force \\?\${{ github.workspace }}\qtSource
        Remove-Item -Recurse -Force \\?\${{ github.workspace }}\qtBuild
        Remove-Item -Recurse -Force \\?\${{ github.workspace }}\qtRelease
        Remove-Item -Recurse -Force \\?\${{ github.workspace }}\..\qtRelease
        dir *.*
