#include "PythonIncludes.h"
#ifdef PYTHON_STATIC_BUILD
#include "App.h"
#endif
#include "PythonThread.h"
#include "ContentEngine.h"

#include <QThread>

// Forward declarations for Python utility functions
extern void replacementStdoutFunction( const char* cDisplay );
extern void replacementStderrFunction( const char* cError );
extern pybind11::str TrySail_input( const std::string stdPrompt );
extern void TrySail_displayhook( const pybind11::handle* pHandleValue );

template < typename T >
inline void redirectPipeWrite( T t, const char* pipeName )
{
    auto pipeNative = PySys_GetObject( pipeName );
    auto pipeObject = pybind11::reinterpret_borrow< pybind11::object >( pipeNative );

    // overwrite write function
    auto functionWrite         = pybind11::cpp_function( std::forward< T >( t ) );
    pipeObject.attr( "write" ) = functionWrite;
}

// global command line arguments
extern int    global_argc;
extern char** global_argv;

void QPythonThread::run()
{
#ifdef PYTHON_STATIC_BUILD
    Nuitka_Init( global_argc, global_argv );
#else
    pybind11::scoped_interpreter guard;
#endif
    try {
        redirectPipeWrite( replacementStdoutFunction, "stdout" );
        redirectPipeWrite( replacementStderrFunction, "stderr" );
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error redirecting pipes: %s.\n", ex.what() );
    }

    try {
        // replace input with dialog
        const auto moduleBuiltins = pybind11::module::import( "builtins" );
        const auto functionInput  = pybind11::cpp_function( TrySail_input );
        moduleBuiltins.attr( "input" ) = functionInput;
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error replacing input: %s.\n", ex.what() );
    }

    try {
        // replace display with custom
        const auto imSys                  = pybind11::module::import("sys");
        const auto functionDisplayHook    = pybind11::cpp_function( TrySail_displayhook );
        imSys.attr( "displayhook" ) = functionDisplayHook;
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error replacing display hook: %s.\n", ex.what() );
    }

    try {
        // set matplotlib to use svg backed
        pybind11::module::import("matplotlib").attr("use")("svg");
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error matplotlib to svg: %s.\n", ex.what() );
    }

    try {
        // set matplotlib default background
        auto modMatplotlib = pybind11::module::import("matplotlib");
        pybind11::dict rcParams = modMatplotlib.attr( "rcParams" );
        rcParams["axes.facecolor"] = "none";
        rcParams["figure.facecolor"] = "none";
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error matplotlib default background: %s.\n", ex.what() );
    }

    try {
        // redirect figure manager to TrySail figure manager
        auto clsTrySailFigureManager = pybind11::module::import("TrySail").attr("TrySailFigureManager");
        auto mod_backend_bases = pybind11::module_::import("matplotlib.backend_bases");
        mod_backend_bases.attr("FigureManagerBase") = clsTrySailFigureManager;
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error redirect figure manager to TrySail figure manager: %s.\n", ex.what() );
    }

    try {
        // register image viewer within TrySail
        auto imShow = pybind11::module::import("PIL.ImageShow");
        pybind11::object clsViewer = pybind11::module::import("TrySail").attr("TrySailViewer");
        auto pViewer = clsViewer();
        auto fnRegister = imShow.attr( "register" );
        fnRegister( pViewer, 0 ); // register internal viewer as highest priority
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error register image viewer: %s.\n", ex.what() );
    }

    try {
        // execute main in "site" so that functions like "help" are available
        pybind11::module::import("site").attr("main")();
    }
    catch ( pybind11::error_already_set& ex ) {

        printf( "Error executing main in site: %s.\n", ex.what() );
    }


    {
        pybind11::gil_scoped_release release;
        QThread::run();
    }
}

void QPythonThread::sendInterrupt()
{
    PyErr_SetInterrupt();
}
