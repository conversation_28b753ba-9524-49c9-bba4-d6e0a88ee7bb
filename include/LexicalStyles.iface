## This file defines the interface to Lexilla

## Copyright 2000-2020 by <PERSON> <<EMAIL>>
## The License.txt file describes the conditions under which this software may be distributed.

## Similar file structure as Scintilla.iface but only contains constants.

cat Default

################################################
# For SciLexer.h
enu Lexer=SCLEX_
val SCLEX_CONTAINER=0
val SCLEX_NULL=1
val SCLEX_PYTHON=2
val SCLEX_CPP=3
val SCLEX_HTML=4
val SCLEX_XML=5
val SCLEX_PERL=6
val SCLEX_SQL=7
val SCLEX_VB=8
val SCLEX_PROPERTIES=9
val SCLEX_ERRORLIST=10
val SCLEX_MAKEFILE=11
val SCLEX_BATCH=12
val SCLEX_XCODE=13
val SCLEX_LATEX=14
val SCLEX_LUA=15
val SCLEX_DIFF=16
val SCLEX_CONF=17
val SCLEX_PASCAL=18
val SCLEX_AVE=19
val SCLEX_ADA=20
val SCLEX_LISP=21
val SCLEX_RUBY=22
val SCLEX_EIFFEL=23
val SCLEX_EIFFELKW=24
val SCLEX_TCL=25
val SCLEX_NNCRONTAB=26
val SCLEX_BULLANT=27
val SCLEX_VBSCRIPT=28
val SCLEX_BAAN=31
val SCLEX_MATLAB=32
val SCLEX_SCRIPTOL=33
val SCLEX_ASM=34
val SCLEX_CPPNOCASE=35
val SCLEX_FORTRAN=36
val SCLEX_F77=37
val SCLEX_CSS=38
val SCLEX_POV=39
val SCLEX_LOUT=40
val SCLEX_ESCRIPT=41
val SCLEX_PS=42
val SCLEX_NSIS=43
val SCLEX_MMIXAL=44
val SCLEX_CLW=45
val SCLEX_CLWNOCASE=46
val SCLEX_LOT=47
val SCLEX_YAML=48
val SCLEX_TEX=49
val SCLEX_METAPOST=50
val SCLEX_POWERBASIC=51
val SCLEX_FORTH=52
val SCLEX_ERLANG=53
val SCLEX_OCTAVE=54
val SCLEX_MSSQL=55
val SCLEX_VERILOG=56
val SCLEX_KIX=57
val SCLEX_GUI4CLI=58
val SCLEX_SPECMAN=59
val SCLEX_AU3=60
val SCLEX_APDL=61
val SCLEX_BASH=62
val SCLEX_ASN1=63
val SCLEX_VHDL=64
val SCLEX_CAML=65
val SCLEX_BLITZBASIC=66
val SCLEX_PUREBASIC=67
val SCLEX_HASKELL=68
val SCLEX_PHPSCRIPT=69
val SCLEX_TADS3=70
val SCLEX_REBOL=71
val SCLEX_SMALLTALK=72
val SCLEX_FLAGSHIP=73
val SCLEX_CSOUND=74
val SCLEX_FREEBASIC=75
val SCLEX_INNOSETUP=76
val SCLEX_OPAL=77
val SCLEX_SPICE=78
val SCLEX_D=79
val SCLEX_CMAKE=80
val SCLEX_GAP=81
val SCLEX_PLM=82
val SCLEX_PROGRESS=83
val SCLEX_ABAQUS=84
val SCLEX_ASYMPTOTE=85
val SCLEX_R=86
val SCLEX_MAGIK=87
val SCLEX_POWERSHELL=88
val SCLEX_MYSQL=89
val SCLEX_PO=90
val SCLEX_TAL=91
val SCLEX_COBOL=92
val SCLEX_TACL=93
val SCLEX_SORCUS=94
val SCLEX_POWERPRO=95
val SCLEX_NIMROD=96
val SCLEX_SML=97
val SCLEX_MARKDOWN=98
val SCLEX_TXT2TAGS=99
val SCLEX_A68K=100
val SCLEX_MODULA=101
val SCLEX_COFFEESCRIPT=102
val SCLEX_TCMD=103
val SCLEX_AVS=104
val SCLEX_ECL=105
val SCLEX_OSCRIPT=106
val SCLEX_VISUALPROLOG=107
val SCLEX_LITERATEHASKELL=108
val SCLEX_STTXT=109
val SCLEX_KVIRC=110
val SCLEX_RUST=111
val SCLEX_DMAP=112
val SCLEX_AS=113
val SCLEX_DMIS=114
val SCLEX_REGISTRY=115
val SCLEX_BIBTEX=116
val SCLEX_SREC=117
val SCLEX_IHEX=118
val SCLEX_TEHEX=119
val SCLEX_JSON=120
val SCLEX_EDIFACT=121
val SCLEX_INDENT=122
val SCLEX_MAXIMA=123
val SCLEX_STATA=124
val SCLEX_SAS=125
val SCLEX_NIM=126
val SCLEX_CIL=127
val SCLEX_X12=128
val SCLEX_DATAFLEX=129
val SCLEX_HOLLYWOOD=130
val SCLEX_RAKU=131
val SCLEX_FSHARP=132
val SCLEX_JULIA=133
val SCLEX_ASCIIDOC=134
val SCLEX_GDSCRIPT=135
val SCLEX_TOML=136
val SCLEX_TROFF=137
val SCLEX_DART=138
val SCLEX_ZIG=139
val SCLEX_NIX=140

# When a lexer specifies its language as SCLEX_AUTOMATIC it receives a
# value assigned in sequence from SCLEX_AUTOMATIC+1.
val SCLEX_AUTOMATIC=1000
# Lexical states for SCLEX_PYTHON
lex Python=SCLEX_PYTHON SCE_P_
lex Nimrod=SCLEX_NIMROD SCE_P_
val SCE_P_DEFAULT=0
val SCE_P_COMMENTLINE=1
val SCE_P_NUMBER=2
val SCE_P_STRING=3
val SCE_P_CHARACTER=4
val SCE_P_WORD=5
val SCE_P_TRIPLE=6
val SCE_P_TRIPLEDOUBLE=7
val SCE_P_CLASSNAME=8
val SCE_P_DEFNAME=9
val SCE_P_OPERATOR=10
val SCE_P_IDENTIFIER=11
val SCE_P_COMMENTBLOCK=12
val SCE_P_STRINGEOL=13
val SCE_P_WORD2=14
val SCE_P_DECORATOR=15
val SCE_P_FSTRING=16
val SCE_P_FCHARACTER=17
val SCE_P_FTRIPLE=18
val SCE_P_FTRIPLEDOUBLE=19
val SCE_P_ATTRIBUTE=20
# Lexical states for SCLEX_CPP
# Lexical states for SCLEX_BULLANT
# Lexical states for SCLEX_TACL
# Lexical states for SCLEX_TAL
lex Cpp=SCLEX_CPP SCE_C_
lex BullAnt=SCLEX_BULLANT SCE_C_
lex TACL=SCLEX_TACL SCE_C_
lex TAL=SCLEX_TAL SCE_C_
val SCE_C_DEFAULT=0
val SCE_C_COMMENT=1
val SCE_C_COMMENTLINE=2
val SCE_C_COMMENTDOC=3
val SCE_C_NUMBER=4
val SCE_C_WORD=5
val SCE_C_STRING=6
val SCE_C_CHARACTER=7
val SCE_C_UUID=8
val SCE_C_PREPROCESSOR=9
val SCE_C_OPERATOR=10
val SCE_C_IDENTIFIER=11
val SCE_C_STRINGEOL=12
val SCE_C_VERBATIM=13
val SCE_C_REGEX=14
val SCE_C_COMMENTLINEDOC=15
val SCE_C_WORD2=16
val SCE_C_COMMENTDOCKEYWORD=17
val SCE_C_COMMENTDOCKEYWORDERROR=18
val SCE_C_GLOBALCLASS=19
val SCE_C_STRINGRAW=20
val SCE_C_TRIPLEVERBATIM=21
val SCE_C_HASHQUOTEDSTRING=22
val SCE_C_PREPROCESSORCOMMENT=23
val SCE_C_PREPROCESSORCOMMENTDOC=24
val SCE_C_USERLITERAL=25
val SCE_C_TASKMARKER=26
val SCE_C_ESCAPESEQUENCE=27
# Lexical states for SCLEX_COBOL
lex COBOL=SCLEX_COBOL SCE_COBOL_
val SCE_COBOL_DEFAULT=0
val SCE_COBOL_COMMENT=1
val SCE_COBOL_COMMENTLINE=2
val SCE_COBOL_COMMENTDOC=3
val SCE_COBOL_NUMBER=4
val SCE_COBOL_WORD=5
val SCE_COBOL_STRING=6
val SCE_COBOL_CHARACTER=7
val SCE_COBOL_WORD3=8
val SCE_COBOL_PREPROCESSOR=9
val SCE_COBOL_OPERATOR=10
val SCE_COBOL_IDENTIFIER=11
val SCE_COBOL_WORD2=16
# Lexical states for SCLEX_D
lex D=SCLEX_D SCE_D_
val SCE_D_DEFAULT=0
val SCE_D_COMMENT=1
val SCE_D_COMMENTLINE=2
val SCE_D_COMMENTDOC=3
val SCE_D_COMMENTNESTED=4
val SCE_D_NUMBER=5
val SCE_D_WORD=6
val SCE_D_WORD2=7
val SCE_D_WORD3=8
val SCE_D_TYPEDEF=9
val SCE_D_STRING=10
val SCE_D_STRINGEOL=11
val SCE_D_CHARACTER=12
val SCE_D_OPERATOR=13
val SCE_D_IDENTIFIER=14
val SCE_D_COMMENTLINEDOC=15
val SCE_D_COMMENTDOCKEYWORD=16
val SCE_D_COMMENTDOCKEYWORDERROR=17
val SCE_D_STRINGB=18
val SCE_D_STRINGR=19
val SCE_D_WORD5=20
val SCE_D_WORD6=21
val SCE_D_WORD7=22
# Lexical states for SCLEX_TCL
lex TCL=SCLEX_TCL SCE_TCL_
val SCE_TCL_DEFAULT=0
val SCE_TCL_COMMENT=1
val SCE_TCL_COMMENTLINE=2
val SCE_TCL_NUMBER=3
val SCE_TCL_WORD_IN_QUOTE=4
val SCE_TCL_IN_QUOTE=5
val SCE_TCL_OPERATOR=6
val SCE_TCL_IDENTIFIER=7
val SCE_TCL_SUBSTITUTION=8
val SCE_TCL_SUB_BRACE=9
val SCE_TCL_MODIFIER=10
val SCE_TCL_EXPAND=11
val SCE_TCL_WORD=12
val SCE_TCL_WORD2=13
val SCE_TCL_WORD3=14
val SCE_TCL_WORD4=15
val SCE_TCL_WORD5=16
val SCE_TCL_WORD6=17
val SCE_TCL_WORD7=18
val SCE_TCL_WORD8=19
val SCE_TCL_COMMENT_BOX=20
val SCE_TCL_BLOCK_COMMENT=21
# Lexical states for SCLEX_HTML, SCLEX_XML
lex HTML=SCLEX_HTML SCE_H_ SCE_HJ_ SCE_HJA_ SCE_HB_ SCE_HBA_ SCE_HP_ SCE_HPHP_ SCE_HPA_
lex XML=SCLEX_XML SCE_H_ SCE_HJ_ SCE_HJA_ SCE_HB_ SCE_HBA_ SCE_HP_ SCE_HPHP_ SCE_HPA_
val SCE_H_DEFAULT=0
val SCE_H_TAG=1
val SCE_H_TAGUNKNOWN=2
val SCE_H_ATTRIBUTE=3
val SCE_H_ATTRIBUTEUNKNOWN=4
val SCE_H_NUMBER=5
val SCE_H_DOUBLESTRING=6
val SCE_H_SINGLESTRING=7
val SCE_H_OTHER=8
val SCE_H_COMMENT=9
val SCE_H_ENTITY=10
# XML and ASP
val SCE_H_TAGEND=11
val SCE_H_XMLSTART=12
val SCE_H_XMLEND=13
val SCE_H_SCRIPT=14
val SCE_H_ASP=15
val SCE_H_ASPAT=16
val SCE_H_CDATA=17
val SCE_H_QUESTION=18
# More HTML
val SCE_H_VALUE=19
# X-Code, ASP.NET, JSP
val SCE_H_XCCOMMENT=20
# SGML
val SCE_H_SGML_DEFAULT=21
val SCE_H_SGML_COMMAND=22
val SCE_H_SGML_1ST_PARAM=23
val SCE_H_SGML_DOUBLESTRING=24
val SCE_H_SGML_SIMPLESTRING=25
val SCE_H_SGML_ERROR=26
val SCE_H_SGML_SPECIAL=27
val SCE_H_SGML_ENTITY=28
val SCE_H_SGML_COMMENT=29
val SCE_H_SGML_1ST_PARAM_COMMENT=30
val SCE_H_SGML_BLOCK_DEFAULT=31
# Embedded Javascript
val SCE_HJ_START=40
val SCE_HJ_DEFAULT=41
val SCE_HJ_COMMENT=42
val SCE_HJ_COMMENTLINE=43
val SCE_HJ_COMMENTDOC=44
val SCE_HJ_NUMBER=45
val SCE_HJ_WORD=46
val SCE_HJ_KEYWORD=47
val SCE_HJ_DOUBLESTRING=48
val SCE_HJ_SINGLESTRING=49
val SCE_HJ_SYMBOLS=50
val SCE_HJ_STRINGEOL=51
val SCE_HJ_REGEX=52
val SCE_HJ_TEMPLATELITERAL=53
# ASP Javascript
val SCE_HJA_START=55
val SCE_HJA_DEFAULT=56
val SCE_HJA_COMMENT=57
val SCE_HJA_COMMENTLINE=58
val SCE_HJA_COMMENTDOC=59
val SCE_HJA_NUMBER=60
val SCE_HJA_WORD=61
val SCE_HJA_KEYWORD=62
val SCE_HJA_DOUBLESTRING=63
val SCE_HJA_SINGLESTRING=64
val SCE_HJA_SYMBOLS=65
val SCE_HJA_STRINGEOL=66
val SCE_HJA_REGEX=67
val SCE_HJA_TEMPLATELITERAL=68
# Embedded VBScript
val SCE_HB_START=70
val SCE_HB_DEFAULT=71
val SCE_HB_COMMENTLINE=72
val SCE_HB_NUMBER=73
val SCE_HB_WORD=74
val SCE_HB_STRING=75
val SCE_HB_IDENTIFIER=76
val SCE_HB_STRINGEOL=77
# ASP VBScript
val SCE_HBA_START=80
val SCE_HBA_DEFAULT=81
val SCE_HBA_COMMENTLINE=82
val SCE_HBA_NUMBER=83
val SCE_HBA_WORD=84
val SCE_HBA_STRING=85
val SCE_HBA_IDENTIFIER=86
val SCE_HBA_STRINGEOL=87
# Embedded Python
val SCE_HP_START=90
val SCE_HP_DEFAULT=91
val SCE_HP_COMMENTLINE=92
val SCE_HP_NUMBER=93
val SCE_HP_STRING=94
val SCE_HP_CHARACTER=95
val SCE_HP_WORD=96
val SCE_HP_TRIPLE=97
val SCE_HP_TRIPLEDOUBLE=98
val SCE_HP_CLASSNAME=99
val SCE_HP_DEFNAME=100
val SCE_HP_OPERATOR=101
val SCE_HP_IDENTIFIER=102
# PHP
val SCE_HPHP_COMPLEX_VARIABLE=104
# ASP Python
val SCE_HPA_START=105
val SCE_HPA_DEFAULT=106
val SCE_HPA_COMMENTLINE=107
val SCE_HPA_NUMBER=108
val SCE_HPA_STRING=109
val SCE_HPA_CHARACTER=110
val SCE_HPA_WORD=111
val SCE_HPA_TRIPLE=112
val SCE_HPA_TRIPLEDOUBLE=113
val SCE_HPA_CLASSNAME=114
val SCE_HPA_DEFNAME=115
val SCE_HPA_OPERATOR=116
val SCE_HPA_IDENTIFIER=117
# PHP
val SCE_HPHP_DEFAULT=118
val SCE_HPHP_HSTRING=119
val SCE_HPHP_SIMPLESTRING=120
val SCE_HPHP_WORD=121
val SCE_HPHP_NUMBER=122
val SCE_HPHP_VARIABLE=123
val SCE_HPHP_COMMENT=124
val SCE_HPHP_COMMENTLINE=125
val SCE_HPHP_HSTRING_VARIABLE=126
val SCE_HPHP_OPERATOR=127
# Lexical states for SCLEX_PERL
lex Perl=SCLEX_PERL SCE_PL_
val SCE_PL_DEFAULT=0
val SCE_PL_ERROR=1
val SCE_PL_COMMENTLINE=2
val SCE_PL_POD=3
val SCE_PL_NUMBER=4
val SCE_PL_WORD=5
val SCE_PL_STRING=6
val SCE_PL_CHARACTER=7
val SCE_PL_PUNCTUATION=8
val SCE_PL_PREPROCESSOR=9
val SCE_PL_OPERATOR=10
val SCE_PL_IDENTIFIER=11
val SCE_PL_SCALAR=12
val SCE_PL_ARRAY=13
val SCE_PL_HASH=14
val SCE_PL_SYMBOLTABLE=15
val SCE_PL_VARIABLE_INDEXER=16
val SCE_PL_REGEX=17
val SCE_PL_REGSUBST=18
val SCE_PL_LONGQUOTE=19
val SCE_PL_BACKTICKS=20
val SCE_PL_DATASECTION=21
val SCE_PL_HERE_DELIM=22
val SCE_PL_HERE_Q=23
val SCE_PL_HERE_QQ=24
val SCE_PL_HERE_QX=25
val SCE_PL_STRING_Q=26
val SCE_PL_STRING_QQ=27
val SCE_PL_STRING_QX=28
val SCE_PL_STRING_QR=29
val SCE_PL_STRING_QW=30
val SCE_PL_POD_VERB=31
val SCE_PL_SUB_PROTOTYPE=40
val SCE_PL_FORMAT_IDENT=41
val SCE_PL_FORMAT=42
val SCE_PL_STRING_VAR=43
val SCE_PL_XLAT=44
val SCE_PL_REGEX_VAR=54
val SCE_PL_REGSUBST_VAR=55
val SCE_PL_BACKTICKS_VAR=57
val SCE_PL_HERE_QQ_VAR=61
val SCE_PL_HERE_QX_VAR=62
val SCE_PL_STRING_QQ_VAR=64
val SCE_PL_STRING_QX_VAR=65
val SCE_PL_STRING_QR_VAR=66
# Lexical states for SCLEX_RUBY
lex Ruby=SCLEX_RUBY SCE_RB_
val SCE_RB_DEFAULT=0
val SCE_RB_ERROR=1
val SCE_RB_COMMENTLINE=2
val SCE_RB_POD=3
val SCE_RB_NUMBER=4
val SCE_RB_WORD=5
val SCE_RB_STRING=6
val SCE_RB_CHARACTER=7
val SCE_RB_CLASSNAME=8
val SCE_RB_DEFNAME=9
val SCE_RB_OPERATOR=10
val SCE_RB_IDENTIFIER=11
val SCE_RB_REGEX=12
val SCE_RB_GLOBAL=13
val SCE_RB_SYMBOL=14
val SCE_RB_MODULE_NAME=15
val SCE_RB_INSTANCE_VAR=16
val SCE_RB_CLASS_VAR=17
val SCE_RB_BACKTICKS=18
val SCE_RB_DATASECTION=19
val SCE_RB_HERE_DELIM=20
val SCE_RB_HERE_Q=21
val SCE_RB_HERE_QQ=22
val SCE_RB_HERE_QX=23
val SCE_RB_STRING_Q=24
val SCE_RB_STRING_QQ=25
val SCE_RB_STRING_QX=26
val SCE_RB_STRING_QR=27
val SCE_RB_STRING_QW=28
val SCE_RB_WORD_DEMOTED=29
val SCE_RB_STDIN=30
val SCE_RB_STDOUT=31
val SCE_RB_STDERR=40
val SCE_RB_STRING_W=41
val SCE_RB_STRING_I=42
val SCE_RB_STRING_QI=43
val SCE_RB_STRING_QS=44
val SCE_RB_UPPER_BOUND=45
# Lexical states for SCLEX_VB, SCLEX_VBSCRIPT, SCLEX_POWERBASIC, SCLEX_BLITZBASIC, SCLEX_PUREBASIC, SCLEX_FREEBASIC
lex VB=SCLEX_VB SCE_B_
lex VBScript=SCLEX_VBSCRIPT SCE_B_
lex PowerBasic=SCLEX_POWERBASIC SCE_B_
lex BlitzBasic=SCLEX_BLITZBASIC SCE_B_
lex PureBasic=SCLEX_PUREBASIC SCE_B_
lex FreeBasic=SCLEX_FREEBASIC SCE_B_
val SCE_B_DEFAULT=0
val SCE_B_COMMENT=1
val SCE_B_NUMBER=2
val SCE_B_KEYWORD=3
val SCE_B_STRING=4
val SCE_B_PREPROCESSOR=5
val SCE_B_OPERATOR=6
val SCE_B_IDENTIFIER=7
val SCE_B_DATE=8
val SCE_B_STRINGEOL=9
val SCE_B_KEYWORD2=10
val SCE_B_KEYWORD3=11
val SCE_B_KEYWORD4=12
val SCE_B_CONSTANT=13
val SCE_B_ASM=14
val SCE_B_LABEL=15
val SCE_B_ERROR=16
val SCE_B_HEXNUMBER=17
val SCE_B_BINNUMBER=18
val SCE_B_COMMENTBLOCK=19
val SCE_B_DOCLINE=20
val SCE_B_DOCBLOCK=21
val SCE_B_DOCKEYWORD=22
# Lexical states for SCLEX_PROPERTIES
lex Properties=SCLEX_PROPERTIES SCE_PROPS_
val SCE_PROPS_DEFAULT=0
val SCE_PROPS_COMMENT=1
val SCE_PROPS_SECTION=2
val SCE_PROPS_ASSIGNMENT=3
val SCE_PROPS_DEFVAL=4
val SCE_PROPS_KEY=5
# Lexical states for SCLEX_LATEX
lex LaTeX=SCLEX_LATEX SCE_L_
val SCE_L_DEFAULT=0
val SCE_L_COMMAND=1
val SCE_L_TAG=2
val SCE_L_MATH=3
val SCE_L_COMMENT=4
val SCE_L_TAG2=5
val SCE_L_MATH2=6
val SCE_L_COMMENT2=7
val SCE_L_VERBATIM=8
val SCE_L_SHORTCMD=9
val SCE_L_SPECIAL=10
val SCE_L_CMDOPT=11
val SCE_L_ERROR=12
# Lexical states for SCLEX_LUA
lex Lua=SCLEX_LUA SCE_LUA_
val SCE_LUA_DEFAULT=0
val SCE_LUA_COMMENT=1
val SCE_LUA_COMMENTLINE=2
val SCE_LUA_COMMENTDOC=3
val SCE_LUA_NUMBER=4
val SCE_LUA_WORD=5
val SCE_LUA_STRING=6
val SCE_LUA_CHARACTER=7
val SCE_LUA_LITERALSTRING=8
val SCE_LUA_PREPROCESSOR=9
val SCE_LUA_OPERATOR=10
val SCE_LUA_IDENTIFIER=11
val SCE_LUA_STRINGEOL=12
val SCE_LUA_WORD2=13
val SCE_LUA_WORD3=14
val SCE_LUA_WORD4=15
val SCE_LUA_WORD5=16
val SCE_LUA_WORD6=17
val SCE_LUA_WORD7=18
val SCE_LUA_WORD8=19
val SCE_LUA_LABEL=20
# Lexical states for SCLEX_ERRORLIST
lex ErrorList=SCLEX_ERRORLIST SCE_ERR_
val SCE_ERR_DEFAULT=0
val SCE_ERR_PYTHON=1
val SCE_ERR_GCC=2
val SCE_ERR_MS=3
val SCE_ERR_CMD=4
val SCE_ERR_BORLAND=5
val SCE_ERR_PERL=6
val SCE_ERR_NET=7
val SCE_ERR_LUA=8
val SCE_ERR_CTAG=9
val SCE_ERR_DIFF_CHANGED=10
val SCE_ERR_DIFF_ADDITION=11
val SCE_ERR_DIFF_DELETION=12
val SCE_ERR_DIFF_MESSAGE=13
val SCE_ERR_PHP=14
val SCE_ERR_ELF=15
val SCE_ERR_IFC=16
val SCE_ERR_IFORT=17
val SCE_ERR_ABSF=18
val SCE_ERR_TIDY=19
val SCE_ERR_JAVA_STACK=20
val SCE_ERR_VALUE=21
val SCE_ERR_GCC_INCLUDED_FROM=22
val SCE_ERR_ESCSEQ=23
val SCE_ERR_ESCSEQ_UNKNOWN=24
val SCE_ERR_GCC_EXCERPT=25
val SCE_ERR_BASH=26
val SCE_ERR_ES_BLACK=40
val SCE_ERR_ES_RED=41
val SCE_ERR_ES_GREEN=42
val SCE_ERR_ES_BROWN=43
val SCE_ERR_ES_BLUE=44
val SCE_ERR_ES_MAGENTA=45
val SCE_ERR_ES_CYAN=46
val SCE_ERR_ES_GRAY=47
val SCE_ERR_ES_DARK_GRAY=48
val SCE_ERR_ES_BRIGHT_RED=49
val SCE_ERR_ES_BRIGHT_GREEN=50
val SCE_ERR_ES_YELLOW=51
val SCE_ERR_ES_BRIGHT_BLUE=52
val SCE_ERR_ES_BRIGHT_MAGENTA=53
val SCE_ERR_ES_BRIGHT_CYAN=54
val SCE_ERR_ES_WHITE=55
# Lexical states for SCLEX_BATCH
lex Batch=SCLEX_BATCH SCE_BAT_
val SCE_BAT_DEFAULT=0
val SCE_BAT_COMMENT=1
val SCE_BAT_WORD=2
val SCE_BAT_LABEL=3
val SCE_BAT_HIDE=4
val SCE_BAT_COMMAND=5
val SCE_BAT_IDENTIFIER=6
val SCE_BAT_OPERATOR=7
val SCE_BAT_AFTER_LABEL=8
# Lexical states for SCLEX_TCMD
lex TCMD=SCLEX_TCMD SCE_TCMD_
val SCE_TCMD_DEFAULT=0
val SCE_TCMD_COMMENT=1
val SCE_TCMD_WORD=2
val SCE_TCMD_LABEL=3
val SCE_TCMD_HIDE=4
val SCE_TCMD_COMMAND=5
val SCE_TCMD_IDENTIFIER=6
val SCE_TCMD_OPERATOR=7
val SCE_TCMD_ENVIRONMENT=8
val SCE_TCMD_EXPANSION=9
val SCE_TCMD_CLABEL=10
# Lexical states for SCLEX_MAKEFILE
lex MakeFile=SCLEX_MAKEFILE SCE_MAKE_
val SCE_MAKE_DEFAULT=0
val SCE_MAKE_COMMENT=1
val SCE_MAKE_PREPROCESSOR=2
val SCE_MAKE_IDENTIFIER=3
val SCE_MAKE_OPERATOR=4
val SCE_MAKE_TARGET=5
val SCE_MAKE_IDEOL=9
# Lexical states for SCLEX_DIFF
lex Diff=SCLEX_DIFF SCE_DIFF_
val SCE_DIFF_DEFAULT=0
val SCE_DIFF_COMMENT=1
val SCE_DIFF_COMMAND=2
val SCE_DIFF_HEADER=3
val SCE_DIFF_POSITION=4
val SCE_DIFF_DELETED=5
val SCE_DIFF_ADDED=6
val SCE_DIFF_CHANGED=7
val SCE_DIFF_PATCH_ADD=8
val SCE_DIFF_PATCH_DELETE=9
val SCE_DIFF_REMOVED_PATCH_ADD=10
val SCE_DIFF_REMOVED_PATCH_DELETE=11
# Lexical states for SCLEX_CONF (Apache Configuration Files Lexer)
lex Conf=SCLEX_CONF SCE_CONF_
val SCE_CONF_DEFAULT=0
val SCE_CONF_COMMENT=1
val SCE_CONF_NUMBER=2
val SCE_CONF_IDENTIFIER=3
val SCE_CONF_EXTENSION=4
val SCE_CONF_PARAMETER=5
val SCE_CONF_STRING=6
val SCE_CONF_OPERATOR=7
val SCE_CONF_IP=8
val SCE_CONF_DIRECTIVE=9
# Lexical states for SCLEX_AVE, Avenue
lex Avenue=SCLEX_AVE SCE_AVE_
val SCE_AVE_DEFAULT=0
val SCE_AVE_COMMENT=1
val SCE_AVE_NUMBER=2
val SCE_AVE_WORD=3
val SCE_AVE_STRING=6
val SCE_AVE_ENUM=7
val SCE_AVE_STRINGEOL=8
val SCE_AVE_IDENTIFIER=9
val SCE_AVE_OPERATOR=10
val SCE_AVE_WORD1=11
val SCE_AVE_WORD2=12
val SCE_AVE_WORD3=13
val SCE_AVE_WORD4=14
val SCE_AVE_WORD5=15
val SCE_AVE_WORD6=16
# Lexical states for SCLEX_ADA
lex Ada=SCLEX_ADA SCE_ADA_
val SCE_ADA_DEFAULT=0
val SCE_ADA_WORD=1
val SCE_ADA_IDENTIFIER=2
val SCE_ADA_NUMBER=3
val SCE_ADA_DELIMITER=4
val SCE_ADA_CHARACTER=5
val SCE_ADA_CHARACTEREOL=6
val SCE_ADA_STRING=7
val SCE_ADA_STRINGEOL=8
val SCE_ADA_LABEL=9
val SCE_ADA_COMMENTLINE=10
val SCE_ADA_ILLEGAL=11
# Lexical states for SCLEX_BAAN
lex Baan=SCLEX_BAAN SCE_BAAN_
val SCE_BAAN_DEFAULT=0
val SCE_BAAN_COMMENT=1
val SCE_BAAN_COMMENTDOC=2
val SCE_BAAN_NUMBER=3
val SCE_BAAN_WORD=4
val SCE_BAAN_STRING=5
val SCE_BAAN_PREPROCESSOR=6
val SCE_BAAN_OPERATOR=7
val SCE_BAAN_IDENTIFIER=8
val SCE_BAAN_STRINGEOL=9
val SCE_BAAN_WORD2=10
val SCE_BAAN_WORD3=11
val SCE_BAAN_WORD4=12
val SCE_BAAN_WORD5=13
val SCE_BAAN_WORD6=14
val SCE_BAAN_WORD7=15
val SCE_BAAN_WORD8=16
val SCE_BAAN_WORD9=17
val SCE_BAAN_TABLEDEF=18
val SCE_BAAN_TABLESQL=19
val SCE_BAAN_FUNCTION=20
val SCE_BAAN_DOMDEF=21
val SCE_BAAN_FUNCDEF=22
val SCE_BAAN_OBJECTDEF=23
val SCE_BAAN_DEFINEDEF=24
# Lexical states for SCLEX_LISP
lex Lisp=SCLEX_LISP SCE_LISP_
val SCE_LISP_DEFAULT=0
val SCE_LISP_COMMENT=1
val SCE_LISP_NUMBER=2
val SCE_LISP_KEYWORD=3
val SCE_LISP_KEYWORD_KW=4
val SCE_LISP_SYMBOL=5
val SCE_LISP_STRING=6
val SCE_LISP_STRINGEOL=8
val SCE_LISP_IDENTIFIER=9
val SCE_LISP_OPERATOR=10
val SCE_LISP_SPECIAL=11
val SCE_LISP_MULTI_COMMENT=12
# Lexical states for SCLEX_EIFFEL and SCLEX_EIFFELKW
lex Eiffel=SCLEX_EIFFEL SCE_EIFFEL_
lex EiffelKW=SCLEX_EIFFELKW SCE_EIFFEL_
val SCE_EIFFEL_DEFAULT=0
val SCE_EIFFEL_COMMENTLINE=1
val SCE_EIFFEL_NUMBER=2
val SCE_EIFFEL_WORD=3
val SCE_EIFFEL_STRING=4
val SCE_EIFFEL_CHARACTER=5
val SCE_EIFFEL_OPERATOR=6
val SCE_EIFFEL_IDENTIFIER=7
val SCE_EIFFEL_STRINGEOL=8
# Lexical states for SCLEX_NNCRONTAB (nnCron crontab Lexer)
lex NNCronTab=SCLEX_NNCRONTAB SCE_NNCRONTAB_
val SCE_NNCRONTAB_DEFAULT=0
val SCE_NNCRONTAB_COMMENT=1
val SCE_NNCRONTAB_TASK=2
val SCE_NNCRONTAB_SECTION=3
val SCE_NNCRONTAB_KEYWORD=4
val SCE_NNCRONTAB_MODIFIER=5
val SCE_NNCRONTAB_ASTERISK=6
val SCE_NNCRONTAB_NUMBER=7
val SCE_NNCRONTAB_STRING=8
val SCE_NNCRONTAB_ENVIRONMENT=9
val SCE_NNCRONTAB_IDENTIFIER=10
# Lexical states for SCLEX_FORTH (Forth Lexer)
lex Forth=SCLEX_FORTH SCE_FORTH_
val SCE_FORTH_DEFAULT=0
val SCE_FORTH_COMMENT=1
val SCE_FORTH_COMMENT_ML=2
val SCE_FORTH_IDENTIFIER=3
val SCE_FORTH_CONTROL=4
val SCE_FORTH_KEYWORD=5
val SCE_FORTH_DEFWORD=6
val SCE_FORTH_PREWORD1=7
val SCE_FORTH_PREWORD2=8
val SCE_FORTH_NUMBER=9
val SCE_FORTH_STRING=10
val SCE_FORTH_LOCALE=11
# Lexical states for SCLEX_MATLAB
lex MatLab=SCLEX_MATLAB SCE_MATLAB_
val SCE_MATLAB_DEFAULT=0
val SCE_MATLAB_COMMENT=1
val SCE_MATLAB_COMMAND=2
val SCE_MATLAB_NUMBER=3
val SCE_MATLAB_KEYWORD=4
# single quoted string
val SCE_MATLAB_STRING=5
val SCE_MATLAB_OPERATOR=6
val SCE_MATLAB_IDENTIFIER=7
val SCE_MATLAB_DOUBLEQUOTESTRING=8
# Lexical states for SCLEX_MAXIMA
lex Maxima=SCLEX_MAXIMA SCE_MAXIMA_
val SCE_MAXIMA_OPERATOR=0
val SCE_MAXIMA_COMMANDENDING=1
val SCE_MAXIMA_COMMENT=2
val SCE_MAXIMA_NUMBER=3
val SCE_MAXIMA_STRING=4
val SCE_MAXIMA_COMMAND=5
val SCE_MAXIMA_VARIABLE=6
val SCE_MAXIMA_UNKNOWN=7
# Lexical states for SCLEX_SCRIPTOL
lex Sol=SCLEX_SCRIPTOL SCE_SCRIPTOL_
val SCE_SCRIPTOL_DEFAULT=0
val SCE_SCRIPTOL_WHITE=1
val SCE_SCRIPTOL_COMMENTLINE=2
val SCE_SCRIPTOL_PERSISTENT=3
val SCE_SCRIPTOL_CSTYLE=4
val SCE_SCRIPTOL_COMMENTBLOCK=5
val SCE_SCRIPTOL_NUMBER=6
val SCE_SCRIPTOL_STRING=7
val SCE_SCRIPTOL_CHARACTER=8
val SCE_SCRIPTOL_STRINGEOL=9
val SCE_SCRIPTOL_KEYWORD=10
val SCE_SCRIPTOL_OPERATOR=11
val SCE_SCRIPTOL_IDENTIFIER=12
val SCE_SCRIPTOL_TRIPLE=13
val SCE_SCRIPTOL_CLASSNAME=14
val SCE_SCRIPTOL_PREPROCESSOR=15
# Lexical states for SCLEX_ASM, SCLEX_AS
lex Asm=SCLEX_ASM SCE_ASM_
lex As=SCLEX_AS SCE_ASM_
val SCE_ASM_DEFAULT=0
val SCE_ASM_COMMENT=1
val SCE_ASM_NUMBER=2
val SCE_ASM_STRING=3
val SCE_ASM_OPERATOR=4
val SCE_ASM_IDENTIFIER=5
val SCE_ASM_CPUINSTRUCTION=6
val SCE_ASM_MATHINSTRUCTION=7
val SCE_ASM_REGISTER=8
val SCE_ASM_DIRECTIVE=9
val SCE_ASM_DIRECTIVEOPERAND=10
val SCE_ASM_COMMENTBLOCK=11
val SCE_ASM_CHARACTER=12
val SCE_ASM_STRINGEOL=13
val SCE_ASM_EXTINSTRUCTION=14
val SCE_ASM_COMMENTDIRECTIVE=15
# Lexical states for SCLEX_FORTRAN
lex Fortran=SCLEX_FORTRAN SCE_F_
lex F77=SCLEX_F77 SCE_F_
val SCE_F_DEFAULT=0
val SCE_F_COMMENT=1
val SCE_F_NUMBER=2
val SCE_F_STRING1=3
val SCE_F_STRING2=4
val SCE_F_STRINGEOL=5
val SCE_F_OPERATOR=6
val SCE_F_IDENTIFIER=7
val SCE_F_WORD=8
val SCE_F_WORD2=9
val SCE_F_WORD3=10
val SCE_F_PREPROCESSOR=11
val SCE_F_OPERATOR2=12
val SCE_F_LABEL=13
val SCE_F_CONTINUATION=14
# Lexical states for SCLEX_CSS
lex CSS=SCLEX_CSS SCE_CSS_
val SCE_CSS_DEFAULT=0
val SCE_CSS_TAG=1
val SCE_CSS_CLASS=2
val SCE_CSS_PSEUDOCLASS=3
val SCE_CSS_UNKNOWN_PSEUDOCLASS=4
val SCE_CSS_OPERATOR=5
val SCE_CSS_IDENTIFIER=6
val SCE_CSS_UNKNOWN_IDENTIFIER=7
val SCE_CSS_VALUE=8
val SCE_CSS_COMMENT=9
val SCE_CSS_ID=10
val SCE_CSS_IMPORTANT=11
val SCE_CSS_DIRECTIVE=12
val SCE_CSS_DOUBLESTRING=13
val SCE_CSS_SINGLESTRING=14
val SCE_CSS_IDENTIFIER2=15
val SCE_CSS_ATTRIBUTE=16
val SCE_CSS_IDENTIFIER3=17
val SCE_CSS_PSEUDOELEMENT=18
val SCE_CSS_EXTENDED_IDENTIFIER=19
val SCE_CSS_EXTENDED_PSEUDOCLASS=20
val SCE_CSS_EXTENDED_PSEUDOELEMENT=21
val SCE_CSS_GROUP_RULE=22
val SCE_CSS_VARIABLE=23
# Lexical states for SCLEX_POV
lex POV=SCLEX_POV SCE_POV_
val SCE_POV_DEFAULT=0
val SCE_POV_COMMENT=1
val SCE_POV_COMMENTLINE=2
val SCE_POV_NUMBER=3
val SCE_POV_OPERATOR=4
val SCE_POV_IDENTIFIER=5
val SCE_POV_STRING=6
val SCE_POV_STRINGEOL=7
val SCE_POV_DIRECTIVE=8
val SCE_POV_BADDIRECTIVE=9
val SCE_POV_WORD2=10
val SCE_POV_WORD3=11
val SCE_POV_WORD4=12
val SCE_POV_WORD5=13
val SCE_POV_WORD6=14
val SCE_POV_WORD7=15
val SCE_POV_WORD8=16
# Lexical states for SCLEX_LOUT
lex LOUT=SCLEX_LOUT SCE_LOUT_
val SCE_LOUT_DEFAULT=0
val SCE_LOUT_COMMENT=1
val SCE_LOUT_NUMBER=2
val SCE_LOUT_WORD=3
val SCE_LOUT_WORD2=4
val SCE_LOUT_WORD3=5
val SCE_LOUT_WORD4=6
val SCE_LOUT_STRING=7
val SCE_LOUT_OPERATOR=8
val SCE_LOUT_IDENTIFIER=9
val SCE_LOUT_STRINGEOL=10
# Lexical states for SCLEX_ESCRIPT
lex ESCRIPT=SCLEX_ESCRIPT SCE_ESCRIPT_
val SCE_ESCRIPT_DEFAULT=0
val SCE_ESCRIPT_COMMENT=1
val SCE_ESCRIPT_COMMENTLINE=2
val SCE_ESCRIPT_COMMENTDOC=3
val SCE_ESCRIPT_NUMBER=4
val SCE_ESCRIPT_WORD=5
val SCE_ESCRIPT_STRING=6
val SCE_ESCRIPT_OPERATOR=7
val SCE_ESCRIPT_IDENTIFIER=8
val SCE_ESCRIPT_BRACE=9
val SCE_ESCRIPT_WORD2=10
val SCE_ESCRIPT_WORD3=11
# Lexical states for SCLEX_PS
lex PS=SCLEX_PS SCE_PS_
val SCE_PS_DEFAULT=0
val SCE_PS_COMMENT=1
val SCE_PS_DSC_COMMENT=2
val SCE_PS_DSC_VALUE=3
val SCE_PS_NUMBER=4
val SCE_PS_NAME=5
val SCE_PS_KEYWORD=6
val SCE_PS_LITERAL=7
val SCE_PS_IMMEVAL=8
val SCE_PS_PAREN_ARRAY=9
val SCE_PS_PAREN_DICT=10
val SCE_PS_PAREN_PROC=11
val SCE_PS_TEXT=12
val SCE_PS_HEXSTRING=13
val SCE_PS_BASE85STRING=14
val SCE_PS_BADSTRINGCHAR=15
# Lexical states for SCLEX_NSIS
lex NSIS=SCLEX_NSIS SCE_NSIS_
val SCE_NSIS_DEFAULT=0
val SCE_NSIS_COMMENT=1
val SCE_NSIS_STRINGDQ=2
val SCE_NSIS_STRINGLQ=3
val SCE_NSIS_STRINGRQ=4
val SCE_NSIS_FUNCTION=5
val SCE_NSIS_VARIABLE=6
val SCE_NSIS_LABEL=7
val SCE_NSIS_USERDEFINED=8
val SCE_NSIS_SECTIONDEF=9
val SCE_NSIS_SUBSECTIONDEF=10
val SCE_NSIS_IFDEFINEDEF=11
val SCE_NSIS_MACRODEF=12
val SCE_NSIS_STRINGVAR=13
val SCE_NSIS_NUMBER=14
val SCE_NSIS_SECTIONGROUP=15
val SCE_NSIS_PAGEEX=16
val SCE_NSIS_FUNCTIONDEF=17
val SCE_NSIS_COMMENTBOX=18
# Lexical states for SCLEX_MMIXAL
lex MMIXAL=SCLEX_MMIXAL SCE_MMIXAL_
val SCE_MMIXAL_LEADWS=0
val SCE_MMIXAL_COMMENT=1
val SCE_MMIXAL_LABEL=2
val SCE_MMIXAL_OPCODE=3
val SCE_MMIXAL_OPCODE_PRE=4
val SCE_MMIXAL_OPCODE_VALID=5
val SCE_MMIXAL_OPCODE_UNKNOWN=6
val SCE_MMIXAL_OPCODE_POST=7
val SCE_MMIXAL_OPERANDS=8
val SCE_MMIXAL_NUMBER=9
val SCE_MMIXAL_REF=10
val SCE_MMIXAL_CHAR=11
val SCE_MMIXAL_STRING=12
val SCE_MMIXAL_REGISTER=13
val SCE_MMIXAL_HEX=14
val SCE_MMIXAL_OPERATOR=15
val SCE_MMIXAL_SYMBOL=16
val SCE_MMIXAL_INCLUDE=17
# Lexical states for SCLEX_CLW
lex Clarion=SCLEX_CLW SCE_CLW_
val SCE_CLW_DEFAULT=0
val SCE_CLW_LABEL=1
val SCE_CLW_COMMENT=2
val SCE_CLW_STRING=3
val SCE_CLW_USER_IDENTIFIER=4
val SCE_CLW_INTEGER_CONSTANT=5
val SCE_CLW_REAL_CONSTANT=6
val SCE_CLW_PICTURE_STRING=7
val SCE_CLW_KEYWORD=8
val SCE_CLW_COMPILER_DIRECTIVE=9
val SCE_CLW_RUNTIME_EXPRESSIONS=10
val SCE_CLW_BUILTIN_PROCEDURES_FUNCTION=11
val SCE_CLW_STRUCTURE_DATA_TYPE=12
val SCE_CLW_ATTRIBUTE=13
val SCE_CLW_STANDARD_EQUATE=14
val SCE_CLW_ERROR=15
val SCE_CLW_DEPRECATED=16
# Lexical states for SCLEX_LOT
lex LOT=SCLEX_LOT SCE_LOT_
val SCE_LOT_DEFAULT=0
val SCE_LOT_HEADER=1
val SCE_LOT_BREAK=2
val SCE_LOT_SET=3
val SCE_LOT_PASS=4
val SCE_LOT_FAIL=5
val SCE_LOT_ABORT=6
# Lexical states for SCLEX_YAML
lex YAML=SCLEX_YAML SCE_YAML_
val SCE_YAML_DEFAULT=0
val SCE_YAML_COMMENT=1
val SCE_YAML_IDENTIFIER=2
val SCE_YAML_KEYWORD=3
val SCE_YAML_NUMBER=4
val SCE_YAML_REFERENCE=5
val SCE_YAML_DOCUMENT=6
val SCE_YAML_TEXT=7
val SCE_YAML_ERROR=8
val SCE_YAML_OPERATOR=9
# Lexical states for SCLEX_TEX
lex TeX=SCLEX_TEX SCE_TEX_
val SCE_TEX_DEFAULT=0
val SCE_TEX_SPECIAL=1
val SCE_TEX_GROUP=2
val SCE_TEX_SYMBOL=3
val SCE_TEX_COMMAND=4
val SCE_TEX_TEXT=5
lex Metapost=SCLEX_METAPOST SCE_METAPOST_
val SCE_METAPOST_DEFAULT=0
val SCE_METAPOST_SPECIAL=1
val SCE_METAPOST_GROUP=2
val SCE_METAPOST_SYMBOL=3
val SCE_METAPOST_COMMAND=4
val SCE_METAPOST_TEXT=5
val SCE_METAPOST_EXTRA=6
# Lexical states for SCLEX_ERLANG
lex Erlang=SCLEX_ERLANG SCE_ERLANG_
val SCE_ERLANG_DEFAULT=0
val SCE_ERLANG_COMMENT=1
val SCE_ERLANG_VARIABLE=2
val SCE_ERLANG_NUMBER=3
val SCE_ERLANG_KEYWORD=4
val SCE_ERLANG_STRING=5
val SCE_ERLANG_OPERATOR=6
val SCE_ERLANG_ATOM=7
val SCE_ERLANG_FUNCTION_NAME=8
val SCE_ERLANG_CHARACTER=9
val SCE_ERLANG_MACRO=10
val SCE_ERLANG_RECORD=11
val SCE_ERLANG_PREPROC=12
val SCE_ERLANG_NODE_NAME=13
val SCE_ERLANG_COMMENT_FUNCTION=14
val SCE_ERLANG_COMMENT_MODULE=15
val SCE_ERLANG_COMMENT_DOC=16
val SCE_ERLANG_COMMENT_DOC_MACRO=17
val SCE_ERLANG_ATOM_QUOTED=18
val SCE_ERLANG_MACRO_QUOTED=19
val SCE_ERLANG_RECORD_QUOTED=20
val SCE_ERLANG_NODE_NAME_QUOTED=21
val SCE_ERLANG_BIFS=22
val SCE_ERLANG_MODULES=23
val SCE_ERLANG_MODULES_ATT=24
val SCE_ERLANG_UNKNOWN=31
# Lexical states for SCLEX_OCTAVE are identical to MatLab
lex Octave=SCLEX_OCTAVE SCE_MATLAB_
# Lexical states for SCLEX_JULIA
lex Julia=SCLEX_JULIA SCE_JULIA_
val SCE_JULIA_DEFAULT=0
val SCE_JULIA_COMMENT=1
val SCE_JULIA_NUMBER=2
val SCE_JULIA_KEYWORD1=3
val SCE_JULIA_KEYWORD2=4
val SCE_JULIA_KEYWORD3=5
val SCE_JULIA_CHAR=6
val SCE_JULIA_OPERATOR=7
val SCE_JULIA_BRACKET=8
val SCE_JULIA_IDENTIFIER=9
val SCE_JULIA_STRING=10
val SCE_JULIA_SYMBOL=11
val SCE_JULIA_MACRO=12
val SCE_JULIA_STRINGINTERP=13
val SCE_JULIA_DOCSTRING=14
val SCE_JULIA_STRINGLITERAL=15
val SCE_JULIA_COMMAND=16
val SCE_JULIA_COMMANDLITERAL=17
val SCE_JULIA_TYPEANNOT=18
val SCE_JULIA_LEXERROR=19
val SCE_JULIA_KEYWORD4=20
val SCE_JULIA_TYPEOPERATOR=21
# Lexical states for SCLEX_MSSQL
lex MSSQL=SCLEX_MSSQL SCE_MSSQL_
val SCE_MSSQL_DEFAULT=0
val SCE_MSSQL_COMMENT=1
val SCE_MSSQL_LINE_COMMENT=2
val SCE_MSSQL_NUMBER=3
val SCE_MSSQL_STRING=4
val SCE_MSSQL_OPERATOR=5
val SCE_MSSQL_IDENTIFIER=6
val SCE_MSSQL_VARIABLE=7
val SCE_MSSQL_COLUMN_NAME=8
val SCE_MSSQL_STATEMENT=9
val SCE_MSSQL_DATATYPE=10
val SCE_MSSQL_SYSTABLE=11
val SCE_MSSQL_GLOBAL_VARIABLE=12
val SCE_MSSQL_FUNCTION=13
val SCE_MSSQL_STORED_PROCEDURE=14
val SCE_MSSQL_DEFAULT_PREF_DATATYPE=15
val SCE_MSSQL_COLUMN_NAME_2=16
# Lexical states for SCLEX_VERILOG
lex Verilog=SCLEX_VERILOG SCE_V_
val SCE_V_DEFAULT=0
val SCE_V_COMMENT=1
val SCE_V_COMMENTLINE=2
val SCE_V_COMMENTLINEBANG=3
val SCE_V_NUMBER=4
val SCE_V_WORD=5
val SCE_V_STRING=6
val SCE_V_WORD2=7
val SCE_V_WORD3=8
val SCE_V_PREPROCESSOR=9
val SCE_V_OPERATOR=10
val SCE_V_IDENTIFIER=11
val SCE_V_STRINGEOL=12
val SCE_V_USER=19
val SCE_V_COMMENT_WORD=20
val SCE_V_INPUT=21
val SCE_V_OUTPUT=22
val SCE_V_INOUT=23
val SCE_V_PORT_CONNECT=24
# Lexical states for SCLEX_KIX
lex Kix=SCLEX_KIX SCE_KIX_
val SCE_KIX_DEFAULT=0
val SCE_KIX_COMMENT=1
val SCE_KIX_STRING1=2
val SCE_KIX_STRING2=3
val SCE_KIX_NUMBER=4
val SCE_KIX_VAR=5
val SCE_KIX_MACRO=6
val SCE_KIX_KEYWORD=7
val SCE_KIX_FUNCTIONS=8
val SCE_KIX_OPERATOR=9
val SCE_KIX_COMMENTSTREAM=10
val SCE_KIX_IDENTIFIER=31
# Lexical states for SCLEX_GUI4CLI
lex Gui4Cli=SCLEX_GUI4CLI SCE_GC_
val SCE_GC_DEFAULT=0
val SCE_GC_COMMENTLINE=1
val SCE_GC_COMMENTBLOCK=2
val SCE_GC_GLOBAL=3
val SCE_GC_EVENT=4
val SCE_GC_ATTRIBUTE=5
val SCE_GC_CONTROL=6
val SCE_GC_COMMAND=7
val SCE_GC_STRING=8
val SCE_GC_OPERATOR=9
# Lexical states for SCLEX_SPECMAN
lex Specman=SCLEX_SPECMAN SCE_SN_
val SCE_SN_DEFAULT=0
val SCE_SN_CODE=1
val SCE_SN_COMMENTLINE=2
val SCE_SN_COMMENTLINEBANG=3
val SCE_SN_NUMBER=4
val SCE_SN_WORD=5
val SCE_SN_STRING=6
val SCE_SN_WORD2=7
val SCE_SN_WORD3=8
val SCE_SN_PREPROCESSOR=9
val SCE_SN_OPERATOR=10
val SCE_SN_IDENTIFIER=11
val SCE_SN_STRINGEOL=12
val SCE_SN_REGEXTAG=13
val SCE_SN_SIGNAL=14
val SCE_SN_USER=19
# Lexical states for SCLEX_AU3
lex Au3=SCLEX_AU3 SCE_AU3_
val SCE_AU3_DEFAULT=0
val SCE_AU3_COMMENT=1
val SCE_AU3_COMMENTBLOCK=2
val SCE_AU3_NUMBER=3
val SCE_AU3_FUNCTION=4
val SCE_AU3_KEYWORD=5
val SCE_AU3_MACRO=6
val SCE_AU3_STRING=7
val SCE_AU3_OPERATOR=8
val SCE_AU3_VARIABLE=9
val SCE_AU3_SENT=10
val SCE_AU3_PREPROCESSOR=11
val SCE_AU3_SPECIAL=12
val SCE_AU3_EXPAND=13
val SCE_AU3_COMOBJ=14
val SCE_AU3_UDF=15
# Lexical states for SCLEX_APDL
lex APDL=SCLEX_APDL SCE_APDL_
val SCE_APDL_DEFAULT=0
val SCE_APDL_COMMENT=1
val SCE_APDL_COMMENTBLOCK=2
val SCE_APDL_NUMBER=3
val SCE_APDL_STRING=4
val SCE_APDL_OPERATOR=5
val SCE_APDL_WORD=6
val SCE_APDL_PROCESSOR=7
val SCE_APDL_COMMAND=8
val SCE_APDL_SLASHCOMMAND=9
val SCE_APDL_STARCOMMAND=10
val SCE_APDL_ARGUMENT=11
val SCE_APDL_FUNCTION=12
# Lexical states for SCLEX_BASH
lex Bash=SCLEX_BASH SCE_SH_
val SCE_SH_DEFAULT=0
val SCE_SH_ERROR=1
val SCE_SH_COMMENTLINE=2
val SCE_SH_NUMBER=3
val SCE_SH_WORD=4
val SCE_SH_STRING=5
val SCE_SH_CHARACTER=6
val SCE_SH_OPERATOR=7
val SCE_SH_IDENTIFIER=8
val SCE_SH_SCALAR=9
val SCE_SH_PARAM=10
val SCE_SH_BACKTICKS=11
val SCE_SH_HERE_DELIM=12
val SCE_SH_HERE_Q=13
# Lexical states for SCLEX_ASN1
lex Asn1=SCLEX_ASN1 SCE_ASN1_
val SCE_ASN1_DEFAULT=0
val SCE_ASN1_COMMENT=1
val SCE_ASN1_IDENTIFIER=2
val SCE_ASN1_STRING=3
val SCE_ASN1_OID=4
val SCE_ASN1_SCALAR=5
val SCE_ASN1_KEYWORD=6
val SCE_ASN1_ATTRIBUTE=7
val SCE_ASN1_DESCRIPTOR=8
val SCE_ASN1_TYPE=9
val SCE_ASN1_OPERATOR=10
# Lexical states for SCLEX_VHDL
lex VHDL=SCLEX_VHDL SCE_VHDL_
val SCE_VHDL_DEFAULT=0
val SCE_VHDL_COMMENT=1
val SCE_VHDL_COMMENTLINEBANG=2
val SCE_VHDL_NUMBER=3
val SCE_VHDL_STRING=4
val SCE_VHDL_OPERATOR=5
val SCE_VHDL_IDENTIFIER=6
val SCE_VHDL_STRINGEOL=7
val SCE_VHDL_KEYWORD=8
val SCE_VHDL_STDOPERATOR=9
val SCE_VHDL_ATTRIBUTE=10
val SCE_VHDL_STDFUNCTION=11
val SCE_VHDL_STDPACKAGE=12
val SCE_VHDL_STDTYPE=13
val SCE_VHDL_USERWORD=14
val SCE_VHDL_BLOCK_COMMENT=15
# Lexical states for SCLEX_CAML
lex Caml=SCLEX_CAML SCE_CAML_
val SCE_CAML_DEFAULT=0
val SCE_CAML_IDENTIFIER=1
val SCE_CAML_TAGNAME=2
val SCE_CAML_KEYWORD=3
val SCE_CAML_KEYWORD2=4
val SCE_CAML_KEYWORD3=5
val SCE_CAML_LINENUM=6
val SCE_CAML_OPERATOR=7
val SCE_CAML_NUMBER=8
val SCE_CAML_CHAR=9
val SCE_CAML_WHITE=10
val SCE_CAML_STRING=11
val SCE_CAML_COMMENT=12
val SCE_CAML_COMMENT1=13
val SCE_CAML_COMMENT2=14
val SCE_CAML_COMMENT3=15
# Lexical states for SCLEX_HASKELL
lex Haskell=SCLEX_HASKELL SCE_HA_
val SCE_HA_DEFAULT=0
val SCE_HA_IDENTIFIER=1
val SCE_HA_KEYWORD=2
val SCE_HA_NUMBER=3
val SCE_HA_STRING=4
val SCE_HA_CHARACTER=5
val SCE_HA_CLASS=6
val SCE_HA_MODULE=7
val SCE_HA_CAPITAL=8
val SCE_HA_DATA=9
val SCE_HA_IMPORT=10
val SCE_HA_OPERATOR=11
val SCE_HA_INSTANCE=12
val SCE_HA_COMMENTLINE=13
val SCE_HA_COMMENTBLOCK=14
val SCE_HA_COMMENTBLOCK2=15
val SCE_HA_COMMENTBLOCK3=16
val SCE_HA_PRAGMA=17
val SCE_HA_PREPROCESSOR=18
val SCE_HA_STRINGEOL=19
val SCE_HA_RESERVED_OPERATOR=20
val SCE_HA_LITERATE_COMMENT=21
val SCE_HA_LITERATE_CODEDELIM=22
# Lexical states of SCLEX_TADS3
lex TADS3=SCLEX_TADS3 SCE_T3_
val SCE_T3_DEFAULT=0
val SCE_T3_X_DEFAULT=1
val SCE_T3_PREPROCESSOR=2
val SCE_T3_BLOCK_COMMENT=3
val SCE_T3_LINE_COMMENT=4
val SCE_T3_OPERATOR=5
val SCE_T3_KEYWORD=6
val SCE_T3_NUMBER=7
val SCE_T3_IDENTIFIER=8
val SCE_T3_S_STRING=9
val SCE_T3_D_STRING=10
val SCE_T3_X_STRING=11
val SCE_T3_LIB_DIRECTIVE=12
val SCE_T3_MSG_PARAM=13
val SCE_T3_HTML_TAG=14
val SCE_T3_HTML_DEFAULT=15
val SCE_T3_HTML_STRING=16
val SCE_T3_USER1=17
val SCE_T3_USER2=18
val SCE_T3_USER3=19
val SCE_T3_BRACE=20
# Lexical states for SCLEX_REBOL
lex Rebol=SCLEX_REBOL SCE_REBOL_
val SCE_REBOL_DEFAULT=0
val SCE_REBOL_COMMENTLINE=1
val SCE_REBOL_COMMENTBLOCK=2
val SCE_REBOL_PREFACE=3
val SCE_REBOL_OPERATOR=4
val SCE_REBOL_CHARACTER=5
val SCE_REBOL_QUOTEDSTRING=6
val SCE_REBOL_BRACEDSTRING=7
val SCE_REBOL_NUMBER=8
val SCE_REBOL_PAIR=9
val SCE_REBOL_TUPLE=10
val SCE_REBOL_BINARY=11
val SCE_REBOL_MONEY=12
val SCE_REBOL_ISSUE=13
val SCE_REBOL_TAG=14
val SCE_REBOL_FILE=15
val SCE_REBOL_EMAIL=16
val SCE_REBOL_URL=17
val SCE_REBOL_DATE=18
val SCE_REBOL_TIME=19
val SCE_REBOL_IDENTIFIER=20
val SCE_REBOL_WORD=21
val SCE_REBOL_WORD2=22
val SCE_REBOL_WORD3=23
val SCE_REBOL_WORD4=24
val SCE_REBOL_WORD5=25
val SCE_REBOL_WORD6=26
val SCE_REBOL_WORD7=27
val SCE_REBOL_WORD8=28
# Lexical states for SCLEX_SQL
lex SQL=SCLEX_SQL SCE_SQL_
val SCE_SQL_DEFAULT=0
val SCE_SQL_COMMENT=1
val SCE_SQL_COMMENTLINE=2
val SCE_SQL_COMMENTDOC=3
val SCE_SQL_NUMBER=4
val SCE_SQL_WORD=5
val SCE_SQL_STRING=6
val SCE_SQL_CHARACTER=7
val SCE_SQL_SQLPLUS=8
val SCE_SQL_SQLPLUS_PROMPT=9
val SCE_SQL_OPERATOR=10
val SCE_SQL_IDENTIFIER=11
val SCE_SQL_SQLPLUS_COMMENT=13
val SCE_SQL_COMMENTLINEDOC=15
val SCE_SQL_WORD2=16
val SCE_SQL_COMMENTDOCKEYWORD=17
val SCE_SQL_COMMENTDOCKEYWORDERROR=18
val SCE_SQL_USER1=19
val SCE_SQL_USER2=20
val SCE_SQL_USER3=21
val SCE_SQL_USER4=22
val SCE_SQL_QUOTEDIDENTIFIER=23
val SCE_SQL_QOPERATOR=24
# Lexical states for SCLEX_SMALLTALK
lex Smalltalk=SCLEX_SMALLTALK SCE_ST_
val SCE_ST_DEFAULT=0
val SCE_ST_STRING=1
val SCE_ST_NUMBER=2
val SCE_ST_COMMENT=3
val SCE_ST_SYMBOL=4
val SCE_ST_BINARY=5
val SCE_ST_BOOL=6
val SCE_ST_SELF=7
val SCE_ST_SUPER=8
val SCE_ST_NIL=9
val SCE_ST_GLOBAL=10
val SCE_ST_RETURN=11
val SCE_ST_SPECIAL=12
val SCE_ST_KWSEND=13
val SCE_ST_ASSIGN=14
val SCE_ST_CHARACTER=15
val SCE_ST_SPEC_SEL=16
# Lexical states for SCLEX_FLAGSHIP (clipper)
lex FlagShip=SCLEX_FLAGSHIP SCE_FS_
val SCE_FS_DEFAULT=0
val SCE_FS_COMMENT=1
val SCE_FS_COMMENTLINE=2
val SCE_FS_COMMENTDOC=3
val SCE_FS_COMMENTLINEDOC=4
val SCE_FS_COMMENTDOCKEYWORD=5
val SCE_FS_COMMENTDOCKEYWORDERROR=6
val SCE_FS_KEYWORD=7
val SCE_FS_KEYWORD2=8
val SCE_FS_KEYWORD3=9
val SCE_FS_KEYWORD4=10
val SCE_FS_NUMBER=11
val SCE_FS_STRING=12
val SCE_FS_PREPROCESSOR=13
val SCE_FS_OPERATOR=14
val SCE_FS_IDENTIFIER=15
val SCE_FS_DATE=16
val SCE_FS_STRINGEOL=17
val SCE_FS_CONSTANT=18
val SCE_FS_WORDOPERATOR=19
val SCE_FS_DISABLEDCODE=20
val SCE_FS_DEFAULT_C=21
val SCE_FS_COMMENTDOC_C=22
val SCE_FS_COMMENTLINEDOC_C=23
val SCE_FS_KEYWORD_C=24
val SCE_FS_KEYWORD2_C=25
val SCE_FS_NUMBER_C=26
val SCE_FS_STRING_C=27
val SCE_FS_PREPROCESSOR_C=28
val SCE_FS_OPERATOR_C=29
val SCE_FS_IDENTIFIER_C=30
val SCE_FS_STRINGEOL_C=31
# Lexical states for SCLEX_CSOUND
lex Csound=SCLEX_CSOUND SCE_CSOUND_
val SCE_CSOUND_DEFAULT=0
val SCE_CSOUND_COMMENT=1
val SCE_CSOUND_NUMBER=2
val SCE_CSOUND_OPERATOR=3
val SCE_CSOUND_INSTR=4
val SCE_CSOUND_IDENTIFIER=5
val SCE_CSOUND_OPCODE=6
val SCE_CSOUND_HEADERSTMT=7
val SCE_CSOUND_USERKEYWORD=8
val SCE_CSOUND_COMMENTBLOCK=9
val SCE_CSOUND_PARAM=10
val SCE_CSOUND_ARATE_VAR=11
val SCE_CSOUND_KRATE_VAR=12
val SCE_CSOUND_IRATE_VAR=13
val SCE_CSOUND_GLOBAL_VAR=14
val SCE_CSOUND_STRINGEOL=15
# Lexical states for SCLEX_INNOSETUP
lex Inno=SCLEX_INNOSETUP SCE_INNO_
val SCE_INNO_DEFAULT=0
val SCE_INNO_COMMENT=1
val SCE_INNO_KEYWORD=2
val SCE_INNO_PARAMETER=3
val SCE_INNO_SECTION=4
val SCE_INNO_PREPROC=5
val SCE_INNO_INLINE_EXPANSION=6
val SCE_INNO_COMMENT_PASCAL=7
val SCE_INNO_KEYWORD_PASCAL=8
val SCE_INNO_KEYWORD_USER=9
val SCE_INNO_STRING_DOUBLE=10
val SCE_INNO_STRING_SINGLE=11
val SCE_INNO_IDENTIFIER=12
# Lexical states for SCLEX_OPAL
lex Opal=SCLEX_OPAL SCE_OPAL_
val SCE_OPAL_SPACE=0
val SCE_OPAL_COMMENT_BLOCK=1
val SCE_OPAL_COMMENT_LINE=2
val SCE_OPAL_INTEGER=3
val SCE_OPAL_KEYWORD=4
val SCE_OPAL_SORT=5
val SCE_OPAL_STRING=6
val SCE_OPAL_PAR=7
val SCE_OPAL_BOOL_CONST=8
val SCE_OPAL_DEFAULT=32
# Lexical states for SCLEX_SPICE
lex Spice=SCLEX_SPICE SCE_SPICE_
val SCE_SPICE_DEFAULT=0
val SCE_SPICE_IDENTIFIER=1
val SCE_SPICE_KEYWORD=2
val SCE_SPICE_KEYWORD2=3
val SCE_SPICE_KEYWORD3=4
val SCE_SPICE_NUMBER=5
val SCE_SPICE_DELIMITER=6
val SCE_SPICE_VALUE=7
val SCE_SPICE_COMMENTLINE=8
# Lexical states for SCLEX_CMAKE
lex CMAKE=SCLEX_CMAKE SCE_CMAKE_
val SCE_CMAKE_DEFAULT=0
val SCE_CMAKE_COMMENT=1
val SCE_CMAKE_STRINGDQ=2
val SCE_CMAKE_STRINGLQ=3
val SCE_CMAKE_STRINGRQ=4
val SCE_CMAKE_COMMANDS=5
val SCE_CMAKE_PARAMETERS=6
val SCE_CMAKE_VARIABLE=7
val SCE_CMAKE_USERDEFINED=8
val SCE_CMAKE_WHILEDEF=9
val SCE_CMAKE_FOREACHDEF=10
val SCE_CMAKE_IFDEFINEDEF=11
val SCE_CMAKE_MACRODEF=12
val SCE_CMAKE_STRINGVAR=13
val SCE_CMAKE_NUMBER=14
# Lexical states for SCLEX_GAP
lex Gap=SCLEX_GAP SCE_GAP_
val SCE_GAP_DEFAULT=0
val SCE_GAP_IDENTIFIER=1
val SCE_GAP_KEYWORD=2
val SCE_GAP_KEYWORD2=3
val SCE_GAP_KEYWORD3=4
val SCE_GAP_KEYWORD4=5
val SCE_GAP_STRING=6
val SCE_GAP_CHAR=7
val SCE_GAP_OPERATOR=8
val SCE_GAP_COMMENT=9
val SCE_GAP_NUMBER=10
val SCE_GAP_STRINGEOL=11
# Lexical state for SCLEX_PLM
lex PLM=SCLEX_PLM SCE_PLM_
val SCE_PLM_DEFAULT=0
val SCE_PLM_COMMENT=1
val SCE_PLM_STRING=2
val SCE_PLM_NUMBER=3
val SCE_PLM_IDENTIFIER=4
val SCE_PLM_OPERATOR=5
val SCE_PLM_CONTROL=6
val SCE_PLM_KEYWORD=7
# Lexical state for SCLEX_PROGRESS
lex Progress=SCLEX_PROGRESS SCE_ABL_
val SCE_ABL_DEFAULT=0
val SCE_ABL_NUMBER=1
val SCE_ABL_WORD=2
val SCE_ABL_STRING=3
val SCE_ABL_CHARACTER=4
val SCE_ABL_PREPROCESSOR=5
val SCE_ABL_OPERATOR=6
val SCE_ABL_IDENTIFIER=7
val SCE_ABL_BLOCK=8
val SCE_ABL_END=9
val SCE_ABL_COMMENT=10
val SCE_ABL_TASKMARKER=11
val SCE_ABL_LINECOMMENT=12
# Lexical states for SCLEX_ABAQUS
lex ABAQUS=SCLEX_ABAQUS SCE_ABAQUS_
val SCE_ABAQUS_DEFAULT=0
val SCE_ABAQUS_COMMENT=1
val SCE_ABAQUS_COMMENTBLOCK=2
val SCE_ABAQUS_NUMBER=3
val SCE_ABAQUS_STRING=4
val SCE_ABAQUS_OPERATOR=5
val SCE_ABAQUS_WORD=6
val SCE_ABAQUS_PROCESSOR=7
val SCE_ABAQUS_COMMAND=8
val SCE_ABAQUS_SLASHCOMMAND=9
val SCE_ABAQUS_STARCOMMAND=10
val SCE_ABAQUS_ARGUMENT=11
val SCE_ABAQUS_FUNCTION=12
# Lexical states for SCLEX_ASYMPTOTE
lex Asymptote=SCLEX_ASYMPTOTE SCE_ASY_
val SCE_ASY_DEFAULT=0
val SCE_ASY_COMMENT=1
val SCE_ASY_COMMENTLINE=2
val SCE_ASY_NUMBER=3
val SCE_ASY_WORD=4
val SCE_ASY_STRING=5
val SCE_ASY_CHARACTER=6
val SCE_ASY_OPERATOR=7
val SCE_ASY_IDENTIFIER=8
val SCE_ASY_STRINGEOL=9
val SCE_ASY_COMMENTLINEDOC=10
val SCE_ASY_WORD2=11
# Lexical states for SCLEX_R
lex R=SCLEX_R SCE_R_
val SCE_R_DEFAULT=0
val SCE_R_COMMENT=1
val SCE_R_KWORD=2
val SCE_R_BASEKWORD=3
val SCE_R_OTHERKWORD=4
val SCE_R_NUMBER=5
val SCE_R_STRING=6
val SCE_R_STRING2=7
val SCE_R_OPERATOR=8
val SCE_R_IDENTIFIER=9
val SCE_R_INFIX=10
val SCE_R_INFIXEOL=11
val SCE_R_BACKTICKS=12
val SCE_R_RAWSTRING=13
val SCE_R_RAWSTRING2=14
val SCE_R_ESCAPESEQUENCE=15
# Lexical state for SCLEX_MAGIK
lex MagikSF=SCLEX_MAGIK SCE_MAGIK_
val SCE_MAGIK_DEFAULT=0
val SCE_MAGIK_COMMENT=1
val SCE_MAGIK_HYPER_COMMENT=16
val SCE_MAGIK_STRING=2
val SCE_MAGIK_CHARACTER=3
val SCE_MAGIK_NUMBER=4
val SCE_MAGIK_IDENTIFIER=5
val SCE_MAGIK_OPERATOR=6
val SCE_MAGIK_FLOW=7
val SCE_MAGIK_CONTAINER=8
val SCE_MAGIK_BRACKET_BLOCK=9
val SCE_MAGIK_BRACE_BLOCK=10
val SCE_MAGIK_SQBRACKET_BLOCK=11
val SCE_MAGIK_UNKNOWN_KEYWORD=12
val SCE_MAGIK_KEYWORD=13
val SCE_MAGIK_PRAGMA=14
val SCE_MAGIK_SYMBOL=15
# Lexical state for SCLEX_POWERSHELL
lex PowerShell=SCLEX_POWERSHELL SCE_POWERSHELL_
val SCE_POWERSHELL_DEFAULT=0
val SCE_POWERSHELL_COMMENT=1
val SCE_POWERSHELL_STRING=2
val SCE_POWERSHELL_CHARACTER=3
val SCE_POWERSHELL_NUMBER=4
val SCE_POWERSHELL_VARIABLE=5
val SCE_POWERSHELL_OPERATOR=6
val SCE_POWERSHELL_IDENTIFIER=7
val SCE_POWERSHELL_KEYWORD=8
val SCE_POWERSHELL_CMDLET=9
val SCE_POWERSHELL_ALIAS=10
val SCE_POWERSHELL_FUNCTION=11
val SCE_POWERSHELL_USER1=12
val SCE_POWERSHELL_COMMENTSTREAM=13
val SCE_POWERSHELL_HERE_STRING=14
val SCE_POWERSHELL_HERE_CHARACTER=15
val SCE_POWERSHELL_COMMENTDOCKEYWORD=16
# Lexical state for SCLEX_MYSQL
lex MySQL=SCLEX_MYSQL SCE_MYSQL_
val SCE_MYSQL_DEFAULT=0
val SCE_MYSQL_COMMENT=1
val SCE_MYSQL_COMMENTLINE=2
val SCE_MYSQL_VARIABLE=3
val SCE_MYSQL_SYSTEMVARIABLE=4
val SCE_MYSQL_KNOWNSYSTEMVARIABLE=5
val SCE_MYSQL_NUMBER=6
val SCE_MYSQL_MAJORKEYWORD=7
val SCE_MYSQL_KEYWORD=8
val SCE_MYSQL_DATABASEOBJECT=9
val SCE_MYSQL_PROCEDUREKEYWORD=10
val SCE_MYSQL_STRING=11
val SCE_MYSQL_SQSTRING=12
val SCE_MYSQL_DQSTRING=13
val SCE_MYSQL_OPERATOR=14
val SCE_MYSQL_FUNCTION=15
val SCE_MYSQL_IDENTIFIER=16
val SCE_MYSQL_QUOTEDIDENTIFIER=17
val SCE_MYSQL_USER1=18
val SCE_MYSQL_USER2=19
val SCE_MYSQL_USER3=20
val SCE_MYSQL_HIDDENCOMMAND=21
val SCE_MYSQL_PLACEHOLDER=22
# Lexical state for SCLEX_PO
lex Po=SCLEX_PO SCE_PO_
val SCE_PO_DEFAULT=0
val SCE_PO_COMMENT=1
val SCE_PO_MSGID=2
val SCE_PO_MSGID_TEXT=3
val SCE_PO_MSGSTR=4
val SCE_PO_MSGSTR_TEXT=5
val SCE_PO_MSGCTXT=6
val SCE_PO_MSGCTXT_TEXT=7
val SCE_PO_FUZZY=8
val SCE_PO_PROGRAMMER_COMMENT=9
val SCE_PO_REFERENCE=10
val SCE_PO_FLAGS=11
val SCE_PO_MSGID_TEXT_EOL=12
val SCE_PO_MSGSTR_TEXT_EOL=13
val SCE_PO_MSGCTXT_TEXT_EOL=14
val SCE_PO_ERROR=15
# Lexical states for SCLEX_PASCAL
lex Pascal=SCLEX_PASCAL SCE_PAS_
val SCE_PAS_DEFAULT=0
val SCE_PAS_IDENTIFIER=1
val SCE_PAS_COMMENT=2
val SCE_PAS_COMMENT2=3
val SCE_PAS_COMMENTLINE=4
val SCE_PAS_PREPROCESSOR=5
val SCE_PAS_PREPROCESSOR2=6
val SCE_PAS_NUMBER=7
val SCE_PAS_HEXNUMBER=8
val SCE_PAS_WORD=9
val SCE_PAS_STRING=10
val SCE_PAS_STRINGEOL=11
val SCE_PAS_CHARACTER=12
val SCE_PAS_OPERATOR=13
val SCE_PAS_ASM=14
# Lexical state for SCLEX_SORCUS
lex SORCUS=SCLEX_SORCUS SCE_SORCUS_
val SCE_SORCUS_DEFAULT=0
val SCE_SORCUS_COMMAND=1
val SCE_SORCUS_PARAMETER=2
val SCE_SORCUS_COMMENTLINE=3
val SCE_SORCUS_STRING=4
val SCE_SORCUS_STRINGEOL=5
val SCE_SORCUS_IDENTIFIER=6
val SCE_SORCUS_OPERATOR=7
val SCE_SORCUS_NUMBER=8
val SCE_SORCUS_CONSTANT=9
# Lexical state for SCLEX_POWERPRO
lex PowerPro=SCLEX_POWERPRO SCE_POWERPRO_
val SCE_POWERPRO_DEFAULT=0
val SCE_POWERPRO_COMMENTBLOCK=1
val SCE_POWERPRO_COMMENTLINE=2
val SCE_POWERPRO_NUMBER=3
val SCE_POWERPRO_WORD=4
val SCE_POWERPRO_WORD2=5
val SCE_POWERPRO_WORD3=6
val SCE_POWERPRO_WORD4=7
val SCE_POWERPRO_DOUBLEQUOTEDSTRING=8
val SCE_POWERPRO_SINGLEQUOTEDSTRING=9
val SCE_POWERPRO_LINECONTINUE=10
val SCE_POWERPRO_OPERATOR=11
val SCE_POWERPRO_IDENTIFIER=12
val SCE_POWERPRO_STRINGEOL=13
val SCE_POWERPRO_VERBATIM=14
val SCE_POWERPRO_ALTQUOTE=15
val SCE_POWERPRO_FUNCTION=16
# Lexical states for SCLEX_SML
lex SML=SCLEX_SML SCE_SML_
val SCE_SML_DEFAULT=0
val SCE_SML_IDENTIFIER=1
val SCE_SML_TAGNAME=2
val SCE_SML_KEYWORD=3
val SCE_SML_KEYWORD2=4
val SCE_SML_KEYWORD3=5
val SCE_SML_LINENUM=6
val SCE_SML_OPERATOR=7
val SCE_SML_NUMBER=8
val SCE_SML_CHAR=9
val SCE_SML_STRING=11
val SCE_SML_COMMENT=12
val SCE_SML_COMMENT1=13
val SCE_SML_COMMENT2=14
val SCE_SML_COMMENT3=15
# Lexical state for SCLEX_MARKDOWN
lex Markdown=SCLEX_MARKDOWN SCE_MARKDOWN_
val SCE_MARKDOWN_DEFAULT=0
val SCE_MARKDOWN_LINE_BEGIN=1
val SCE_MARKDOWN_STRONG1=2
val SCE_MARKDOWN_STRONG2=3
val SCE_MARKDOWN_EM1=4
val SCE_MARKDOWN_EM2=5
val SCE_MARKDOWN_HEADER1=6
val SCE_MARKDOWN_HEADER2=7
val SCE_MARKDOWN_HEADER3=8
val SCE_MARKDOWN_HEADER4=9
val SCE_MARKDOWN_HEADER5=10
val SCE_MARKDOWN_HEADER6=11
val SCE_MARKDOWN_PRECHAR=12
val SCE_MARKDOWN_ULIST_ITEM=13
val SCE_MARKDOWN_OLIST_ITEM=14
val SCE_MARKDOWN_BLOCKQUOTE=15
val SCE_MARKDOWN_STRIKEOUT=16
val SCE_MARKDOWN_HRULE=17
val SCE_MARKDOWN_LINK=18
val SCE_MARKDOWN_CODE=19
val SCE_MARKDOWN_CODE2=20
val SCE_MARKDOWN_CODEBK=21
# Lexical state for SCLEX_TXT2TAGS
lex Txt2tags=SCLEX_TXT2TAGS SCE_TXT2TAGS_
val SCE_TXT2TAGS_DEFAULT=0
val SCE_TXT2TAGS_LINE_BEGIN=1
val SCE_TXT2TAGS_STRONG1=2
val SCE_TXT2TAGS_STRONG2=3
val SCE_TXT2TAGS_EM1=4
val SCE_TXT2TAGS_EM2=5
val SCE_TXT2TAGS_HEADER1=6
val SCE_TXT2TAGS_HEADER2=7
val SCE_TXT2TAGS_HEADER3=8
val SCE_TXT2TAGS_HEADER4=9
val SCE_TXT2TAGS_HEADER5=10
val SCE_TXT2TAGS_HEADER6=11
val SCE_TXT2TAGS_PRECHAR=12
val SCE_TXT2TAGS_ULIST_ITEM=13
val SCE_TXT2TAGS_OLIST_ITEM=14
val SCE_TXT2TAGS_BLOCKQUOTE=15
val SCE_TXT2TAGS_STRIKEOUT=16
val SCE_TXT2TAGS_HRULE=17
val SCE_TXT2TAGS_LINK=18
val SCE_TXT2TAGS_CODE=19
val SCE_TXT2TAGS_CODE2=20
val SCE_TXT2TAGS_CODEBK=21
val SCE_TXT2TAGS_COMMENT=22
val SCE_TXT2TAGS_OPTION=23
val SCE_TXT2TAGS_PREPROC=24
val SCE_TXT2TAGS_POSTPROC=25
# Lexical states for SCLEX_A68K
lex A68k=SCLEX_A68K SCE_A68K_
val SCE_A68K_DEFAULT=0
val SCE_A68K_COMMENT=1
val SCE_A68K_NUMBER_DEC=2
val SCE_A68K_NUMBER_BIN=3
val SCE_A68K_NUMBER_HEX=4
val SCE_A68K_STRING1=5
val SCE_A68K_OPERATOR=6
val SCE_A68K_CPUINSTRUCTION=7
val SCE_A68K_EXTINSTRUCTION=8
val SCE_A68K_REGISTER=9
val SCE_A68K_DIRECTIVE=10
val SCE_A68K_MACRO_ARG=11
val SCE_A68K_LABEL=12
val SCE_A68K_STRING2=13
val SCE_A68K_IDENTIFIER=14
val SCE_A68K_MACRO_DECLARATION=15
val SCE_A68K_COMMENT_WORD=16
val SCE_A68K_COMMENT_SPECIAL=17
val SCE_A68K_COMMENT_DOXYGEN=18
# Lexical states for SCLEX_MODULA
lex Modula=SCLEX_MODULA SCE_MODULA_
val SCE_MODULA_DEFAULT=0
val SCE_MODULA_COMMENT=1
val SCE_MODULA_DOXYCOMM=2
val SCE_MODULA_DOXYKEY=3
val SCE_MODULA_KEYWORD=4
val SCE_MODULA_RESERVED=5
val SCE_MODULA_NUMBER=6
val SCE_MODULA_BASENUM=7
val SCE_MODULA_FLOAT=8
val SCE_MODULA_STRING=9
val SCE_MODULA_STRSPEC=10
val SCE_MODULA_CHAR=11
val SCE_MODULA_CHARSPEC=12
val SCE_MODULA_PROC=13
val SCE_MODULA_PRAGMA=14
val SCE_MODULA_PRGKEY=15
val SCE_MODULA_OPERATOR=16
val SCE_MODULA_BADSTR=17
# Lexical states for SCLEX_COFFEESCRIPT
lex CoffeeScript=SCLEX_COFFEESCRIPT SCE_COFFEESCRIPT_
val SCE_COFFEESCRIPT_DEFAULT=0
val SCE_COFFEESCRIPT_COMMENT=1
val SCE_COFFEESCRIPT_COMMENTLINE=2
val SCE_COFFEESCRIPT_COMMENTDOC=3
val SCE_COFFEESCRIPT_NUMBER=4
val SCE_COFFEESCRIPT_WORD=5
val SCE_COFFEESCRIPT_STRING=6
val SCE_COFFEESCRIPT_CHARACTER=7
val SCE_COFFEESCRIPT_UUID=8
val SCE_COFFEESCRIPT_PREPROCESSOR=9
val SCE_COFFEESCRIPT_OPERATOR=10
val SCE_COFFEESCRIPT_IDENTIFIER=11
val SCE_COFFEESCRIPT_STRINGEOL=12
val SCE_COFFEESCRIPT_VERBATIM=13
val SCE_COFFEESCRIPT_REGEX=14
val SCE_COFFEESCRIPT_COMMENTLINEDOC=15
val SCE_COFFEESCRIPT_WORD2=16
val SCE_COFFEESCRIPT_COMMENTDOCKEYWORD=17
val SCE_COFFEESCRIPT_COMMENTDOCKEYWORDERROR=18
val SCE_COFFEESCRIPT_GLOBALCLASS=19
val SCE_COFFEESCRIPT_STRINGRAW=20
val SCE_COFFEESCRIPT_TRIPLEVERBATIM=21
val SCE_COFFEESCRIPT_COMMENTBLOCK=22
val SCE_COFFEESCRIPT_VERBOSE_REGEX=23
val SCE_COFFEESCRIPT_VERBOSE_REGEX_COMMENT=24
val SCE_COFFEESCRIPT_INSTANCEPROPERTY=25
# Lexical states for SCLEX_AVS
lex AVS=SCLEX_AVS SCE_AVS_
val SCE_AVS_DEFAULT=0
val SCE_AVS_COMMENTBLOCK=1
val SCE_AVS_COMMENTBLOCKN=2
val SCE_AVS_COMMENTLINE=3
val SCE_AVS_NUMBER=4
val SCE_AVS_OPERATOR=5
val SCE_AVS_IDENTIFIER=6
val SCE_AVS_STRING=7
val SCE_AVS_TRIPLESTRING=8
val SCE_AVS_KEYWORD=9
val SCE_AVS_FILTER=10
val SCE_AVS_PLUGIN=11
val SCE_AVS_FUNCTION=12
val SCE_AVS_CLIPPROP=13
val SCE_AVS_USERDFN=14
# Lexical states for SCLEX_ECL
lex ECL=SCLEX_ECL SCE_ECL_
val SCE_ECL_DEFAULT=0
val SCE_ECL_COMMENT=1
val SCE_ECL_COMMENTLINE=2
val SCE_ECL_NUMBER=3
val SCE_ECL_STRING=4
val SCE_ECL_WORD0=5
val SCE_ECL_OPERATOR=6
val SCE_ECL_CHARACTER=7
val SCE_ECL_UUID=8
val SCE_ECL_PREPROCESSOR=9
val SCE_ECL_UNKNOWN=10
val SCE_ECL_IDENTIFIER=11
val SCE_ECL_STRINGEOL=12
val SCE_ECL_VERBATIM=13
val SCE_ECL_REGEX=14
val SCE_ECL_COMMENTLINEDOC=15
val SCE_ECL_WORD1=16
val SCE_ECL_COMMENTDOCKEYWORD=17
val SCE_ECL_COMMENTDOCKEYWORDERROR=18
val SCE_ECL_WORD2=19
val SCE_ECL_WORD3=20
val SCE_ECL_WORD4=21
val SCE_ECL_WORD5=22
val SCE_ECL_COMMENTDOC=23
val SCE_ECL_ADDED=24
val SCE_ECL_DELETED=25
val SCE_ECL_CHANGED=26
val SCE_ECL_MOVED=27
# Lexical states for SCLEX_OSCRIPT
lex OScript=SCLEX_OSCRIPT SCE_OSCRIPT_
val SCE_OSCRIPT_DEFAULT=0
val SCE_OSCRIPT_LINE_COMMENT=1
val SCE_OSCRIPT_BLOCK_COMMENT=2
val SCE_OSCRIPT_DOC_COMMENT=3
val SCE_OSCRIPT_PREPROCESSOR=4
val SCE_OSCRIPT_NUMBER=5
val SCE_OSCRIPT_SINGLEQUOTE_STRING=6
val SCE_OSCRIPT_DOUBLEQUOTE_STRING=7
val SCE_OSCRIPT_CONSTANT=8
val SCE_OSCRIPT_IDENTIFIER=9
val SCE_OSCRIPT_GLOBAL=10
val SCE_OSCRIPT_KEYWORD=11
val SCE_OSCRIPT_OPERATOR=12
val SCE_OSCRIPT_LABEL=13
val SCE_OSCRIPT_TYPE=14
val SCE_OSCRIPT_FUNCTION=15
val SCE_OSCRIPT_OBJECT=16
val SCE_OSCRIPT_PROPERTY=17
val SCE_OSCRIPT_METHOD=18
# Lexical states for SCLEX_VISUALPROLOG
lex VisualProlog=SCLEX_VISUALPROLOG SCE_VISUALPROLOG_
val SCE_VISUALPROLOG_DEFAULT=0
val SCE_VISUALPROLOG_KEY_MAJOR=1
val SCE_VISUALPROLOG_KEY_MINOR=2
val SCE_VISUALPROLOG_KEY_DIRECTIVE=3
val SCE_VISUALPROLOG_COMMENT_BLOCK=4
val SCE_VISUALPROLOG_COMMENT_LINE=5
val SCE_VISUALPROLOG_COMMENT_KEY=6
val SCE_VISUALPROLOG_COMMENT_KEY_ERROR=7
val SCE_VISUALPROLOG_IDENTIFIER=8
val SCE_VISUALPROLOG_VARIABLE=9
val SCE_VISUALPROLOG_ANONYMOUS=10
val SCE_VISUALPROLOG_NUMBER=11
val SCE_VISUALPROLOG_OPERATOR=12
val SCE_VISUALPROLOG_UNUSED1=13
val SCE_VISUALPROLOG_UNUSED2=14
val SCE_VISUALPROLOG_UNUSED3=15
val SCE_VISUALPROLOG_STRING_QUOTE=16
val SCE_VISUALPROLOG_STRING_ESCAPE=17
val SCE_VISUALPROLOG_STRING_ESCAPE_ERROR=18
val SCE_VISUALPROLOG_UNUSED4=19
val SCE_VISUALPROLOG_STRING=20
val SCE_VISUALPROLOG_UNUSED5=21
val SCE_VISUALPROLOG_STRING_EOL=22
val SCE_VISUALPROLOG_EMBEDDED=23
val SCE_VISUALPROLOG_PLACEHOLDER=24
# Lexical states for SCLEX_STTXT
lex StructuredText=SCLEX_STTXT SCE_STTXT_
val SCE_STTXT_DEFAULT=0
val SCE_STTXT_COMMENT=1
val SCE_STTXT_COMMENTLINE=2
val SCE_STTXT_KEYWORD=3
val SCE_STTXT_TYPE=4
val SCE_STTXT_FUNCTION=5
val SCE_STTXT_FB=6
val SCE_STTXT_NUMBER=7
val SCE_STTXT_HEXNUMBER=8
val SCE_STTXT_PRAGMA=9
val SCE_STTXT_OPERATOR=10
val SCE_STTXT_CHARACTER=11
val SCE_STTXT_STRING1=12
val SCE_STTXT_STRING2=13
val SCE_STTXT_STRINGEOL=14
val SCE_STTXT_IDENTIFIER=15
val SCE_STTXT_DATETIME=16
val SCE_STTXT_VARS=17
val SCE_STTXT_PRAGMAS=18
# Lexical states for SCLEX_KVIRC
lex KVIrc=SCLEX_KVIRC SCE_KVIRC_
val SCE_KVIRC_DEFAULT=0
val SCE_KVIRC_COMMENT=1
val SCE_KVIRC_COMMENTBLOCK=2
val SCE_KVIRC_STRING=3
val SCE_KVIRC_WORD=4
val SCE_KVIRC_KEYWORD=5
val SCE_KVIRC_FUNCTION_KEYWORD=6
val SCE_KVIRC_FUNCTION=7
val SCE_KVIRC_VARIABLE=8
val SCE_KVIRC_NUMBER=9
val SCE_KVIRC_OPERATOR=10
val SCE_KVIRC_STRING_FUNCTION=11
val SCE_KVIRC_STRING_VARIABLE=12
# Lexical states for SCLEX_RUST
lex Rust=SCLEX_RUST SCE_RUST_
val SCE_RUST_DEFAULT=0
val SCE_RUST_COMMENTBLOCK=1
val SCE_RUST_COMMENTLINE=2
val SCE_RUST_COMMENTBLOCKDOC=3
val SCE_RUST_COMMENTLINEDOC=4
val SCE_RUST_NUMBER=5
val SCE_RUST_WORD=6
val SCE_RUST_WORD2=7
val SCE_RUST_WORD3=8
val SCE_RUST_WORD4=9
val SCE_RUST_WORD5=10
val SCE_RUST_WORD6=11
val SCE_RUST_WORD7=12
val SCE_RUST_STRING=13
val SCE_RUST_STRINGR=14
val SCE_RUST_CHARACTER=15
val SCE_RUST_OPERATOR=16
val SCE_RUST_IDENTIFIER=17
val SCE_RUST_LIFETIME=18
val SCE_RUST_MACRO=19
val SCE_RUST_LEXERROR=20
val SCE_RUST_BYTESTRING=21
val SCE_RUST_BYTESTRINGR=22
val SCE_RUST_BYTECHARACTER=23
val SCE_RUST_CSTRING=24
val SCE_RUST_CSTRINGR=25
# Lexical states for SCLEX_DMAP
lex DMAP=SCLEX_DMAP SCE_DMAP_
val SCE_DMAP_DEFAULT=0
val SCE_DMAP_COMMENT=1
val SCE_DMAP_NUMBER=2
val SCE_DMAP_STRING1=3
val SCE_DMAP_STRING2=4
val SCE_DMAP_STRINGEOL=5
val SCE_DMAP_OPERATOR=6
val SCE_DMAP_IDENTIFIER=7
val SCE_DMAP_WORD=8
val SCE_DMAP_WORD2=9
val SCE_DMAP_WORD3=10
# Lexical states for SCLEX_DMIS
lex DMIS=SCLEX_DMIS SCE_DMIS_
val SCE_DMIS_DEFAULT=0
val SCE_DMIS_COMMENT=1
val SCE_DMIS_STRING=2
val SCE_DMIS_NUMBER=3
val SCE_DMIS_KEYWORD=4
val SCE_DMIS_MAJORWORD=5
val SCE_DMIS_MINORWORD=6
val SCE_DMIS_UNSUPPORTED_MAJOR=7
val SCE_DMIS_UNSUPPORTED_MINOR=8
val SCE_DMIS_LABEL=9
# Lexical states for SCLEX_REGISTRY
lex REG=SCLEX_REGISTRY SCE_REG_
val SCE_REG_DEFAULT=0
val SCE_REG_COMMENT=1
val SCE_REG_VALUENAME=2
val SCE_REG_STRING=3
val SCE_REG_HEXDIGIT=4
val SCE_REG_VALUETYPE=5
val SCE_REG_ADDEDKEY=6
val SCE_REG_DELETEDKEY=7
val SCE_REG_ESCAPED=8
val SCE_REG_KEYPATH_GUID=9
val SCE_REG_STRING_GUID=10
val SCE_REG_PARAMETER=11
val SCE_REG_OPERATOR=12
# Lexical state for SCLEX_BIBTEX
lex BibTeX=SCLEX_BIBTEX SCE_BIBTEX_
val SCE_BIBTEX_DEFAULT=0
val SCE_BIBTEX_ENTRY=1
val SCE_BIBTEX_UNKNOWN_ENTRY=2
val SCE_BIBTEX_KEY=3
val SCE_BIBTEX_PARAMETER=4
val SCE_BIBTEX_VALUE=5
val SCE_BIBTEX_COMMENT=6
# Lexical state for SCLEX_SREC
lex Srec=SCLEX_SREC SCE_HEX_
val SCE_HEX_DEFAULT=0
val SCE_HEX_RECSTART=1
val SCE_HEX_RECTYPE=2
val SCE_HEX_RECTYPE_UNKNOWN=3
val SCE_HEX_BYTECOUNT=4
val SCE_HEX_BYTECOUNT_WRONG=5
val SCE_HEX_NOADDRESS=6
val SCE_HEX_DATAADDRESS=7
val SCE_HEX_RECCOUNT=8
val SCE_HEX_STARTADDRESS=9
val SCE_HEX_ADDRESSFIELD_UNKNOWN=10
val SCE_HEX_EXTENDEDADDRESS=11
val SCE_HEX_DATA_ODD=12
val SCE_HEX_DATA_EVEN=13
val SCE_HEX_DATA_UNKNOWN=14
val SCE_HEX_DATA_EMPTY=15
val SCE_HEX_CHECKSUM=16
val SCE_HEX_CHECKSUM_WRONG=17
val SCE_HEX_GARBAGE=18
# Lexical state for SCLEX_IHEX (shared with Srec)
lex IHex=SCLEX_IHEX SCE_HEX_
# Lexical state for SCLEX_TEHEX (shared with Srec)
lex TEHex=SCLEX_TEHEX SCE_HEX_
# Lexical states for SCLEX_JSON
lex JSON=SCLEX_JSON SCE_JSON_
val SCE_JSON_DEFAULT=0
val SCE_JSON_NUMBER=1
val SCE_JSON_STRING=2
val SCE_JSON_STRINGEOL=3
val SCE_JSON_PROPERTYNAME=4
val SCE_JSON_ESCAPESEQUENCE=5
val SCE_JSON_LINECOMMENT=6
val SCE_JSON_BLOCKCOMMENT=7
val SCE_JSON_OPERATOR=8
val SCE_JSON_URI=9
val SCE_JSON_COMPACTIRI=10
val SCE_JSON_KEYWORD=11
val SCE_JSON_LDKEYWORD=12
val SCE_JSON_ERROR=13
lex EDIFACT=SCLEX_EDIFACT SCE_EDI_
val SCE_EDI_DEFAULT=0
val SCE_EDI_SEGMENTSTART=1
val SCE_EDI_SEGMENTEND=2
val SCE_EDI_SEP_ELEMENT=3
val SCE_EDI_SEP_COMPOSITE=4
val SCE_EDI_SEP_RELEASE=5
val SCE_EDI_UNA=6
val SCE_EDI_UNH=7
val SCE_EDI_BADSEGMENT=8
# Lexical states for SCLEX_STATA
lex STATA=SCLEX_STATA SCE_STATA_
val SCE_STATA_DEFAULT=0
val SCE_STATA_COMMENT=1
val SCE_STATA_COMMENTLINE=2
val SCE_STATA_COMMENTBLOCK=3
val SCE_STATA_NUMBER=4
val SCE_STATA_OPERATOR=5
val SCE_STATA_IDENTIFIER=6
val SCE_STATA_STRING=7
val SCE_STATA_TYPE=8
val SCE_STATA_WORD=9
val SCE_STATA_GLOBAL_MACRO=10
val SCE_STATA_MACRO=11
# Lexical states for SCLEX_SAS
lex SAS=SCLEX_SAS SCE_SAS_
val SCE_SAS_DEFAULT=0
val SCE_SAS_COMMENT=1
val SCE_SAS_COMMENTLINE=2
val SCE_SAS_COMMENTBLOCK=3
val SCE_SAS_NUMBER=4
val SCE_SAS_OPERATOR=5
val SCE_SAS_IDENTIFIER=6
val SCE_SAS_STRING=7
val SCE_SAS_TYPE=8
val SCE_SAS_WORD=9
val SCE_SAS_GLOBAL_MACRO=10
val SCE_SAS_MACRO=11
val SCE_SAS_MACRO_KEYWORD=12
val SCE_SAS_BLOCK_KEYWORD=13
val SCE_SAS_MACRO_FUNCTION=14
val SCE_SAS_STATEMENT=15
# Lexical states for SCLEX_NIM
lex Nim=SCLEX_NIM SCE_NIM_
val SCE_NIM_DEFAULT=0
val SCE_NIM_COMMENT=1
val SCE_NIM_COMMENTDOC=2
val SCE_NIM_COMMENTLINE=3
val SCE_NIM_COMMENTLINEDOC=4
val SCE_NIM_NUMBER=5
val SCE_NIM_STRING=6
val SCE_NIM_CHARACTER=7
val SCE_NIM_WORD=8
val SCE_NIM_TRIPLE=9
val SCE_NIM_TRIPLEDOUBLE=10
val SCE_NIM_BACKTICKS=11
val SCE_NIM_FUNCNAME=12
val SCE_NIM_STRINGEOL=13
val SCE_NIM_NUMERROR=14
val SCE_NIM_OPERATOR=15
val SCE_NIM_IDENTIFIER=16
# Lexical states for SCLEX_CIL
lex CIL=SCLEX_CIL SCE_CIL_
val SCE_CIL_DEFAULT=0
val SCE_CIL_COMMENT=1
val SCE_CIL_COMMENTLINE=2
val SCE_CIL_WORD=3
val SCE_CIL_WORD2=4
val SCE_CIL_WORD3=5
val SCE_CIL_STRING=6
val SCE_CIL_LABEL=7
val SCE_CIL_OPERATOR=8
val SCE_CIL_IDENTIFIER=9
val SCE_CIL_STRINGEOL=10
# Lexical states for SCLEX_X12
lex X12=SCLEX_X12 SCE_X12_
val SCE_X12_DEFAULT=0
val SCE_X12_BAD=1
val SCE_X12_ENVELOPE=2
val SCE_X12_FUNCTIONGROUP=3
val SCE_X12_TRANSACTIONSET=4
val SCE_X12_SEGMENTHEADER=5
val SCE_X12_SEGMENTEND=6
val SCE_X12_SEP_ELEMENT=7
val SCE_X12_SEP_SUBELEMENT=8
# Lexical states for SCLEX_DATAFLEX
lex Dataflex=SCLEX_DATAFLEX SCE_DF_
val SCE_DF_DEFAULT=0
val SCE_DF_IDENTIFIER=1
val SCE_DF_METATAG=2
val SCE_DF_IMAGE=3
val SCE_DF_COMMENTLINE=4
val SCE_DF_PREPROCESSOR=5
val SCE_DF_PREPROCESSOR2=6
val SCE_DF_NUMBER=7
val SCE_DF_HEXNUMBER=8
val SCE_DF_WORD=9
val SCE_DF_STRING=10
val SCE_DF_STRINGEOL=11
val SCE_DF_SCOPEWORD=12
val SCE_DF_OPERATOR=13
val SCE_DF_ICODE=14
# Lexical states for SCLEX_HOLLYWOOD
lex Hollywood=SCLEX_HOLLYWOOD SCE_HOLLYWOOD_
val SCE_HOLLYWOOD_DEFAULT=0
val SCE_HOLLYWOOD_COMMENT=1
val SCE_HOLLYWOOD_COMMENTBLOCK=2
val SCE_HOLLYWOOD_NUMBER=3
val SCE_HOLLYWOOD_KEYWORD=4
val SCE_HOLLYWOOD_STDAPI=5
val SCE_HOLLYWOOD_PLUGINAPI=6
val SCE_HOLLYWOOD_PLUGINMETHOD=7
val SCE_HOLLYWOOD_STRING=8
val SCE_HOLLYWOOD_STRINGBLOCK=9
val SCE_HOLLYWOOD_PREPROCESSOR=10
val SCE_HOLLYWOOD_OPERATOR=11
val SCE_HOLLYWOOD_IDENTIFIER=12
val SCE_HOLLYWOOD_CONSTANT=13
val SCE_HOLLYWOOD_HEXNUMBER=14
# Lexical states for SCLEX_RAKU
lex Raku=SCLEX_RAKU SCE_RAKU_
val SCE_RAKU_DEFAULT=0
val SCE_RAKU_ERROR=1
val SCE_RAKU_COMMENTLINE=2
val SCE_RAKU_COMMENTEMBED=3
val SCE_RAKU_POD=4
val SCE_RAKU_CHARACTER=5
val SCE_RAKU_HEREDOC_Q=6
val SCE_RAKU_HEREDOC_QQ=7
val SCE_RAKU_STRING=8
val SCE_RAKU_STRING_Q=9
val SCE_RAKU_STRING_QQ=10
val SCE_RAKU_STRING_Q_LANG=11
val SCE_RAKU_STRING_VAR=12
val SCE_RAKU_REGEX=13
val SCE_RAKU_REGEX_VAR=14
val SCE_RAKU_ADVERB=15
val SCE_RAKU_NUMBER=16
val SCE_RAKU_PREPROCESSOR=17
val SCE_RAKU_OPERATOR=18
val SCE_RAKU_WORD=19
val SCE_RAKU_FUNCTION=20
val SCE_RAKU_IDENTIFIER=21
val SCE_RAKU_TYPEDEF=22
val SCE_RAKU_MU=23
val SCE_RAKU_POSITIONAL=24
val SCE_RAKU_ASSOCIATIVE=25
val SCE_RAKU_CALLABLE=26
val SCE_RAKU_GRAMMAR=27
val SCE_RAKU_CLASS=28
# Lexical states for SCLEX_FSHARP
lex FSharp=SCLEX_FSHARP SCE_FSHARP_
val SCE_FSHARP_DEFAULT=0
val SCE_FSHARP_KEYWORD=1
val SCE_FSHARP_KEYWORD2=2
val SCE_FSHARP_KEYWORD3=3
val SCE_FSHARP_KEYWORD4=4
val SCE_FSHARP_KEYWORD5=5
val SCE_FSHARP_IDENTIFIER=6
val SCE_FSHARP_QUOT_IDENTIFIER=7
val SCE_FSHARP_COMMENT=8
val SCE_FSHARP_COMMENTLINE=9
val SCE_FSHARP_PREPROCESSOR=10
val SCE_FSHARP_LINENUM=11
val SCE_FSHARP_OPERATOR=12
val SCE_FSHARP_NUMBER=13
val SCE_FSHARP_CHARACTER=14
val SCE_FSHARP_STRING=15
val SCE_FSHARP_VERBATIM=16
val SCE_FSHARP_QUOTATION=17
val SCE_FSHARP_ATTRIBUTE=18
val SCE_FSHARP_FORMAT_SPEC=19
# Lexical states for SCLEX_ASCIIDOC
lex Asciidoc=SCLEX_ASCIIDOC SCE_ASCIIDOC_
val SCE_ASCIIDOC_DEFAULT=0
val SCE_ASCIIDOC_STRONG1=1
val SCE_ASCIIDOC_STRONG2=2
val SCE_ASCIIDOC_EM1=3
val SCE_ASCIIDOC_EM2=4
val SCE_ASCIIDOC_HEADER1=5
val SCE_ASCIIDOC_HEADER2=6
val SCE_ASCIIDOC_HEADER3=7
val SCE_ASCIIDOC_HEADER4=8
val SCE_ASCIIDOC_HEADER5=9
val SCE_ASCIIDOC_HEADER6=10
val SCE_ASCIIDOC_ULIST_ITEM=11
val SCE_ASCIIDOC_OLIST_ITEM=12
val SCE_ASCIIDOC_BLOCKQUOTE=13
val SCE_ASCIIDOC_LINK=14
val SCE_ASCIIDOC_CODEBK=15
val SCE_ASCIIDOC_PASSBK=16
val SCE_ASCIIDOC_COMMENT=17
val SCE_ASCIIDOC_COMMENTBK=18
val SCE_ASCIIDOC_LITERAL=19
val SCE_ASCIIDOC_LITERALBK=20
val SCE_ASCIIDOC_ATTRIB=21
val SCE_ASCIIDOC_ATTRIBVAL=22
val SCE_ASCIIDOC_MACRO=23
# Lexical states for SCLEX_GDSCRIPT
lex GDScript=SCLEX_GDSCRIPT SCE_GD_
val SCE_GD_DEFAULT=0
val SCE_GD_COMMENTLINE=1
val SCE_GD_NUMBER=2
val SCE_GD_STRING=3
val SCE_GD_CHARACTER=4
val SCE_GD_WORD=5
val SCE_GD_TRIPLE=6
val SCE_GD_TRIPLEDOUBLE=7
val SCE_GD_CLASSNAME=8
val SCE_GD_FUNCNAME=9
val SCE_GD_OPERATOR=10
val SCE_GD_IDENTIFIER=11
val SCE_GD_COMMENTBLOCK=12
val SCE_GD_STRINGEOL=13
val SCE_GD_WORD2=14
val SCE_GD_ANNOTATION=15
val SCE_GD_NODEPATH=16
# Lexical states for SCLEX_TOML
lex TOML=SCLEX_TOML SCE_TOML_
val SCE_TOML_DEFAULT=0
val SCE_TOML_COMMENT=1
val SCE_TOML_IDENTIFIER=2
val SCE_TOML_KEYWORD=3
val SCE_TOML_NUMBER=4
val SCE_TOML_TABLE=5
val SCE_TOML_KEY=6
val SCE_TOML_ERROR=7
val SCE_TOML_OPERATOR=8
val SCE_TOML_STRING_SQ=9
val SCE_TOML_STRING_DQ=10
val SCE_TOML_TRIPLE_STRING_SQ=11
val SCE_TOML_TRIPLE_STRING_DQ=12
val SCE_TOML_ESCAPECHAR=13
val SCE_TOML_DATETIME=14
# Lexical states for SCLEX_TROFF
lex troff=SCLEX_TROFF SCE_TROFF_
val SCE_TROFF_DEFAULT=0
val SCE_TROFF_REQUEST=1
val SCE_TROFF_COMMAND=2
val SCE_TROFF_NUMBER=3
val SCE_TROFF_OPERATOR=4
val SCE_TROFF_STRING=5
val SCE_TROFF_COMMENT=6
val SCE_TROFF_IGNORE=7
val SCE_TROFF_ESCAPE_STRING=8
val SCE_TROFF_ESCAPE_MACRO=9
val SCE_TROFF_ESCAPE_FONT=10
val SCE_TROFF_ESCAPE_NUMBER=11
val SCE_TROFF_ESCAPE_COLOUR=12
val SCE_TROFF_ESCAPE_GLYPH=13
val SCE_TROFF_ESCAPE_ENV=14
val SCE_TROFF_ESCAPE_SUPPRESSION=15
val SCE_TROFF_ESCAPE_SIZE=16
val SCE_TROFF_ESCAPE_TRANSPARENT=17
val SCE_TROFF_ESCAPE_ISVALID=18
val SCE_TROFF_ESCAPE_DRAW=19
val SCE_TROFF_ESCAPE_MOVE=20
val SCE_TROFF_ESCAPE_HEIGHT=21
val SCE_TROFF_ESCAPE_OVERSTRIKE=22
val SCE_TROFF_ESCAPE_SLANT=23
val SCE_TROFF_ESCAPE_WIDTH=24
val SCE_TROFF_ESCAPE_VSPACING=25
val SCE_TROFF_ESCAPE_DEVICE=26
val SCE_TROFF_ESCAPE_NOMOVE=27
# Lexical states for SCLEX_DART
lex Dart=SCLEX_DART SCE_DART_
val SCE_DART_DEFAULT=0
val SCE_DART_COMMENTLINE=1
val SCE_DART_COMMENTLINEDOC=2
val SCE_DART_COMMENTBLOCK=3
val SCE_DART_COMMENTBLOCKDOC=4
val SCE_DART_STRING_SQ=5
val SCE_DART_STRING_DQ=6
val SCE_DART_TRIPLE_STRING_SQ=7
val SCE_DART_TRIPLE_STRING_DQ=8
val SCE_DART_RAWSTRING_SQ=9
val SCE_DART_RAWSTRING_DQ=10
val SCE_DART_TRIPLE_RAWSTRING_SQ=11
val SCE_DART_TRIPLE_RAWSTRING_DQ=12
val SCE_DART_ESCAPECHAR=13
val SCE_DART_IDENTIFIER=14
val SCE_DART_IDENTIFIER_STRING=15
val SCE_DART_OPERATOR=16
val SCE_DART_OPERATOR_STRING=17
val SCE_DART_SYMBOL_IDENTIFIER=18
val SCE_DART_SYMBOL_OPERATOR=19
val SCE_DART_NUMBER=20
val SCE_DART_KEY=21
val SCE_DART_METADATA=22
val SCE_DART_KW_PRIMARY=23
val SCE_DART_KW_SECONDARY=24
val SCE_DART_KW_TERTIARY=25
val SCE_DART_KW_TYPE=26
# Lexical states for SCLEX_ZIG
lex Zig=SCLEX_ZIG SCE_ZIG_
val SCE_ZIG_DEFAULT=0
val SCE_ZIG_COMMENTLINE=1
val SCE_ZIG_COMMENTLINEDOC=2
val SCE_ZIG_COMMENTLINETOP=3
val SCE_ZIG_NUMBER=4
val SCE_ZIG_OPERATOR=5
val SCE_ZIG_CHARACTER=6
val SCE_ZIG_STRING=7
val SCE_ZIG_MULTISTRING=8
val SCE_ZIG_ESCAPECHAR=9
val SCE_ZIG_IDENTIFIER=10
val SCE_ZIG_FUNCTION=11
val SCE_ZIG_BUILTIN_FUNCTION=12
val SCE_ZIG_KW_PRIMARY=13
val SCE_ZIG_KW_SECONDARY=14
val SCE_ZIG_KW_TERTIARY=15
val SCE_ZIG_KW_TYPE=16
val SCE_ZIG_IDENTIFIER_STRING=17
# Lexical states for SCLEX_NIX
lex Nix=SCLEX_NIX SCE_NIX_
val SCE_NIX_DEFAULT=0
val SCE_NIX_COMMENTLINE=1
val SCE_NIX_COMMENTBLOCK=2
val SCE_NIX_STRING=3
val SCE_NIX_STRING_MULTILINE=4
val SCE_NIX_ESCAPECHAR=5
val SCE_NIX_IDENTIFIER=6
val SCE_NIX_OPERATOR=7
val SCE_NIX_OPERATOR_STRING=8
val SCE_NIX_NUMBER=9
val SCE_NIX_KEY=10
val SCE_NIX_PATH=11
val SCE_NIX_KEYWORD1=12
val SCE_NIX_KEYWORD2=13
val SCE_NIX_KEYWORD3=14
val SCE_NIX_KEYWORD4=15
