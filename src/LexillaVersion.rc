// Resource file for Lexilla - provides a version number
// Copyright 2020 by <PERSON> <<EMAIL>>
// The License.txt file describes the conditions under which this software may be distributed.

#include <windows.h>

#define VERSION_LEXILLA "5.4.3"
#define VERSION_WORDS 5, 4, 3, 0

VS_VERSION_INFO VERSIONINFO
FILEVERSION	VERSION_WORDS
PRODUCTVERSION	VERSION_WORDS
FILEFLAGSMASK	0x3fL
FILEFLAGS 0
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_APP
FILESUBTYPE VFT2_UNKNOWN
BEGIN
	BLOCK	"VarFileInfo"
	BEGIN
		VALUE	"Translation",	0x409,	1200
	END
	BLOCK	"StringFileInfo"
	BEGIN
		BLOCK "040904b0"
		BEGIN
			VALUE	"CompanyName",	"<PERSON> <EMAIL>\0"
			VALUE	"FileDescription",	"Lexilla.DLL - a Lexical Analysis Component\0"
			VALUE	"FileVersion",	VERSION_LEXILLA "\0"
			VALUE	"InternalName",	"Lexilla\0"
			VALUE	"LegalCopyright",	"Copyright 2019 by <PERSON>\0"
			VALUE	"OriginalFilename",	"Lexilla.DLL\0"
			VALUE	"ProductName",	"Lexilla\0"
			VALUE	"ProductVersion",	VERSION_LEXILLA "\0"
		END
	END
END
