README for building of Scintilla on Qt

There are two different Scintilla libraries that can be produced:

	ScintillaEditBase
A basic widget callable from C++ which is small and can be used just as is
or with higher level functionality added.

	ScintillaEdit
A more complete C++ widget with a method for every Scintilla API and a
secondary API allowing direct access to document objects.

	Building a library

ScintillaEditBase can be built without performing any generation steps.
The ScintillaEditBase/ScintillaEditBase.pro project can be loaded into
Qt Creator and the "Build All" command performed.
Alternatively, run "qmake" to build make files and then use the platform
make to build. Most commonly, use "make" on Unix and "nmake"
on Windows.

On Linux, qmake may be called qmake-qt5 or qmake-qt4.

ScintillaEdit requires a generation command be run first. From the
ScintillaEdit directory:

python WidgetGen.py

After the generation command has run, the ScintillaEdit.h and
ScintillaEdit.cpp files will have been populated with the Scintilla API
methods.
To build, use Qt Creator or qmake and make as for ScintillaEditBase.
