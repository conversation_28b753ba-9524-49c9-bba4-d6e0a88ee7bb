// @file ScintillaEdit.h
// Extended version of ScintillaEditBase with a method for each API
// Copyright (c) 2011 Archaeopteryx Software, Inc. d/b/a Wingware

#ifndef SCINTILLAEDIT_H
#define SCINTILLAEDIT_H

#include <QPair>

#include "ScintillaEditBase.h"
#include "ScintillaDocument.h"

#ifndef EXPORT_IMPORT_API
#ifdef WIN32
#ifdef MAKING_LIBRARY
#define EXPORT_IMPORT_API __declspec(dllexport)
#else
// Defining dllimport upsets moc
#define EXPORT_IMPORT_API __declspec(dllimport)
//#define EXPORT_IMPORT_API
#endif
#else
#define EXPORT_IMPORT_API
#endif
#endif

class EXPORT_IMPORT_API ScintillaEdit : public ScintillaEditBase {
	Q_OBJECT

public:
	ScintillaEdit(QWidget *parent = 0);
	virtual ~ScintillaEdit();

	QByteArray TextReturner(int message, uptr_t wParam) const;

	QPair<int, int>find_text(int flags, const char *text, int cpMin, int cpMax);
	QByteArray get_text_range(int start, int end);
        ScintillaDocument *get_doc();
        void set_doc(ScintillaDocument *pdoc_);

	// Same as previous two methods but with Qt style names
	QPair<int, int>findText(int flags, const char *text, int cpMin, int cpMax) {
		return find_text(flags, text, cpMin, cpMax);
	}

	QByteArray textRange(int start, int end) {
		return get_text_range(start, end);
	}

	// Exposing the FORMATRANGE api with both underscore & qt style names
	long format_range(bool draw, QPaintDevice* target, QPaintDevice* measure,
			   const QRect& print_rect, const QRect& page_rect,
                          long range_start, long range_end);
	long formatRange(bool draw, QPaintDevice* target, QPaintDevice* measure,
                         const QRect& print_rect, const QRect& page_rect,
                         long range_start, long range_end) {
		return format_range(draw, target, measure, print_rect, page_rect,
		                    range_start, range_end);
	}

/* ++Autogenerated -- start of section automatically generated from Scintilla.iface */
/* --Autogenerated -- end of section automatically generated from Scintilla.iface */

};

#if defined(__GNUC__)
#pragma GCC diagnostic ignored "-Wmissing-field-initializers"
#if !defined(__clang__) && (__GNUC__ >= 8)
#pragma GCC diagnostic ignored "-Wcast-function-type"
#endif
#endif

#endif /* SCINTILLAEDIT_H */
