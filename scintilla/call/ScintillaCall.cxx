// SciTE - Scintilla based Text Editor
/** @file ScintillaCall.cxx
 ** Interface to calling a Scintilla instance.
 **/
// Copyright 1998-2019 by <PERSON> <<EMAIL>>
// The License.txt file describes the conditions under which this software may be distributed.

/* Most of this file is automatically generated from the Scintilla.iface interface definition
 * file which contains any comments about the definitions. APIFacer.py does the generation. */

#include <cstdint>

#include <string>
#include <string_view>

#include "ScintillaTypes.h"
#include "ScintillaMessages.h"
#include "ScintillaCall.h"
#include "ScintillaStructures.h"

namespace Scintilla {

ScintillaCall::ScintillaCall() noexcept : fn(nullptr), ptr(0), statusLastCall(Status::Ok) {
}

void ScintillaCall::SetFnPtr(FunctionDirect fn_, intptr_t ptr_) noexcept {
	fn = fn_;
	ptr = ptr_;
}

bool ScintillaCall::IsValid() const noexcept {
	return fn && ptr;
}

intptr_t ScintillaCall::Call(Message msg, uintptr_t wParam, intptr_t lParam) {
	if (!fn)
		throw Failure(Status::Failure);
	int status = 0;
	const intptr_t retVal = fn(ptr, static_cast<unsigned int>(msg), wParam, lParam, &status);
	statusLastCall = static_cast<Scintilla::Status>(status);
	if (statusLastCall > Status::Ok && statusLastCall < Status::WarnStart)
		throw Failure(statusLastCall);
	return retVal;
}

intptr_t ScintillaCall::CallPointer(Message msg, uintptr_t wParam, void *s) {
	return Call(msg, wParam, reinterpret_cast<intptr_t>(s));
}

intptr_t ScintillaCall::CallString(Message msg, uintptr_t wParam, const char *s) {
	return Call(msg, wParam, reinterpret_cast<intptr_t>(s));
}

std::string ScintillaCall::CallReturnString(Message msg, uintptr_t wParam) {
	const size_t len = CallPointer(msg, wParam, nullptr);
	if (len) {
		std::string value(len, '\0');
		CallPointer(msg, wParam, value.data());
		return value;
	} else {
		return std::string();
	}
}

// Common APIs made more structured and type-safe

Position ScintillaCall::LineStart(Line line) {
	return Call(Message::PositionFromLine, line);
}

Position ScintillaCall::LineEnd(Line line) {
	return Call(Message::GetLineEndPosition, line);
}

Span ScintillaCall::SelectionSpan() {
	return Span(
		       Call(Message::GetSelectionStart),
		       Call(Message::GetSelectionEnd));
}

Span ScintillaCall::TargetSpan() {
	return Span(
		       Call(Message::GetTargetStart),
		       Call(Message::GetTargetEnd));
}

void ScintillaCall::SetTarget(Span span) {
	Call(Message::SetTargetRange, span.start, span.end);
}

void ScintillaCall::ColouriseAll() {
	Colourise(0, -1);
}

char ScintillaCall::CharacterAt(Position position) {
	return static_cast<char>(Call(Message::GetCharAt, position));
}

int ScintillaCall::UnsignedStyleAt(Position position) {
	return static_cast<int>(Call(Message::GetStyleIndexAt, position));
}

std::string ScintillaCall::StringOfSpan(Span span) {
	if (span.Length() == 0) {
		return std::string();
	} else {
		std::string text(span.Length(), '\0');
		SetTarget(span);
		TargetText(text.data());
		return text;
	}
}

std::string ScintillaCall::StringOfRange(Span span) {
	if (span.Length() == 0) {
		return std::string();
	} else {
		std::string text(span.Length(), '\0');
		TextRangeFull tr{ {span.start, span.end}, text.data() };
		GetTextRangeFull(&tr);
		return text;
	}
}

Position ScintillaCall::ReplaceTarget(std::string_view text) {
	return ScintillaCall::CallString(Message::ReplaceTarget, text.length(), text.data());
}

Position ScintillaCall::ReplaceTargetRE(std::string_view text) {
	return CallString(Message::ReplaceTargetRE, text.length(), text.data());
}

Position ScintillaCall::ReplaceTargetMinimal(std::string_view text) {
	return CallString(Message::ReplaceTargetMinimal, text.length(), text.data());
}

Position ScintillaCall::SearchInTarget(std::string_view text) {
	return CallString(Message::SearchInTarget, text.length(), text.data());
}

Span ScintillaCall::SpanSearchInTarget(std::string_view text) {
	const Position posFound = SearchInTarget(text);
	if (posFound >= 0)
		return Span(posFound, TargetEnd());
	else
		return Span(posFound, 0);
}

// Generated methods

// ScintillaCall requires automatically generated casts as it is converting
// specific types to/from generic arguments and return values of 'Call'.
// Suppress Visual C++ Code Analysis warnings for these casts and pointer returns.
// 26472 = Don't use a static_cast for arithmetic conversions.
// 26487 = Don't return a pointer '*' that may be invalid (lifetime.4).
// 26490 = Don't use reinterpret_cast.
#if defined(_MSC_VER)
#pragma warning(disable: 26472 26487 26490)
#endif

//++Autogenerated -- start of section automatically generated from Scintilla.iface
void ScintillaCall::AddText(Position length, const char *text) {
	CallString(Message::AddText, length, text);
}

void ScintillaCall::AddStyledText(Position length, const char *c) {
	CallString(Message::AddStyledText, length, c);
}

void ScintillaCall::InsertText(Position pos, const char *text) {
	CallString(Message::InsertText, pos, text);
}

void ScintillaCall::ChangeInsertion(Position length, const char *text) {
	CallString(Message::ChangeInsertion, length, text);
}

void ScintillaCall::ClearAll() {
	Call(Message::ClearAll);
}

void ScintillaCall::DeleteRange(Position start, Position lengthDelete) {
	Call(Message::DeleteRange, start, lengthDelete);
}

void ScintillaCall::ClearDocumentStyle() {
	Call(Message::ClearDocumentStyle);
}

Position ScintillaCall::Length() {
	return Call(Message::GetLength);
}

int ScintillaCall::CharAt(Position pos) {
	return static_cast<int>(Call(Message::GetCharAt, pos));
}

Position ScintillaCall::CurrentPos() {
	return Call(Message::GetCurrentPos);
}

Position ScintillaCall::Anchor() {
	return Call(Message::GetAnchor);
}

int ScintillaCall::StyleAt(Position pos) {
	return static_cast<int>(Call(Message::GetStyleAt, pos));
}

int ScintillaCall::StyleIndexAt(Position pos) {
	return static_cast<int>(Call(Message::GetStyleIndexAt, pos));
}

void ScintillaCall::Redo() {
	Call(Message::Redo);
}

void ScintillaCall::SetUndoCollection(bool collectUndo) {
	Call(Message::SetUndoCollection, collectUndo);
}

void ScintillaCall::SelectAll() {
	Call(Message::SelectAll);
}

void ScintillaCall::SetSavePoint() {
	Call(Message::SetSavePoint);
}

Position ScintillaCall::GetStyledText(void *tr) {
	return CallPointer(Message::GetStyledText, 0, tr);
}

Position ScintillaCall::GetStyledTextFull(TextRangeFull *tr) {
	return CallPointer(Message::GetStyledTextFull, 0, tr);
}

bool ScintillaCall::CanRedo() {
	return Call(Message::CanRedo);
}

Line ScintillaCall::MarkerLineFromHandle(int markerHandle) {
	return Call(Message::MarkerLineFromHandle, markerHandle);
}

void ScintillaCall::MarkerDeleteHandle(int markerHandle) {
	Call(Message::MarkerDeleteHandle, markerHandle);
}

int ScintillaCall::MarkerHandleFromLine(Line line, int which) {
	return static_cast<int>(Call(Message::MarkerHandleFromLine, line, which));
}

int ScintillaCall::MarkerNumberFromLine(Line line, int which) {
	return static_cast<int>(Call(Message::MarkerNumberFromLine, line, which));
}

bool ScintillaCall::UndoCollection() {
	return Call(Message::GetUndoCollection);
}

WhiteSpace ScintillaCall::ViewWS() {
	return static_cast<Scintilla::WhiteSpace>(Call(Message::GetViewWS));
}

void ScintillaCall::SetViewWS(Scintilla::WhiteSpace viewWS) {
	Call(Message::SetViewWS, static_cast<uintptr_t>(viewWS));
}

TabDrawMode ScintillaCall::TabDrawMode() {
	return static_cast<Scintilla::TabDrawMode>(Call(Message::GetTabDrawMode));
}

void ScintillaCall::SetTabDrawMode(Scintilla::TabDrawMode tabDrawMode) {
	Call(Message::SetTabDrawMode, static_cast<uintptr_t>(tabDrawMode));
}

Position ScintillaCall::PositionFromPoint(int x, int y) {
	return Call(Message::PositionFromPoint, x, y);
}

Position ScintillaCall::PositionFromPointClose(int x, int y) {
	return Call(Message::PositionFromPointClose, x, y);
}

void ScintillaCall::GotoLine(Line line) {
	Call(Message::GotoLine, line);
}

void ScintillaCall::GotoPos(Position caret) {
	Call(Message::GotoPos, caret);
}

void ScintillaCall::SetAnchor(Position anchor) {
	Call(Message::SetAnchor, anchor);
}

Position ScintillaCall::GetCurLine(Position length, char *text) {
	return CallPointer(Message::GetCurLine, length, text);
}

std::string ScintillaCall::GetCurLine(Position length) {
	return CallReturnString(Message::GetCurLine, length);
}

Position ScintillaCall::EndStyled() {
	return Call(Message::GetEndStyled);
}

void ScintillaCall::ConvertEOLs(Scintilla::EndOfLine eolMode) {
	Call(Message::ConvertEOLs, static_cast<uintptr_t>(eolMode));
}

EndOfLine ScintillaCall::EOLMode() {
	return static_cast<Scintilla::EndOfLine>(Call(Message::GetEOLMode));
}

void ScintillaCall::SetEOLMode(Scintilla::EndOfLine eolMode) {
	Call(Message::SetEOLMode, static_cast<uintptr_t>(eolMode));
}

void ScintillaCall::StartStyling(Position start, int unused) {
	Call(Message::StartStyling, start, unused);
}

void ScintillaCall::SetStyling(Position length, int style) {
	Call(Message::SetStyling, length, style);
}

bool ScintillaCall::BufferedDraw() {
	return Call(Message::GetBufferedDraw);
}

void ScintillaCall::SetBufferedDraw(bool buffered) {
	Call(Message::SetBufferedDraw, buffered);
}

void ScintillaCall::SetTabWidth(int tabWidth) {
	Call(Message::SetTabWidth, tabWidth);
}

int ScintillaCall::TabWidth() {
	return static_cast<int>(Call(Message::GetTabWidth));
}

void ScintillaCall::SetTabMinimumWidth(int pixels) {
	Call(Message::SetTabMinimumWidth, pixels);
}

int ScintillaCall::TabMinimumWidth() {
	return static_cast<int>(Call(Message::GetTabMinimumWidth));
}

void ScintillaCall::ClearTabStops(Line line) {
	Call(Message::ClearTabStops, line);
}

void ScintillaCall::AddTabStop(Line line, int x) {
	Call(Message::AddTabStop, line, x);
}

int ScintillaCall::GetNextTabStop(Line line, int x) {
	return static_cast<int>(Call(Message::GetNextTabStop, line, x));
}

void ScintillaCall::SetCodePage(int codePage) {
	Call(Message::SetCodePage, codePage);
}

void ScintillaCall::SetFontLocale(const char *localeName) {
	CallString(Message::SetFontLocale, 0, localeName);
}

int ScintillaCall::FontLocale(char *localeName) {
	return static_cast<int>(CallPointer(Message::GetFontLocale, 0, localeName));
}

std::string ScintillaCall::FontLocale() {
	return CallReturnString(Message::GetFontLocale, 0);
}

IMEInteraction ScintillaCall::IMEInteraction() {
	return static_cast<Scintilla::IMEInteraction>(Call(Message::GetIMEInteraction));
}

void ScintillaCall::SetIMEInteraction(Scintilla::IMEInteraction imeInteraction) {
	Call(Message::SetIMEInteraction, static_cast<uintptr_t>(imeInteraction));
}

void ScintillaCall::MarkerDefine(int markerNumber, Scintilla::MarkerSymbol markerSymbol) {
	Call(Message::MarkerDefine, markerNumber, static_cast<intptr_t>(markerSymbol));
}

void ScintillaCall::MarkerSetFore(int markerNumber, Colour fore) {
	Call(Message::MarkerSetFore, markerNumber, fore);
}

void ScintillaCall::MarkerSetBack(int markerNumber, Colour back) {
	Call(Message::MarkerSetBack, markerNumber, back);
}

void ScintillaCall::MarkerSetBackSelected(int markerNumber, Colour back) {
	Call(Message::MarkerSetBackSelected, markerNumber, back);
}

void ScintillaCall::MarkerSetForeTranslucent(int markerNumber, ColourAlpha fore) {
	Call(Message::MarkerSetForeTranslucent, markerNumber, fore);
}

void ScintillaCall::MarkerSetBackTranslucent(int markerNumber, ColourAlpha back) {
	Call(Message::MarkerSetBackTranslucent, markerNumber, back);
}

void ScintillaCall::MarkerSetBackSelectedTranslucent(int markerNumber, ColourAlpha back) {
	Call(Message::MarkerSetBackSelectedTranslucent, markerNumber, back);
}

void ScintillaCall::MarkerSetStrokeWidth(int markerNumber, int hundredths) {
	Call(Message::MarkerSetStrokeWidth, markerNumber, hundredths);
}

void ScintillaCall::MarkerEnableHighlight(bool enabled) {
	Call(Message::MarkerEnableHighlight, enabled);
}

int ScintillaCall::MarkerAdd(Line line, int markerNumber) {
	return static_cast<int>(Call(Message::MarkerAdd, line, markerNumber));
}

void ScintillaCall::MarkerDelete(Line line, int markerNumber) {
	Call(Message::MarkerDelete, line, markerNumber);
}

void ScintillaCall::MarkerDeleteAll(int markerNumber) {
	Call(Message::MarkerDeleteAll, markerNumber);
}

int ScintillaCall::MarkerGet(Line line) {
	return static_cast<int>(Call(Message::MarkerGet, line));
}

Line ScintillaCall::MarkerNext(Line lineStart, int markerMask) {
	return Call(Message::MarkerNext, lineStart, markerMask);
}

Line ScintillaCall::MarkerPrevious(Line lineStart, int markerMask) {
	return Call(Message::MarkerPrevious, lineStart, markerMask);
}

void ScintillaCall::MarkerDefinePixmap(int markerNumber, const char *pixmap) {
	CallString(Message::MarkerDefinePixmap, markerNumber, pixmap);
}

void ScintillaCall::MarkerAddSet(Line line, int markerSet) {
	Call(Message::MarkerAddSet, line, markerSet);
}

void ScintillaCall::MarkerSetAlpha(int markerNumber, Scintilla::Alpha alpha) {
	Call(Message::MarkerSetAlpha, markerNumber, static_cast<intptr_t>(alpha));
}

Layer ScintillaCall::MarkerGetLayer(int markerNumber) {
	return static_cast<Scintilla::Layer>(Call(Message::MarkerGetLayer, markerNumber));
}

void ScintillaCall::MarkerSetLayer(int markerNumber, Scintilla::Layer layer) {
	Call(Message::MarkerSetLayer, markerNumber, static_cast<intptr_t>(layer));
}

void ScintillaCall::SetMarginTypeN(int margin, Scintilla::MarginType marginType) {
	Call(Message::SetMarginTypeN, margin, static_cast<intptr_t>(marginType));
}

MarginType ScintillaCall::MarginTypeN(int margin) {
	return static_cast<Scintilla::MarginType>(Call(Message::GetMarginTypeN, margin));
}

void ScintillaCall::SetMarginWidthN(int margin, int pixelWidth) {
	Call(Message::SetMarginWidthN, margin, pixelWidth);
}

int ScintillaCall::MarginWidthN(int margin) {
	return static_cast<int>(Call(Message::GetMarginWidthN, margin));
}

void ScintillaCall::SetMarginMaskN(int margin, int mask) {
	Call(Message::SetMarginMaskN, margin, mask);
}

int ScintillaCall::MarginMaskN(int margin) {
	return static_cast<int>(Call(Message::GetMarginMaskN, margin));
}

void ScintillaCall::SetMarginSensitiveN(int margin, bool sensitive) {
	Call(Message::SetMarginSensitiveN, margin, sensitive);
}

bool ScintillaCall::MarginSensitiveN(int margin) {
	return Call(Message::GetMarginSensitiveN, margin);
}

void ScintillaCall::SetMarginCursorN(int margin, Scintilla::CursorShape cursor) {
	Call(Message::SetMarginCursorN, margin, static_cast<intptr_t>(cursor));
}

CursorShape ScintillaCall::MarginCursorN(int margin) {
	return static_cast<Scintilla::CursorShape>(Call(Message::GetMarginCursorN, margin));
}

void ScintillaCall::SetMarginBackN(int margin, Colour back) {
	Call(Message::SetMarginBackN, margin, back);
}

Colour ScintillaCall::MarginBackN(int margin) {
	return static_cast<Colour>(Call(Message::GetMarginBackN, margin));
}

void ScintillaCall::SetMargins(int margins) {
	Call(Message::SetMargins, margins);
}

int ScintillaCall::Margins() {
	return static_cast<int>(Call(Message::GetMargins));
}

void ScintillaCall::StyleClearAll() {
	Call(Message::StyleClearAll);
}

void ScintillaCall::StyleSetFore(int style, Colour fore) {
	Call(Message::StyleSetFore, style, fore);
}

void ScintillaCall::StyleSetBack(int style, Colour back) {
	Call(Message::StyleSetBack, style, back);
}

void ScintillaCall::StyleSetBold(int style, bool bold) {
	Call(Message::StyleSetBold, style, bold);
}

void ScintillaCall::StyleSetItalic(int style, bool italic) {
	Call(Message::StyleSetItalic, style, italic);
}

void ScintillaCall::StyleSetSize(int style, int sizePoints) {
	Call(Message::StyleSetSize, style, sizePoints);
}

void ScintillaCall::StyleSetFont(int style, const char *fontName) {
	CallString(Message::StyleSetFont, style, fontName);
}

void ScintillaCall::StyleSetEOLFilled(int style, bool eolFilled) {
	Call(Message::StyleSetEOLFilled, style, eolFilled);
}

void ScintillaCall::StyleResetDefault() {
	Call(Message::StyleResetDefault);
}

void ScintillaCall::StyleSetUnderline(int style, bool underline) {
	Call(Message::StyleSetUnderline, style, underline);
}

Colour ScintillaCall::StyleGetFore(int style) {
	return static_cast<Colour>(Call(Message::StyleGetFore, style));
}

Colour ScintillaCall::StyleGetBack(int style) {
	return static_cast<Colour>(Call(Message::StyleGetBack, style));
}

bool ScintillaCall::StyleGetBold(int style) {
	return Call(Message::StyleGetBold, style);
}

bool ScintillaCall::StyleGetItalic(int style) {
	return Call(Message::StyleGetItalic, style);
}

int ScintillaCall::StyleGetSize(int style) {
	return static_cast<int>(Call(Message::StyleGetSize, style));
}

int ScintillaCall::StyleGetFont(int style, char *fontName) {
	return static_cast<int>(CallPointer(Message::StyleGetFont, style, fontName));
}

std::string ScintillaCall::StyleGetFont(int style) {
	return CallReturnString(Message::StyleGetFont, style);
}

bool ScintillaCall::StyleGetEOLFilled(int style) {
	return Call(Message::StyleGetEOLFilled, style);
}

bool ScintillaCall::StyleGetUnderline(int style) {
	return Call(Message::StyleGetUnderline, style);
}

CaseVisible ScintillaCall::StyleGetCase(int style) {
	return static_cast<Scintilla::CaseVisible>(Call(Message::StyleGetCase, style));
}

CharacterSet ScintillaCall::StyleGetCharacterSet(int style) {
	return static_cast<Scintilla::CharacterSet>(Call(Message::StyleGetCharacterSet, style));
}

bool ScintillaCall::StyleGetVisible(int style) {
	return Call(Message::StyleGetVisible, style);
}

bool ScintillaCall::StyleGetChangeable(int style) {
	return Call(Message::StyleGetChangeable, style);
}

bool ScintillaCall::StyleGetHotSpot(int style) {
	return Call(Message::StyleGetHotSpot, style);
}

void ScintillaCall::StyleSetCase(int style, Scintilla::CaseVisible caseVisible) {
	Call(Message::StyleSetCase, style, static_cast<intptr_t>(caseVisible));
}

void ScintillaCall::StyleSetSizeFractional(int style, int sizeHundredthPoints) {
	Call(Message::StyleSetSizeFractional, style, sizeHundredthPoints);
}

int ScintillaCall::StyleGetSizeFractional(int style) {
	return static_cast<int>(Call(Message::StyleGetSizeFractional, style));
}

void ScintillaCall::StyleSetWeight(int style, Scintilla::FontWeight weight) {
	Call(Message::StyleSetWeight, style, static_cast<intptr_t>(weight));
}

FontWeight ScintillaCall::StyleGetWeight(int style) {
	return static_cast<Scintilla::FontWeight>(Call(Message::StyleGetWeight, style));
}

void ScintillaCall::StyleSetCharacterSet(int style, Scintilla::CharacterSet characterSet) {
	Call(Message::StyleSetCharacterSet, style, static_cast<intptr_t>(characterSet));
}

void ScintillaCall::StyleSetHotSpot(int style, bool hotspot) {
	Call(Message::StyleSetHotSpot, style, hotspot);
}

void ScintillaCall::StyleSetCheckMonospaced(int style, bool checkMonospaced) {
	Call(Message::StyleSetCheckMonospaced, style, checkMonospaced);
}

bool ScintillaCall::StyleGetCheckMonospaced(int style) {
	return Call(Message::StyleGetCheckMonospaced, style);
}

void ScintillaCall::StyleSetStretch(int style, Scintilla::FontStretch stretch) {
	Call(Message::StyleSetStretch, style, static_cast<intptr_t>(stretch));
}

FontStretch ScintillaCall::StyleGetStretch(int style) {
	return static_cast<Scintilla::FontStretch>(Call(Message::StyleGetStretch, style));
}

void ScintillaCall::StyleSetInvisibleRepresentation(int style, const char *representation) {
	CallString(Message::StyleSetInvisibleRepresentation, style, representation);
}

int ScintillaCall::StyleGetInvisibleRepresentation(int style, char *representation) {
	return static_cast<int>(CallPointer(Message::StyleGetInvisibleRepresentation, style, representation));
}

std::string ScintillaCall::StyleGetInvisibleRepresentation(int style) {
	return CallReturnString(Message::StyleGetInvisibleRepresentation, style);
}

void ScintillaCall::SetElementColour(Scintilla::Element element, ColourAlpha colourElement) {
	Call(Message::SetElementColour, static_cast<uintptr_t>(element), colourElement);
}

ColourAlpha ScintillaCall::ElementColour(Scintilla::Element element) {
	return static_cast<ColourAlpha>(Call(Message::GetElementColour, static_cast<uintptr_t>(element)));
}

void ScintillaCall::ResetElementColour(Scintilla::Element element) {
	Call(Message::ResetElementColour, static_cast<uintptr_t>(element));
}

bool ScintillaCall::ElementIsSet(Scintilla::Element element) {
	return Call(Message::GetElementIsSet, static_cast<uintptr_t>(element));
}

bool ScintillaCall::ElementAllowsTranslucent(Scintilla::Element element) {
	return Call(Message::GetElementAllowsTranslucent, static_cast<uintptr_t>(element));
}

ColourAlpha ScintillaCall::ElementBaseColour(Scintilla::Element element) {
	return static_cast<ColourAlpha>(Call(Message::GetElementBaseColour, static_cast<uintptr_t>(element)));
}

void ScintillaCall::SetSelFore(bool useSetting, Colour fore) {
	Call(Message::SetSelFore, useSetting, fore);
}

void ScintillaCall::SetSelBack(bool useSetting, Colour back) {
	Call(Message::SetSelBack, useSetting, back);
}

Alpha ScintillaCall::SelAlpha() {
	return static_cast<Scintilla::Alpha>(Call(Message::GetSelAlpha));
}

void ScintillaCall::SetSelAlpha(Scintilla::Alpha alpha) {
	Call(Message::SetSelAlpha, static_cast<uintptr_t>(alpha));
}

bool ScintillaCall::SelEOLFilled() {
	return Call(Message::GetSelEOLFilled);
}

void ScintillaCall::SetSelEOLFilled(bool filled) {
	Call(Message::SetSelEOLFilled, filled);
}

Layer ScintillaCall::SelectionLayer() {
	return static_cast<Scintilla::Layer>(Call(Message::GetSelectionLayer));
}

void ScintillaCall::SetSelectionLayer(Scintilla::Layer layer) {
	Call(Message::SetSelectionLayer, static_cast<uintptr_t>(layer));
}

Layer ScintillaCall::CaretLineLayer() {
	return static_cast<Scintilla::Layer>(Call(Message::GetCaretLineLayer));
}

void ScintillaCall::SetCaretLineLayer(Scintilla::Layer layer) {
	Call(Message::SetCaretLineLayer, static_cast<uintptr_t>(layer));
}

bool ScintillaCall::CaretLineHighlightSubLine() {
	return Call(Message::GetCaretLineHighlightSubLine);
}

void ScintillaCall::SetCaretLineHighlightSubLine(bool subLine) {
	Call(Message::SetCaretLineHighlightSubLine, subLine);
}

void ScintillaCall::SetCaretFore(Colour fore) {
	Call(Message::SetCaretFore, fore);
}

void ScintillaCall::AssignCmdKey(int keyDefinition, int sciCommand) {
	Call(Message::AssignCmdKey, keyDefinition, sciCommand);
}

void ScintillaCall::ClearCmdKey(int keyDefinition) {
	Call(Message::ClearCmdKey, keyDefinition);
}

void ScintillaCall::ClearAllCmdKeys() {
	Call(Message::ClearAllCmdKeys);
}

void ScintillaCall::SetStylingEx(Position length, const char *styles) {
	CallString(Message::SetStylingEx, length, styles);
}

void ScintillaCall::StyleSetVisible(int style, bool visible) {
	Call(Message::StyleSetVisible, style, visible);
}

int ScintillaCall::CaretPeriod() {
	return static_cast<int>(Call(Message::GetCaretPeriod));
}

void ScintillaCall::SetCaretPeriod(int periodMilliseconds) {
	Call(Message::SetCaretPeriod, periodMilliseconds);
}

void ScintillaCall::SetWordChars(const char *characters) {
	CallString(Message::SetWordChars, 0, characters);
}

int ScintillaCall::WordChars(char *characters) {
	return static_cast<int>(CallPointer(Message::GetWordChars, 0, characters));
}

std::string ScintillaCall::WordChars() {
	return CallReturnString(Message::GetWordChars, 0);
}

void ScintillaCall::SetCharacterCategoryOptimization(int countCharacters) {
	Call(Message::SetCharacterCategoryOptimization, countCharacters);
}

int ScintillaCall::CharacterCategoryOptimization() {
	return static_cast<int>(Call(Message::GetCharacterCategoryOptimization));
}

void ScintillaCall::BeginUndoAction() {
	Call(Message::BeginUndoAction);
}

void ScintillaCall::EndUndoAction() {
	Call(Message::EndUndoAction);
}

int ScintillaCall::UndoSequence() {
	return static_cast<int>(Call(Message::GetUndoSequence));
}

int ScintillaCall::UndoActions() {
	return static_cast<int>(Call(Message::GetUndoActions));
}

void ScintillaCall::SetUndoSavePoint(int action) {
	Call(Message::SetUndoSavePoint, action);
}

int ScintillaCall::UndoSavePoint() {
	return static_cast<int>(Call(Message::GetUndoSavePoint));
}

void ScintillaCall::SetUndoDetach(int action) {
	Call(Message::SetUndoDetach, action);
}

int ScintillaCall::UndoDetach() {
	return static_cast<int>(Call(Message::GetUndoDetach));
}

void ScintillaCall::SetUndoTentative(int action) {
	Call(Message::SetUndoTentative, action);
}

int ScintillaCall::UndoTentative() {
	return static_cast<int>(Call(Message::GetUndoTentative));
}

void ScintillaCall::SetUndoCurrent(int action) {
	Call(Message::SetUndoCurrent, action);
}

int ScintillaCall::UndoCurrent() {
	return static_cast<int>(Call(Message::GetUndoCurrent));
}

void ScintillaCall::PushUndoActionType(int type, Position pos) {
	Call(Message::PushUndoActionType, type, pos);
}

void ScintillaCall::ChangeLastUndoActionText(Position length, const char *text) {
	CallString(Message::ChangeLastUndoActionText, length, text);
}

int ScintillaCall::UndoActionType(int action) {
	return static_cast<int>(Call(Message::GetUndoActionType, action));
}

Position ScintillaCall::UndoActionPosition(int action) {
	return Call(Message::GetUndoActionPosition, action);
}

int ScintillaCall::UndoActionText(int action, char *text) {
	return static_cast<int>(CallPointer(Message::GetUndoActionText, action, text));
}

std::string ScintillaCall::UndoActionText(int action) {
	return CallReturnString(Message::GetUndoActionText, action);
}

void ScintillaCall::IndicSetStyle(int indicator, Scintilla::IndicatorStyle indicatorStyle) {
	Call(Message::IndicSetStyle, indicator, static_cast<intptr_t>(indicatorStyle));
}

IndicatorStyle ScintillaCall::IndicGetStyle(int indicator) {
	return static_cast<Scintilla::IndicatorStyle>(Call(Message::IndicGetStyle, indicator));
}

void ScintillaCall::IndicSetFore(int indicator, Colour fore) {
	Call(Message::IndicSetFore, indicator, fore);
}

Colour ScintillaCall::IndicGetFore(int indicator) {
	return static_cast<Colour>(Call(Message::IndicGetFore, indicator));
}

void ScintillaCall::IndicSetUnder(int indicator, bool under) {
	Call(Message::IndicSetUnder, indicator, under);
}

bool ScintillaCall::IndicGetUnder(int indicator) {
	return Call(Message::IndicGetUnder, indicator);
}

void ScintillaCall::IndicSetHoverStyle(int indicator, Scintilla::IndicatorStyle indicatorStyle) {
	Call(Message::IndicSetHoverStyle, indicator, static_cast<intptr_t>(indicatorStyle));
}

IndicatorStyle ScintillaCall::IndicGetHoverStyle(int indicator) {
	return static_cast<Scintilla::IndicatorStyle>(Call(Message::IndicGetHoverStyle, indicator));
}

void ScintillaCall::IndicSetHoverFore(int indicator, Colour fore) {
	Call(Message::IndicSetHoverFore, indicator, fore);
}

Colour ScintillaCall::IndicGetHoverFore(int indicator) {
	return static_cast<Colour>(Call(Message::IndicGetHoverFore, indicator));
}

void ScintillaCall::IndicSetFlags(int indicator, Scintilla::IndicFlag flags) {
	Call(Message::IndicSetFlags, indicator, static_cast<intptr_t>(flags));
}

IndicFlag ScintillaCall::IndicGetFlags(int indicator) {
	return static_cast<Scintilla::IndicFlag>(Call(Message::IndicGetFlags, indicator));
}

void ScintillaCall::IndicSetStrokeWidth(int indicator, int hundredths) {
	Call(Message::IndicSetStrokeWidth, indicator, hundredths);
}

int ScintillaCall::IndicGetStrokeWidth(int indicator) {
	return static_cast<int>(Call(Message::IndicGetStrokeWidth, indicator));
}

void ScintillaCall::SetWhitespaceFore(bool useSetting, Colour fore) {
	Call(Message::SetWhitespaceFore, useSetting, fore);
}

void ScintillaCall::SetWhitespaceBack(bool useSetting, Colour back) {
	Call(Message::SetWhitespaceBack, useSetting, back);
}

void ScintillaCall::SetWhitespaceSize(int size) {
	Call(Message::SetWhitespaceSize, size);
}

int ScintillaCall::WhitespaceSize() {
	return static_cast<int>(Call(Message::GetWhitespaceSize));
}

void ScintillaCall::SetLineState(Line line, int state) {
	Call(Message::SetLineState, line, state);
}

int ScintillaCall::LineState(Line line) {
	return static_cast<int>(Call(Message::GetLineState, line));
}

int ScintillaCall::MaxLineState() {
	return static_cast<int>(Call(Message::GetMaxLineState));
}

bool ScintillaCall::CaretLineVisible() {
	return Call(Message::GetCaretLineVisible);
}

void ScintillaCall::SetCaretLineVisible(bool show) {
	Call(Message::SetCaretLineVisible, show);
}

Colour ScintillaCall::CaretLineBack() {
	return static_cast<Colour>(Call(Message::GetCaretLineBack));
}

void ScintillaCall::SetCaretLineBack(Colour back) {
	Call(Message::SetCaretLineBack, back);
}

int ScintillaCall::CaretLineFrame() {
	return static_cast<int>(Call(Message::GetCaretLineFrame));
}

void ScintillaCall::SetCaretLineFrame(int width) {
	Call(Message::SetCaretLineFrame, width);
}

void ScintillaCall::StyleSetChangeable(int style, bool changeable) {
	Call(Message::StyleSetChangeable, style, changeable);
}

void ScintillaCall::AutoCShow(Position lengthEntered, const char *itemList) {
	CallString(Message::AutoCShow, lengthEntered, itemList);
}

void ScintillaCall::AutoCCancel() {
	Call(Message::AutoCCancel);
}

bool ScintillaCall::AutoCActive() {
	return Call(Message::AutoCActive);
}

Position ScintillaCall::AutoCPosStart() {
	return Call(Message::AutoCPosStart);
}

void ScintillaCall::AutoCComplete() {
	Call(Message::AutoCComplete);
}

void ScintillaCall::AutoCStops(const char *characterSet) {
	CallString(Message::AutoCStops, 0, characterSet);
}

void ScintillaCall::AutoCSetSeparator(int separatorCharacter) {
	Call(Message::AutoCSetSeparator, separatorCharacter);
}

int ScintillaCall::AutoCGetSeparator() {
	return static_cast<int>(Call(Message::AutoCGetSeparator));
}

void ScintillaCall::AutoCSelect(const char *select) {
	CallString(Message::AutoCSelect, 0, select);
}

void ScintillaCall::AutoCSetCancelAtStart(bool cancel) {
	Call(Message::AutoCSetCancelAtStart, cancel);
}

bool ScintillaCall::AutoCGetCancelAtStart() {
	return Call(Message::AutoCGetCancelAtStart);
}

void ScintillaCall::AutoCSetFillUps(const char *characterSet) {
	CallString(Message::AutoCSetFillUps, 0, characterSet);
}

void ScintillaCall::AutoCSetChooseSingle(bool chooseSingle) {
	Call(Message::AutoCSetChooseSingle, chooseSingle);
}

bool ScintillaCall::AutoCGetChooseSingle() {
	return Call(Message::AutoCGetChooseSingle);
}

void ScintillaCall::AutoCSetIgnoreCase(bool ignoreCase) {
	Call(Message::AutoCSetIgnoreCase, ignoreCase);
}

bool ScintillaCall::AutoCGetIgnoreCase() {
	return Call(Message::AutoCGetIgnoreCase);
}

void ScintillaCall::UserListShow(int listType, const char *itemList) {
	CallString(Message::UserListShow, listType, itemList);
}

void ScintillaCall::AutoCSetAutoHide(bool autoHide) {
	Call(Message::AutoCSetAutoHide, autoHide);
}

bool ScintillaCall::AutoCGetAutoHide() {
	return Call(Message::AutoCGetAutoHide);
}

void ScintillaCall::AutoCSetOptions(Scintilla::AutoCompleteOption options) {
	Call(Message::AutoCSetOptions, static_cast<uintptr_t>(options));
}

AutoCompleteOption ScintillaCall::AutoCGetOptions() {
	return static_cast<Scintilla::AutoCompleteOption>(Call(Message::AutoCGetOptions));
}

void ScintillaCall::AutoCSetDropRestOfWord(bool dropRestOfWord) {
	Call(Message::AutoCSetDropRestOfWord, dropRestOfWord);
}

bool ScintillaCall::AutoCGetDropRestOfWord() {
	return Call(Message::AutoCGetDropRestOfWord);
}

void ScintillaCall::RegisterImage(int type, const char *xpmData) {
	CallString(Message::RegisterImage, type, xpmData);
}

void ScintillaCall::ClearRegisteredImages() {
	Call(Message::ClearRegisteredImages);
}

int ScintillaCall::AutoCGetTypeSeparator() {
	return static_cast<int>(Call(Message::AutoCGetTypeSeparator));
}

void ScintillaCall::AutoCSetTypeSeparator(int separatorCharacter) {
	Call(Message::AutoCSetTypeSeparator, separatorCharacter);
}

void ScintillaCall::AutoCSetMaxWidth(int characterCount) {
	Call(Message::AutoCSetMaxWidth, characterCount);
}

int ScintillaCall::AutoCGetMaxWidth() {
	return static_cast<int>(Call(Message::AutoCGetMaxWidth));
}

void ScintillaCall::AutoCSetMaxHeight(int rowCount) {
	Call(Message::AutoCSetMaxHeight, rowCount);
}

int ScintillaCall::AutoCGetMaxHeight() {
	return static_cast<int>(Call(Message::AutoCGetMaxHeight));
}

void ScintillaCall::AutoCSetStyle(int style) {
	Call(Message::AutoCSetStyle, style);
}

int ScintillaCall::AutoCGetStyle() {
	return static_cast<int>(Call(Message::AutoCGetStyle));
}

void ScintillaCall::SetIndent(int indentSize) {
	Call(Message::SetIndent, indentSize);
}

int ScintillaCall::Indent() {
	return static_cast<int>(Call(Message::GetIndent));
}

void ScintillaCall::SetUseTabs(bool useTabs) {
	Call(Message::SetUseTabs, useTabs);
}

bool ScintillaCall::UseTabs() {
	return Call(Message::GetUseTabs);
}

void ScintillaCall::SetLineIndentation(Line line, int indentation) {
	Call(Message::SetLineIndentation, line, indentation);
}

int ScintillaCall::LineIndentation(Line line) {
	return static_cast<int>(Call(Message::GetLineIndentation, line));
}

Position ScintillaCall::LineIndentPosition(Line line) {
	return Call(Message::GetLineIndentPosition, line);
}

Position ScintillaCall::Column(Position pos) {
	return Call(Message::GetColumn, pos);
}

Position ScintillaCall::CountCharacters(Position start, Position end) {
	return Call(Message::CountCharacters, start, end);
}

Position ScintillaCall::CountCodeUnits(Position start, Position end) {
	return Call(Message::CountCodeUnits, start, end);
}

void ScintillaCall::SetHScrollBar(bool visible) {
	Call(Message::SetHScrollBar, visible);
}

bool ScintillaCall::HScrollBar() {
	return Call(Message::GetHScrollBar);
}

void ScintillaCall::SetIndentationGuides(Scintilla::IndentView indentView) {
	Call(Message::SetIndentationGuides, static_cast<uintptr_t>(indentView));
}

IndentView ScintillaCall::IndentationGuides() {
	return static_cast<Scintilla::IndentView>(Call(Message::GetIndentationGuides));
}

void ScintillaCall::SetHighlightGuide(Position column) {
	Call(Message::SetHighlightGuide, column);
}

Position ScintillaCall::HighlightGuide() {
	return Call(Message::GetHighlightGuide);
}

Position ScintillaCall::LineEndPosition(Line line) {
	return Call(Message::GetLineEndPosition, line);
}

int ScintillaCall::CodePage() {
	return static_cast<int>(Call(Message::GetCodePage));
}

Colour ScintillaCall::CaretFore() {
	return static_cast<Colour>(Call(Message::GetCaretFore));
}

bool ScintillaCall::ReadOnly() {
	return Call(Message::GetReadOnly);
}

void ScintillaCall::SetCurrentPos(Position caret) {
	Call(Message::SetCurrentPos, caret);
}

void ScintillaCall::SetSelectionStart(Position anchor) {
	Call(Message::SetSelectionStart, anchor);
}

Position ScintillaCall::SelectionStart() {
	return Call(Message::GetSelectionStart);
}

void ScintillaCall::SetSelectionEnd(Position caret) {
	Call(Message::SetSelectionEnd, caret);
}

Position ScintillaCall::SelectionEnd() {
	return Call(Message::GetSelectionEnd);
}

void ScintillaCall::SetEmptySelection(Position caret) {
	Call(Message::SetEmptySelection, caret);
}

void ScintillaCall::SetPrintMagnification(int magnification) {
	Call(Message::SetPrintMagnification, magnification);
}

int ScintillaCall::PrintMagnification() {
	return static_cast<int>(Call(Message::GetPrintMagnification));
}

void ScintillaCall::SetPrintColourMode(Scintilla::PrintOption mode) {
	Call(Message::SetPrintColourMode, static_cast<uintptr_t>(mode));
}

PrintOption ScintillaCall::PrintColourMode() {
	return static_cast<Scintilla::PrintOption>(Call(Message::GetPrintColourMode));
}

Position ScintillaCall::FindText(Scintilla::FindOption searchFlags, void *ft) {
	return CallPointer(Message::FindText, static_cast<uintptr_t>(searchFlags), ft);
}

Position ScintillaCall::FindTextFull(Scintilla::FindOption searchFlags, TextToFindFull *ft) {
	return CallPointer(Message::FindTextFull, static_cast<uintptr_t>(searchFlags), ft);
}

Position ScintillaCall::FormatRange(bool draw, void *fr) {
	return CallPointer(Message::FormatRange, draw, fr);
}

Position ScintillaCall::FormatRangeFull(bool draw, RangeToFormatFull *fr) {
	return CallPointer(Message::FormatRangeFull, draw, fr);
}

void ScintillaCall::SetChangeHistory(Scintilla::ChangeHistoryOption changeHistory) {
	Call(Message::SetChangeHistory, static_cast<uintptr_t>(changeHistory));
}

ChangeHistoryOption ScintillaCall::ChangeHistory() {
	return static_cast<Scintilla::ChangeHistoryOption>(Call(Message::GetChangeHistory));
}

void ScintillaCall::SetUndoSelectionHistory(Scintilla::UndoSelectionHistoryOption undoSelectionHistory) {
	Call(Message::SetUndoSelectionHistory, static_cast<uintptr_t>(undoSelectionHistory));
}

UndoSelectionHistoryOption ScintillaCall::UndoSelectionHistory() {
	return static_cast<Scintilla::UndoSelectionHistoryOption>(Call(Message::GetUndoSelectionHistory));
}

void ScintillaCall::SetSelectionSerialized(const char *selectionString) {
	CallString(Message::SetSelectionSerialized, 0, selectionString);
}

Position ScintillaCall::SelectionSerialized(char *selectionString) {
	return CallPointer(Message::GetSelectionSerialized, 0, selectionString);
}

std::string ScintillaCall::SelectionSerialized() {
	return CallReturnString(Message::GetSelectionSerialized, 0);
}

Line ScintillaCall::FirstVisibleLine() {
	return Call(Message::GetFirstVisibleLine);
}

Position ScintillaCall::GetLine(Line line, char *text) {
	return CallPointer(Message::GetLine, line, text);
}

std::string ScintillaCall::GetLine(Line line) {
	return CallReturnString(Message::GetLine, line);
}

Line ScintillaCall::LineCount() {
	return Call(Message::GetLineCount);
}

void ScintillaCall::AllocateLines(Line lines) {
	Call(Message::AllocateLines, lines);
}

void ScintillaCall::SetMarginLeft(int pixelWidth) {
	Call(Message::SetMarginLeft, 0, pixelWidth);
}

int ScintillaCall::MarginLeft() {
	return static_cast<int>(Call(Message::GetMarginLeft));
}

void ScintillaCall::SetMarginRight(int pixelWidth) {
	Call(Message::SetMarginRight, 0, pixelWidth);
}

int ScintillaCall::MarginRight() {
	return static_cast<int>(Call(Message::GetMarginRight));
}

bool ScintillaCall::Modify() {
	return Call(Message::GetModify);
}

void ScintillaCall::SetSel(Position anchor, Position caret) {
	Call(Message::SetSel, anchor, caret);
}

Position ScintillaCall::GetSelText(char *text) {
	return CallPointer(Message::GetSelText, 0, text);
}

std::string ScintillaCall::GetSelText() {
	return CallReturnString(Message::GetSelText, 0);
}

Position ScintillaCall::GetTextRange(void *tr) {
	return CallPointer(Message::GetTextRange, 0, tr);
}

Position ScintillaCall::GetTextRangeFull(TextRangeFull *tr) {
	return CallPointer(Message::GetTextRangeFull, 0, tr);
}

void ScintillaCall::HideSelection(bool hide) {
	Call(Message::HideSelection, hide);
}

bool ScintillaCall::SelectionHidden() {
	return Call(Message::GetSelectionHidden);
}

int ScintillaCall::PointXFromPosition(Position pos) {
	return static_cast<int>(Call(Message::PointXFromPosition, 0, pos));
}

int ScintillaCall::PointYFromPosition(Position pos) {
	return static_cast<int>(Call(Message::PointYFromPosition, 0, pos));
}

Line ScintillaCall::LineFromPosition(Position pos) {
	return Call(Message::LineFromPosition, pos);
}

Position ScintillaCall::PositionFromLine(Line line) {
	return Call(Message::PositionFromLine, line);
}

void ScintillaCall::LineScroll(Position columns, Line lines) {
	Call(Message::LineScroll, columns, lines);
}

void ScintillaCall::ScrollCaret() {
	Call(Message::ScrollCaret);
}

void ScintillaCall::ScrollRange(Position secondary, Position primary) {
	Call(Message::ScrollRange, secondary, primary);
}

void ScintillaCall::ReplaceSel(const char *text) {
	CallString(Message::ReplaceSel, 0, text);
}

void ScintillaCall::SetReadOnly(bool readOnly) {
	Call(Message::SetReadOnly, readOnly);
}

void ScintillaCall::Null() {
	Call(Message::Null);
}

bool ScintillaCall::CanPaste() {
	return Call(Message::CanPaste);
}

bool ScintillaCall::CanUndo() {
	return Call(Message::CanUndo);
}

void ScintillaCall::EmptyUndoBuffer() {
	Call(Message::EmptyUndoBuffer);
}

void ScintillaCall::Undo() {
	Call(Message::Undo);
}

void ScintillaCall::Cut() {
	Call(Message::Cut);
}

void ScintillaCall::Copy() {
	Call(Message::Copy);
}

void ScintillaCall::Paste() {
	Call(Message::Paste);
}

void ScintillaCall::Clear() {
	Call(Message::Clear);
}

void ScintillaCall::SetText(const char *text) {
	CallString(Message::SetText, 0, text);
}

Position ScintillaCall::GetText(Position length, char *text) {
	return CallPointer(Message::GetText, length, text);
}

std::string ScintillaCall::GetText(Position length) {
	return CallReturnString(Message::GetText, length);
}

Position ScintillaCall::TextLength() {
	return Call(Message::GetTextLength);
}

void *ScintillaCall::DirectFunction() {
	return reinterpret_cast<void *>(Call(Message::GetDirectFunction));
}

void *ScintillaCall::DirectStatusFunction() {
	return reinterpret_cast<void *>(Call(Message::GetDirectStatusFunction));
}

void *ScintillaCall::DirectPointer() {
	return reinterpret_cast<void *>(Call(Message::GetDirectPointer));
}

void ScintillaCall::SetOvertype(bool overType) {
	Call(Message::SetOvertype, overType);
}

bool ScintillaCall::Overtype() {
	return Call(Message::GetOvertype);
}

void ScintillaCall::SetCaretWidth(int pixelWidth) {
	Call(Message::SetCaretWidth, pixelWidth);
}

int ScintillaCall::CaretWidth() {
	return static_cast<int>(Call(Message::GetCaretWidth));
}

void ScintillaCall::SetTargetStart(Position start) {
	Call(Message::SetTargetStart, start);
}

Position ScintillaCall::TargetStart() {
	return Call(Message::GetTargetStart);
}

void ScintillaCall::SetTargetStartVirtualSpace(Position space) {
	Call(Message::SetTargetStartVirtualSpace, space);
}

Position ScintillaCall::TargetStartVirtualSpace() {
	return Call(Message::GetTargetStartVirtualSpace);
}

void ScintillaCall::SetTargetEnd(Position end) {
	Call(Message::SetTargetEnd, end);
}

Position ScintillaCall::TargetEnd() {
	return Call(Message::GetTargetEnd);
}

void ScintillaCall::SetTargetEndVirtualSpace(Position space) {
	Call(Message::SetTargetEndVirtualSpace, space);
}

Position ScintillaCall::TargetEndVirtualSpace() {
	return Call(Message::GetTargetEndVirtualSpace);
}

void ScintillaCall::SetTargetRange(Position start, Position end) {
	Call(Message::SetTargetRange, start, end);
}

Position ScintillaCall::TargetText(char *text) {
	return CallPointer(Message::GetTargetText, 0, text);
}

std::string ScintillaCall::TargetText() {
	return CallReturnString(Message::GetTargetText, 0);
}

void ScintillaCall::TargetFromSelection() {
	Call(Message::TargetFromSelection);
}

void ScintillaCall::TargetWholeDocument() {
	Call(Message::TargetWholeDocument);
}

Position ScintillaCall::ReplaceTarget(Position length, const char *text) {
	return CallString(Message::ReplaceTarget, length, text);
}

Position ScintillaCall::ReplaceTargetRE(Position length, const char *text) {
	return CallString(Message::ReplaceTargetRE, length, text);
}

Position ScintillaCall::ReplaceTargetMinimal(Position length, const char *text) {
	return CallString(Message::ReplaceTargetMinimal, length, text);
}

Position ScintillaCall::SearchInTarget(Position length, const char *text) {
	return CallString(Message::SearchInTarget, length, text);
}

void ScintillaCall::SetSearchFlags(Scintilla::FindOption searchFlags) {
	Call(Message::SetSearchFlags, static_cast<uintptr_t>(searchFlags));
}

FindOption ScintillaCall::SearchFlags() {
	return static_cast<Scintilla::FindOption>(Call(Message::GetSearchFlags));
}

void ScintillaCall::CallTipShow(Position pos, const char *definition) {
	CallString(Message::CallTipShow, pos, definition);
}

void ScintillaCall::CallTipCancel() {
	Call(Message::CallTipCancel);
}

bool ScintillaCall::CallTipActive() {
	return Call(Message::CallTipActive);
}

Position ScintillaCall::CallTipPosStart() {
	return Call(Message::CallTipPosStart);
}

void ScintillaCall::CallTipSetPosStart(Position posStart) {
	Call(Message::CallTipSetPosStart, posStart);
}

void ScintillaCall::CallTipSetHlt(Position highlightStart, Position highlightEnd) {
	Call(Message::CallTipSetHlt, highlightStart, highlightEnd);
}

void ScintillaCall::CallTipSetBack(Colour back) {
	Call(Message::CallTipSetBack, back);
}

void ScintillaCall::CallTipSetFore(Colour fore) {
	Call(Message::CallTipSetFore, fore);
}

void ScintillaCall::CallTipSetForeHlt(Colour fore) {
	Call(Message::CallTipSetForeHlt, fore);
}

void ScintillaCall::CallTipUseStyle(int tabSize) {
	Call(Message::CallTipUseStyle, tabSize);
}

void ScintillaCall::CallTipSetPosition(bool above) {
	Call(Message::CallTipSetPosition, above);
}

Line ScintillaCall::VisibleFromDocLine(Line docLine) {
	return Call(Message::VisibleFromDocLine, docLine);
}

Line ScintillaCall::DocLineFromVisible(Line displayLine) {
	return Call(Message::DocLineFromVisible, displayLine);
}

Line ScintillaCall::WrapCount(Line docLine) {
	return Call(Message::WrapCount, docLine);
}

void ScintillaCall::SetFoldLevel(Line line, Scintilla::FoldLevel level) {
	Call(Message::SetFoldLevel, line, static_cast<intptr_t>(level));
}

FoldLevel ScintillaCall::FoldLevel(Line line) {
	return static_cast<Scintilla::FoldLevel>(Call(Message::GetFoldLevel, line));
}

Line ScintillaCall::LastChild(Line line, Scintilla::FoldLevel level) {
	return Call(Message::GetLastChild, line, static_cast<intptr_t>(level));
}

Line ScintillaCall::FoldParent(Line line) {
	return Call(Message::GetFoldParent, line);
}

void ScintillaCall::ShowLines(Line lineStart, Line lineEnd) {
	Call(Message::ShowLines, lineStart, lineEnd);
}

void ScintillaCall::HideLines(Line lineStart, Line lineEnd) {
	Call(Message::HideLines, lineStart, lineEnd);
}

bool ScintillaCall::LineVisible(Line line) {
	return Call(Message::GetLineVisible, line);
}

bool ScintillaCall::AllLinesVisible() {
	return Call(Message::GetAllLinesVisible);
}

void ScintillaCall::SetFoldExpanded(Line line, bool expanded) {
	Call(Message::SetFoldExpanded, line, expanded);
}

bool ScintillaCall::FoldExpanded(Line line) {
	return Call(Message::GetFoldExpanded, line);
}

void ScintillaCall::ToggleFold(Line line) {
	Call(Message::ToggleFold, line);
}

void ScintillaCall::ToggleFoldShowText(Line line, const char *text) {
	CallString(Message::ToggleFoldShowText, line, text);
}

void ScintillaCall::FoldDisplayTextSetStyle(Scintilla::FoldDisplayTextStyle style) {
	Call(Message::FoldDisplayTextSetStyle, static_cast<uintptr_t>(style));
}

FoldDisplayTextStyle ScintillaCall::FoldDisplayTextGetStyle() {
	return static_cast<Scintilla::FoldDisplayTextStyle>(Call(Message::FoldDisplayTextGetStyle));
}

void ScintillaCall::SetDefaultFoldDisplayText(const char *text) {
	CallString(Message::SetDefaultFoldDisplayText, 0, text);
}

int ScintillaCall::GetDefaultFoldDisplayText(char *text) {
	return static_cast<int>(CallPointer(Message::GetDefaultFoldDisplayText, 0, text));
}

std::string ScintillaCall::GetDefaultFoldDisplayText() {
	return CallReturnString(Message::GetDefaultFoldDisplayText, 0);
}

void ScintillaCall::FoldLine(Line line, Scintilla::FoldAction action) {
	Call(Message::FoldLine, line, static_cast<intptr_t>(action));
}

void ScintillaCall::FoldChildren(Line line, Scintilla::FoldAction action) {
	Call(Message::FoldChildren, line, static_cast<intptr_t>(action));
}

void ScintillaCall::ExpandChildren(Line line, Scintilla::FoldLevel level) {
	Call(Message::ExpandChildren, line, static_cast<intptr_t>(level));
}

void ScintillaCall::FoldAll(Scintilla::FoldAction action) {
	Call(Message::FoldAll, static_cast<uintptr_t>(action));
}

void ScintillaCall::EnsureVisible(Line line) {
	Call(Message::EnsureVisible, line);
}

void ScintillaCall::SetAutomaticFold(Scintilla::AutomaticFold automaticFold) {
	Call(Message::SetAutomaticFold, static_cast<uintptr_t>(automaticFold));
}

AutomaticFold ScintillaCall::AutomaticFold() {
	return static_cast<Scintilla::AutomaticFold>(Call(Message::GetAutomaticFold));
}

void ScintillaCall::SetFoldFlags(Scintilla::FoldFlag flags) {
	Call(Message::SetFoldFlags, static_cast<uintptr_t>(flags));
}

void ScintillaCall::EnsureVisibleEnforcePolicy(Line line) {
	Call(Message::EnsureVisibleEnforcePolicy, line);
}

void ScintillaCall::SetTabIndents(bool tabIndents) {
	Call(Message::SetTabIndents, tabIndents);
}

bool ScintillaCall::TabIndents() {
	return Call(Message::GetTabIndents);
}

void ScintillaCall::SetBackSpaceUnIndents(bool bsUnIndents) {
	Call(Message::SetBackSpaceUnIndents, bsUnIndents);
}

bool ScintillaCall::BackSpaceUnIndents() {
	return Call(Message::GetBackSpaceUnIndents);
}

void ScintillaCall::SetMouseDwellTime(int periodMilliseconds) {
	Call(Message::SetMouseDwellTime, periodMilliseconds);
}

int ScintillaCall::MouseDwellTime() {
	return static_cast<int>(Call(Message::GetMouseDwellTime));
}

Position ScintillaCall::WordStartPosition(Position pos, bool onlyWordCharacters) {
	return Call(Message::WordStartPosition, pos, onlyWordCharacters);
}

Position ScintillaCall::WordEndPosition(Position pos, bool onlyWordCharacters) {
	return Call(Message::WordEndPosition, pos, onlyWordCharacters);
}

bool ScintillaCall::IsRangeWord(Position start, Position end) {
	return Call(Message::IsRangeWord, start, end);
}

void ScintillaCall::SetIdleStyling(Scintilla::IdleStyling idleStyling) {
	Call(Message::SetIdleStyling, static_cast<uintptr_t>(idleStyling));
}

IdleStyling ScintillaCall::IdleStyling() {
	return static_cast<Scintilla::IdleStyling>(Call(Message::GetIdleStyling));
}

void ScintillaCall::SetWrapMode(Scintilla::Wrap wrapMode) {
	Call(Message::SetWrapMode, static_cast<uintptr_t>(wrapMode));
}

Wrap ScintillaCall::WrapMode() {
	return static_cast<Scintilla::Wrap>(Call(Message::GetWrapMode));
}

void ScintillaCall::SetWrapVisualFlags(Scintilla::WrapVisualFlag wrapVisualFlags) {
	Call(Message::SetWrapVisualFlags, static_cast<uintptr_t>(wrapVisualFlags));
}

WrapVisualFlag ScintillaCall::WrapVisualFlags() {
	return static_cast<Scintilla::WrapVisualFlag>(Call(Message::GetWrapVisualFlags));
}

void ScintillaCall::SetWrapVisualFlagsLocation(Scintilla::WrapVisualLocation wrapVisualFlagsLocation) {
	Call(Message::SetWrapVisualFlagsLocation, static_cast<uintptr_t>(wrapVisualFlagsLocation));
}

WrapVisualLocation ScintillaCall::WrapVisualFlagsLocation() {
	return static_cast<Scintilla::WrapVisualLocation>(Call(Message::GetWrapVisualFlagsLocation));
}

void ScintillaCall::SetWrapStartIndent(int indent) {
	Call(Message::SetWrapStartIndent, indent);
}

int ScintillaCall::WrapStartIndent() {
	return static_cast<int>(Call(Message::GetWrapStartIndent));
}

void ScintillaCall::SetWrapIndentMode(Scintilla::WrapIndentMode wrapIndentMode) {
	Call(Message::SetWrapIndentMode, static_cast<uintptr_t>(wrapIndentMode));
}

WrapIndentMode ScintillaCall::WrapIndentMode() {
	return static_cast<Scintilla::WrapIndentMode>(Call(Message::GetWrapIndentMode));
}

void ScintillaCall::SetLayoutCache(Scintilla::LineCache cacheMode) {
	Call(Message::SetLayoutCache, static_cast<uintptr_t>(cacheMode));
}

LineCache ScintillaCall::LayoutCache() {
	return static_cast<Scintilla::LineCache>(Call(Message::GetLayoutCache));
}

void ScintillaCall::SetScrollWidth(int pixelWidth) {
	Call(Message::SetScrollWidth, pixelWidth);
}

int ScintillaCall::ScrollWidth() {
	return static_cast<int>(Call(Message::GetScrollWidth));
}

void ScintillaCall::SetScrollWidthTracking(bool tracking) {
	Call(Message::SetScrollWidthTracking, tracking);
}

bool ScintillaCall::ScrollWidthTracking() {
	return Call(Message::GetScrollWidthTracking);
}

int ScintillaCall::TextWidth(int style, const char *text) {
	return static_cast<int>(CallString(Message::TextWidth, style, text));
}

void ScintillaCall::SetEndAtLastLine(bool endAtLastLine) {
	Call(Message::SetEndAtLastLine, endAtLastLine);
}

bool ScintillaCall::EndAtLastLine() {
	return Call(Message::GetEndAtLastLine);
}

int ScintillaCall::TextHeight(Line line) {
	return static_cast<int>(Call(Message::TextHeight, line));
}

void ScintillaCall::SetVScrollBar(bool visible) {
	Call(Message::SetVScrollBar, visible);
}

bool ScintillaCall::VScrollBar() {
	return Call(Message::GetVScrollBar);
}

void ScintillaCall::AppendText(Position length, const char *text) {
	CallString(Message::AppendText, length, text);
}

PhasesDraw ScintillaCall::PhasesDraw() {
	return static_cast<Scintilla::PhasesDraw>(Call(Message::GetPhasesDraw));
}

void ScintillaCall::SetPhasesDraw(Scintilla::PhasesDraw phases) {
	Call(Message::SetPhasesDraw, static_cast<uintptr_t>(phases));
}

void ScintillaCall::SetFontQuality(Scintilla::FontQuality fontQuality) {
	Call(Message::SetFontQuality, static_cast<uintptr_t>(fontQuality));
}

FontQuality ScintillaCall::FontQuality() {
	return static_cast<Scintilla::FontQuality>(Call(Message::GetFontQuality));
}

void ScintillaCall::SetFirstVisibleLine(Line displayLine) {
	Call(Message::SetFirstVisibleLine, displayLine);
}

void ScintillaCall::SetMultiPaste(Scintilla::MultiPaste multiPaste) {
	Call(Message::SetMultiPaste, static_cast<uintptr_t>(multiPaste));
}

MultiPaste ScintillaCall::MultiPaste() {
	return static_cast<Scintilla::MultiPaste>(Call(Message::GetMultiPaste));
}

int ScintillaCall::Tag(int tagNumber, char *tagValue) {
	return static_cast<int>(CallPointer(Message::GetTag, tagNumber, tagValue));
}

std::string ScintillaCall::Tag(int tagNumber) {
	return CallReturnString(Message::GetTag, tagNumber);
}

void ScintillaCall::LinesJoin() {
	Call(Message::LinesJoin);
}

void ScintillaCall::LinesSplit(int pixelWidth) {
	Call(Message::LinesSplit, pixelWidth);
}

void ScintillaCall::SetFoldMarginColour(bool useSetting, Colour back) {
	Call(Message::SetFoldMarginColour, useSetting, back);
}

void ScintillaCall::SetFoldMarginHiColour(bool useSetting, Colour fore) {
	Call(Message::SetFoldMarginHiColour, useSetting, fore);
}

void ScintillaCall::SetAccessibility(Scintilla::Accessibility accessibility) {
	Call(Message::SetAccessibility, static_cast<uintptr_t>(accessibility));
}

Accessibility ScintillaCall::Accessibility() {
	return static_cast<Scintilla::Accessibility>(Call(Message::GetAccessibility));
}

void ScintillaCall::LineDown() {
	Call(Message::LineDown);
}

void ScintillaCall::LineDownExtend() {
	Call(Message::LineDownExtend);
}

void ScintillaCall::LineUp() {
	Call(Message::LineUp);
}

void ScintillaCall::LineUpExtend() {
	Call(Message::LineUpExtend);
}

void ScintillaCall::CharLeft() {
	Call(Message::CharLeft);
}

void ScintillaCall::CharLeftExtend() {
	Call(Message::CharLeftExtend);
}

void ScintillaCall::CharRight() {
	Call(Message::CharRight);
}

void ScintillaCall::CharRightExtend() {
	Call(Message::CharRightExtend);
}

void ScintillaCall::WordLeft() {
	Call(Message::WordLeft);
}

void ScintillaCall::WordLeftExtend() {
	Call(Message::WordLeftExtend);
}

void ScintillaCall::WordRight() {
	Call(Message::WordRight);
}

void ScintillaCall::WordRightExtend() {
	Call(Message::WordRightExtend);
}

void ScintillaCall::Home() {
	Call(Message::Home);
}

void ScintillaCall::HomeExtend() {
	Call(Message::HomeExtend);
}

void ScintillaCall::LineEnd() {
	Call(Message::LineEnd);
}

void ScintillaCall::LineEndExtend() {
	Call(Message::LineEndExtend);
}

void ScintillaCall::DocumentStart() {
	Call(Message::DocumentStart);
}

void ScintillaCall::DocumentStartExtend() {
	Call(Message::DocumentStartExtend);
}

void ScintillaCall::DocumentEnd() {
	Call(Message::DocumentEnd);
}

void ScintillaCall::DocumentEndExtend() {
	Call(Message::DocumentEndExtend);
}

void ScintillaCall::PageUp() {
	Call(Message::PageUp);
}

void ScintillaCall::PageUpExtend() {
	Call(Message::PageUpExtend);
}

void ScintillaCall::PageDown() {
	Call(Message::PageDown);
}

void ScintillaCall::PageDownExtend() {
	Call(Message::PageDownExtend);
}

void ScintillaCall::EditToggleOvertype() {
	Call(Message::EditToggleOvertype);
}

void ScintillaCall::Cancel() {
	Call(Message::Cancel);
}

void ScintillaCall::DeleteBack() {
	Call(Message::DeleteBack);
}

void ScintillaCall::Tab() {
	Call(Message::Tab);
}

void ScintillaCall::LineIndent() {
	Call(Message::LineIndent);
}

void ScintillaCall::BackTab() {
	Call(Message::BackTab);
}

void ScintillaCall::LineDedent() {
	Call(Message::LineDedent);
}

void ScintillaCall::NewLine() {
	Call(Message::NewLine);
}

void ScintillaCall::FormFeed() {
	Call(Message::FormFeed);
}

void ScintillaCall::VCHome() {
	Call(Message::VCHome);
}

void ScintillaCall::VCHomeExtend() {
	Call(Message::VCHomeExtend);
}

void ScintillaCall::ZoomIn() {
	Call(Message::ZoomIn);
}

void ScintillaCall::ZoomOut() {
	Call(Message::ZoomOut);
}

void ScintillaCall::DelWordLeft() {
	Call(Message::DelWordLeft);
}

void ScintillaCall::DelWordRight() {
	Call(Message::DelWordRight);
}

void ScintillaCall::DelWordRightEnd() {
	Call(Message::DelWordRightEnd);
}

void ScintillaCall::LineCut() {
	Call(Message::LineCut);
}

void ScintillaCall::LineDelete() {
	Call(Message::LineDelete);
}

void ScintillaCall::LineTranspose() {
	Call(Message::LineTranspose);
}

void ScintillaCall::LineReverse() {
	Call(Message::LineReverse);
}

void ScintillaCall::LineDuplicate() {
	Call(Message::LineDuplicate);
}

void ScintillaCall::LowerCase() {
	Call(Message::LowerCase);
}

void ScintillaCall::UpperCase() {
	Call(Message::UpperCase);
}

void ScintillaCall::LineScrollDown() {
	Call(Message::LineScrollDown);
}

void ScintillaCall::LineScrollUp() {
	Call(Message::LineScrollUp);
}

void ScintillaCall::DeleteBackNotLine() {
	Call(Message::DeleteBackNotLine);
}

void ScintillaCall::HomeDisplay() {
	Call(Message::HomeDisplay);
}

void ScintillaCall::HomeDisplayExtend() {
	Call(Message::HomeDisplayExtend);
}

void ScintillaCall::LineEndDisplay() {
	Call(Message::LineEndDisplay);
}

void ScintillaCall::LineEndDisplayExtend() {
	Call(Message::LineEndDisplayExtend);
}

void ScintillaCall::HomeWrap() {
	Call(Message::HomeWrap);
}

void ScintillaCall::HomeWrapExtend() {
	Call(Message::HomeWrapExtend);
}

void ScintillaCall::LineEndWrap() {
	Call(Message::LineEndWrap);
}

void ScintillaCall::LineEndWrapExtend() {
	Call(Message::LineEndWrapExtend);
}

void ScintillaCall::VCHomeWrap() {
	Call(Message::VCHomeWrap);
}

void ScintillaCall::VCHomeWrapExtend() {
	Call(Message::VCHomeWrapExtend);
}

void ScintillaCall::LineCopy() {
	Call(Message::LineCopy);
}

void ScintillaCall::MoveCaretInsideView() {
	Call(Message::MoveCaretInsideView);
}

Position ScintillaCall::LineLength(Line line) {
	return Call(Message::LineLength, line);
}

void ScintillaCall::BraceHighlight(Position posA, Position posB) {
	Call(Message::BraceHighlight, posA, posB);
}

void ScintillaCall::BraceHighlightIndicator(bool useSetting, int indicator) {
	Call(Message::BraceHighlightIndicator, useSetting, indicator);
}

void ScintillaCall::BraceBadLight(Position pos) {
	Call(Message::BraceBadLight, pos);
}

void ScintillaCall::BraceBadLightIndicator(bool useSetting, int indicator) {
	Call(Message::BraceBadLightIndicator, useSetting, indicator);
}

Position ScintillaCall::BraceMatch(Position pos, int maxReStyle) {
	return Call(Message::BraceMatch, pos, maxReStyle);
}

Position ScintillaCall::BraceMatchNext(Position pos, Position startPos) {
	return Call(Message::BraceMatchNext, pos, startPos);
}

bool ScintillaCall::ViewEOL() {
	return Call(Message::GetViewEOL);
}

void ScintillaCall::SetViewEOL(bool visible) {
	Call(Message::SetViewEOL, visible);
}

IDocumentEditable *ScintillaCall::DocPointer() {
	return reinterpret_cast<IDocumentEditable *>(Call(Message::GetDocPointer));
}

void ScintillaCall::SetDocPointer(IDocumentEditable *doc) {
	CallPointer(Message::SetDocPointer, 0, doc);
}

void ScintillaCall::SetModEventMask(Scintilla::ModificationFlags eventMask) {
	Call(Message::SetModEventMask, static_cast<uintptr_t>(eventMask));
}

Position ScintillaCall::EdgeColumn() {
	return Call(Message::GetEdgeColumn);
}

void ScintillaCall::SetEdgeColumn(Position column) {
	Call(Message::SetEdgeColumn, column);
}

EdgeVisualStyle ScintillaCall::EdgeMode() {
	return static_cast<Scintilla::EdgeVisualStyle>(Call(Message::GetEdgeMode));
}

void ScintillaCall::SetEdgeMode(Scintilla::EdgeVisualStyle edgeMode) {
	Call(Message::SetEdgeMode, static_cast<uintptr_t>(edgeMode));
}

Colour ScintillaCall::EdgeColour() {
	return static_cast<Colour>(Call(Message::GetEdgeColour));
}

void ScintillaCall::SetEdgeColour(Colour edgeColour) {
	Call(Message::SetEdgeColour, edgeColour);
}

void ScintillaCall::MultiEdgeAddLine(Position column, Colour edgeColour) {
	Call(Message::MultiEdgeAddLine, column, edgeColour);
}

void ScintillaCall::MultiEdgeClearAll() {
	Call(Message::MultiEdgeClearAll);
}

Position ScintillaCall::MultiEdgeColumn(int which) {
	return Call(Message::GetMultiEdgeColumn, which);
}

void ScintillaCall::SearchAnchor() {
	Call(Message::SearchAnchor);
}

Position ScintillaCall::SearchNext(Scintilla::FindOption searchFlags, const char *text) {
	return CallString(Message::SearchNext, static_cast<uintptr_t>(searchFlags), text);
}

Position ScintillaCall::SearchPrev(Scintilla::FindOption searchFlags, const char *text) {
	return CallString(Message::SearchPrev, static_cast<uintptr_t>(searchFlags), text);
}

Line ScintillaCall::LinesOnScreen() {
	return Call(Message::LinesOnScreen);
}

void ScintillaCall::UsePopUp(Scintilla::PopUp popUpMode) {
	Call(Message::UsePopUp, static_cast<uintptr_t>(popUpMode));
}

bool ScintillaCall::SelectionIsRectangle() {
	return Call(Message::SelectionIsRectangle);
}

void ScintillaCall::SetZoom(int zoomInPoints) {
	Call(Message::SetZoom, zoomInPoints);
}

int ScintillaCall::Zoom() {
	return static_cast<int>(Call(Message::GetZoom));
}

IDocumentEditable *ScintillaCall::CreateDocument(Position bytes, Scintilla::DocumentOption documentOptions) {
	return reinterpret_cast<IDocumentEditable *>(Call(Message::CreateDocument, bytes, static_cast<intptr_t>(documentOptions)));
}

void ScintillaCall::AddRefDocument(IDocumentEditable *doc) {
	CallPointer(Message::AddRefDocument, 0, doc);
}

void ScintillaCall::ReleaseDocument(IDocumentEditable *doc) {
	CallPointer(Message::ReleaseDocument, 0, doc);
}

DocumentOption ScintillaCall::DocumentOptions() {
	return static_cast<Scintilla::DocumentOption>(Call(Message::GetDocumentOptions));
}

ModificationFlags ScintillaCall::ModEventMask() {
	return static_cast<Scintilla::ModificationFlags>(Call(Message::GetModEventMask));
}

void ScintillaCall::SetCommandEvents(bool commandEvents) {
	Call(Message::SetCommandEvents, commandEvents);
}

bool ScintillaCall::CommandEvents() {
	return Call(Message::GetCommandEvents);
}

void ScintillaCall::SetFocus(bool focus) {
	Call(Message::SetFocus, focus);
}

bool ScintillaCall::Focus() {
	return Call(Message::GetFocus);
}

void ScintillaCall::SetStatus(Scintilla::Status status) {
	Call(Message::SetStatus, static_cast<uintptr_t>(status));
}

Status ScintillaCall::Status() {
	return static_cast<Scintilla::Status>(Call(Message::GetStatus));
}

void ScintillaCall::SetMouseDownCaptures(bool captures) {
	Call(Message::SetMouseDownCaptures, captures);
}

bool ScintillaCall::MouseDownCaptures() {
	return Call(Message::GetMouseDownCaptures);
}

void ScintillaCall::SetMouseWheelCaptures(bool captures) {
	Call(Message::SetMouseWheelCaptures, captures);
}

bool ScintillaCall::MouseWheelCaptures() {
	return Call(Message::GetMouseWheelCaptures);
}

void ScintillaCall::SetCursor(Scintilla::CursorShape cursorType) {
	Call(Message::SetCursor, static_cast<uintptr_t>(cursorType));
}

CursorShape ScintillaCall::Cursor() {
	return static_cast<Scintilla::CursorShape>(Call(Message::GetCursor));
}

void ScintillaCall::SetControlCharSymbol(int symbol) {
	Call(Message::SetControlCharSymbol, symbol);
}

int ScintillaCall::ControlCharSymbol() {
	return static_cast<int>(Call(Message::GetControlCharSymbol));
}

void ScintillaCall::WordPartLeft() {
	Call(Message::WordPartLeft);
}

void ScintillaCall::WordPartLeftExtend() {
	Call(Message::WordPartLeftExtend);
}

void ScintillaCall::WordPartRight() {
	Call(Message::WordPartRight);
}

void ScintillaCall::WordPartRightExtend() {
	Call(Message::WordPartRightExtend);
}

void ScintillaCall::SetVisiblePolicy(Scintilla::VisiblePolicy visiblePolicy, int visibleSlop) {
	Call(Message::SetVisiblePolicy, static_cast<uintptr_t>(visiblePolicy), visibleSlop);
}

void ScintillaCall::DelLineLeft() {
	Call(Message::DelLineLeft);
}

void ScintillaCall::DelLineRight() {
	Call(Message::DelLineRight);
}

void ScintillaCall::SetXOffset(int xOffset) {
	Call(Message::SetXOffset, xOffset);
}

int ScintillaCall::XOffset() {
	return static_cast<int>(Call(Message::GetXOffset));
}

void ScintillaCall::ChooseCaretX() {
	Call(Message::ChooseCaretX);
}

void ScintillaCall::GrabFocus() {
	Call(Message::GrabFocus);
}

void ScintillaCall::SetXCaretPolicy(Scintilla::CaretPolicy caretPolicy, int caretSlop) {
	Call(Message::SetXCaretPolicy, static_cast<uintptr_t>(caretPolicy), caretSlop);
}

void ScintillaCall::SetYCaretPolicy(Scintilla::CaretPolicy caretPolicy, int caretSlop) {
	Call(Message::SetYCaretPolicy, static_cast<uintptr_t>(caretPolicy), caretSlop);
}

void ScintillaCall::SetPrintWrapMode(Scintilla::Wrap wrapMode) {
	Call(Message::SetPrintWrapMode, static_cast<uintptr_t>(wrapMode));
}

Wrap ScintillaCall::PrintWrapMode() {
	return static_cast<Scintilla::Wrap>(Call(Message::GetPrintWrapMode));
}

void ScintillaCall::SetHotspotActiveFore(bool useSetting, Colour fore) {
	Call(Message::SetHotspotActiveFore, useSetting, fore);
}

Colour ScintillaCall::HotspotActiveFore() {
	return static_cast<Colour>(Call(Message::GetHotspotActiveFore));
}

void ScintillaCall::SetHotspotActiveBack(bool useSetting, Colour back) {
	Call(Message::SetHotspotActiveBack, useSetting, back);
}

Colour ScintillaCall::HotspotActiveBack() {
	return static_cast<Colour>(Call(Message::GetHotspotActiveBack));
}

void ScintillaCall::SetHotspotActiveUnderline(bool underline) {
	Call(Message::SetHotspotActiveUnderline, underline);
}

bool ScintillaCall::HotspotActiveUnderline() {
	return Call(Message::GetHotspotActiveUnderline);
}

void ScintillaCall::SetHotspotSingleLine(bool singleLine) {
	Call(Message::SetHotspotSingleLine, singleLine);
}

bool ScintillaCall::HotspotSingleLine() {
	return Call(Message::GetHotspotSingleLine);
}

void ScintillaCall::ParaDown() {
	Call(Message::ParaDown);
}

void ScintillaCall::ParaDownExtend() {
	Call(Message::ParaDownExtend);
}

void ScintillaCall::ParaUp() {
	Call(Message::ParaUp);
}

void ScintillaCall::ParaUpExtend() {
	Call(Message::ParaUpExtend);
}

Position ScintillaCall::PositionBefore(Position pos) {
	return Call(Message::PositionBefore, pos);
}

Position ScintillaCall::PositionAfter(Position pos) {
	return Call(Message::PositionAfter, pos);
}

Position ScintillaCall::PositionRelative(Position pos, Position relative) {
	return Call(Message::PositionRelative, pos, relative);
}

Position ScintillaCall::PositionRelativeCodeUnits(Position pos, Position relative) {
	return Call(Message::PositionRelativeCodeUnits, pos, relative);
}

void ScintillaCall::CopyRange(Position start, Position end) {
	Call(Message::CopyRange, start, end);
}

void ScintillaCall::CopyText(Position length, const char *text) {
	CallString(Message::CopyText, length, text);
}

void ScintillaCall::SetSelectionMode(Scintilla::SelectionMode selectionMode) {
	Call(Message::SetSelectionMode, static_cast<uintptr_t>(selectionMode));
}

void ScintillaCall::ChangeSelectionMode(Scintilla::SelectionMode selectionMode) {
	Call(Message::ChangeSelectionMode, static_cast<uintptr_t>(selectionMode));
}

SelectionMode ScintillaCall::SelectionMode() {
	return static_cast<Scintilla::SelectionMode>(Call(Message::GetSelectionMode));
}

void ScintillaCall::SetMoveExtendsSelection(bool moveExtendsSelection) {
	Call(Message::SetMoveExtendsSelection, moveExtendsSelection);
}

bool ScintillaCall::MoveExtendsSelection() {
	return Call(Message::GetMoveExtendsSelection);
}

Position ScintillaCall::GetLineSelStartPosition(Line line) {
	return Call(Message::GetLineSelStartPosition, line);
}

Position ScintillaCall::GetLineSelEndPosition(Line line) {
	return Call(Message::GetLineSelEndPosition, line);
}

void ScintillaCall::LineDownRectExtend() {
	Call(Message::LineDownRectExtend);
}

void ScintillaCall::LineUpRectExtend() {
	Call(Message::LineUpRectExtend);
}

void ScintillaCall::CharLeftRectExtend() {
	Call(Message::CharLeftRectExtend);
}

void ScintillaCall::CharRightRectExtend() {
	Call(Message::CharRightRectExtend);
}

void ScintillaCall::HomeRectExtend() {
	Call(Message::HomeRectExtend);
}

void ScintillaCall::VCHomeRectExtend() {
	Call(Message::VCHomeRectExtend);
}

void ScintillaCall::LineEndRectExtend() {
	Call(Message::LineEndRectExtend);
}

void ScintillaCall::PageUpRectExtend() {
	Call(Message::PageUpRectExtend);
}

void ScintillaCall::PageDownRectExtend() {
	Call(Message::PageDownRectExtend);
}

void ScintillaCall::StutteredPageUp() {
	Call(Message::StutteredPageUp);
}

void ScintillaCall::StutteredPageUpExtend() {
	Call(Message::StutteredPageUpExtend);
}

void ScintillaCall::StutteredPageDown() {
	Call(Message::StutteredPageDown);
}

void ScintillaCall::StutteredPageDownExtend() {
	Call(Message::StutteredPageDownExtend);
}

void ScintillaCall::WordLeftEnd() {
	Call(Message::WordLeftEnd);
}

void ScintillaCall::WordLeftEndExtend() {
	Call(Message::WordLeftEndExtend);
}

void ScintillaCall::WordRightEnd() {
	Call(Message::WordRightEnd);
}

void ScintillaCall::WordRightEndExtend() {
	Call(Message::WordRightEndExtend);
}

void ScintillaCall::SetWhitespaceChars(const char *characters) {
	CallString(Message::SetWhitespaceChars, 0, characters);
}

int ScintillaCall::WhitespaceChars(char *characters) {
	return static_cast<int>(CallPointer(Message::GetWhitespaceChars, 0, characters));
}

std::string ScintillaCall::WhitespaceChars() {
	return CallReturnString(Message::GetWhitespaceChars, 0);
}

void ScintillaCall::SetPunctuationChars(const char *characters) {
	CallString(Message::SetPunctuationChars, 0, characters);
}

int ScintillaCall::PunctuationChars(char *characters) {
	return static_cast<int>(CallPointer(Message::GetPunctuationChars, 0, characters));
}

std::string ScintillaCall::PunctuationChars() {
	return CallReturnString(Message::GetPunctuationChars, 0);
}

void ScintillaCall::SetCharsDefault() {
	Call(Message::SetCharsDefault);
}

int ScintillaCall::AutoCGetCurrent() {
	return static_cast<int>(Call(Message::AutoCGetCurrent));
}

int ScintillaCall::AutoCGetCurrentText(char *text) {
	return static_cast<int>(CallPointer(Message::AutoCGetCurrentText, 0, text));
}

std::string ScintillaCall::AutoCGetCurrentText() {
	return CallReturnString(Message::AutoCGetCurrentText, 0);
}

void ScintillaCall::AutoCSetCaseInsensitiveBehaviour(Scintilla::CaseInsensitiveBehaviour behaviour) {
	Call(Message::AutoCSetCaseInsensitiveBehaviour, static_cast<uintptr_t>(behaviour));
}

CaseInsensitiveBehaviour ScintillaCall::AutoCGetCaseInsensitiveBehaviour() {
	return static_cast<Scintilla::CaseInsensitiveBehaviour>(Call(Message::AutoCGetCaseInsensitiveBehaviour));
}

void ScintillaCall::AutoCSetMulti(Scintilla::MultiAutoComplete multi) {
	Call(Message::AutoCSetMulti, static_cast<uintptr_t>(multi));
}

MultiAutoComplete ScintillaCall::AutoCGetMulti() {
	return static_cast<Scintilla::MultiAutoComplete>(Call(Message::AutoCGetMulti));
}

void ScintillaCall::AutoCSetOrder(Scintilla::Ordering order) {
	Call(Message::AutoCSetOrder, static_cast<uintptr_t>(order));
}

Ordering ScintillaCall::AutoCGetOrder() {
	return static_cast<Scintilla::Ordering>(Call(Message::AutoCGetOrder));
}

void ScintillaCall::Allocate(Position bytes) {
	Call(Message::Allocate, bytes);
}

Position ScintillaCall::TargetAsUTF8(char *s) {
	return CallPointer(Message::TargetAsUTF8, 0, s);
}

std::string ScintillaCall::TargetAsUTF8() {
	return CallReturnString(Message::TargetAsUTF8, 0);
}

void ScintillaCall::SetLengthForEncode(Position bytes) {
	Call(Message::SetLengthForEncode, bytes);
}

Position ScintillaCall::EncodedFromUTF8(const char *utf8, char *encoded) {
	return CallPointer(Message::EncodedFromUTF8, reinterpret_cast<uintptr_t>(utf8), encoded);
}

std::string ScintillaCall::EncodedFromUTF8(const char *utf8) {
	return CallReturnString(Message::EncodedFromUTF8, reinterpret_cast<uintptr_t>(utf8));
}

Position ScintillaCall::FindColumn(Line line, Position column) {
	return Call(Message::FindColumn, line, column);
}

CaretSticky ScintillaCall::CaretSticky() {
	return static_cast<Scintilla::CaretSticky>(Call(Message::GetCaretSticky));
}

void ScintillaCall::SetCaretSticky(Scintilla::CaretSticky useCaretStickyBehaviour) {
	Call(Message::SetCaretSticky, static_cast<uintptr_t>(useCaretStickyBehaviour));
}

void ScintillaCall::ToggleCaretSticky() {
	Call(Message::ToggleCaretSticky);
}

void ScintillaCall::SetPasteConvertEndings(bool convert) {
	Call(Message::SetPasteConvertEndings, convert);
}

bool ScintillaCall::PasteConvertEndings() {
	return Call(Message::GetPasteConvertEndings);
}

void ScintillaCall::ReplaceRectangular(Position length, const char *text) {
	CallString(Message::ReplaceRectangular, length, text);
}

void ScintillaCall::SelectionDuplicate() {
	Call(Message::SelectionDuplicate);
}

void ScintillaCall::SetCaretLineBackAlpha(Scintilla::Alpha alpha) {
	Call(Message::SetCaretLineBackAlpha, static_cast<uintptr_t>(alpha));
}

Alpha ScintillaCall::CaretLineBackAlpha() {
	return static_cast<Scintilla::Alpha>(Call(Message::GetCaretLineBackAlpha));
}

void ScintillaCall::SetCaretStyle(Scintilla::CaretStyle caretStyle) {
	Call(Message::SetCaretStyle, static_cast<uintptr_t>(caretStyle));
}

CaretStyle ScintillaCall::CaretStyle() {
	return static_cast<Scintilla::CaretStyle>(Call(Message::GetCaretStyle));
}

void ScintillaCall::SetIndicatorCurrent(int indicator) {
	Call(Message::SetIndicatorCurrent, indicator);
}

int ScintillaCall::IndicatorCurrent() {
	return static_cast<int>(Call(Message::GetIndicatorCurrent));
}

void ScintillaCall::SetIndicatorValue(int value) {
	Call(Message::SetIndicatorValue, value);
}

int ScintillaCall::IndicatorValue() {
	return static_cast<int>(Call(Message::GetIndicatorValue));
}

void ScintillaCall::IndicatorFillRange(Position start, Position lengthFill) {
	Call(Message::IndicatorFillRange, start, lengthFill);
}

void ScintillaCall::IndicatorClearRange(Position start, Position lengthClear) {
	Call(Message::IndicatorClearRange, start, lengthClear);
}

int ScintillaCall::IndicatorAllOnFor(Position pos) {
	return static_cast<int>(Call(Message::IndicatorAllOnFor, pos));
}

int ScintillaCall::IndicatorValueAt(int indicator, Position pos) {
	return static_cast<int>(Call(Message::IndicatorValueAt, indicator, pos));
}

Position ScintillaCall::IndicatorStart(int indicator, Position pos) {
	return Call(Message::IndicatorStart, indicator, pos);
}

Position ScintillaCall::IndicatorEnd(int indicator, Position pos) {
	return Call(Message::IndicatorEnd, indicator, pos);
}

void ScintillaCall::SetPositionCache(int size) {
	Call(Message::SetPositionCache, size);
}

int ScintillaCall::PositionCache() {
	return static_cast<int>(Call(Message::GetPositionCache));
}

void ScintillaCall::SetLayoutThreads(int threads) {
	Call(Message::SetLayoutThreads, threads);
}

int ScintillaCall::LayoutThreads() {
	return static_cast<int>(Call(Message::GetLayoutThreads));
}

void ScintillaCall::CopyAllowLine() {
	Call(Message::CopyAllowLine);
}

void ScintillaCall::CutAllowLine() {
	Call(Message::CutAllowLine);
}

void ScintillaCall::SetCopySeparator(const char *separator) {
	CallString(Message::SetCopySeparator, 0, separator);
}

int ScintillaCall::CopySeparator(char *separator) {
	return static_cast<int>(CallPointer(Message::GetCopySeparator, 0, separator));
}

std::string ScintillaCall::CopySeparator() {
	return CallReturnString(Message::GetCopySeparator, 0);
}

void *ScintillaCall::CharacterPointer() {
	return reinterpret_cast<void *>(Call(Message::GetCharacterPointer));
}

void *ScintillaCall::RangePointer(Position start, Position lengthRange) {
	return reinterpret_cast<void *>(Call(Message::GetRangePointer, start, lengthRange));
}

Position ScintillaCall::GapPosition() {
	return Call(Message::GetGapPosition);
}

void ScintillaCall::IndicSetAlpha(int indicator, Scintilla::Alpha alpha) {
	Call(Message::IndicSetAlpha, indicator, static_cast<intptr_t>(alpha));
}

Alpha ScintillaCall::IndicGetAlpha(int indicator) {
	return static_cast<Scintilla::Alpha>(Call(Message::IndicGetAlpha, indicator));
}

void ScintillaCall::IndicSetOutlineAlpha(int indicator, Scintilla::Alpha alpha) {
	Call(Message::IndicSetOutlineAlpha, indicator, static_cast<intptr_t>(alpha));
}

Alpha ScintillaCall::IndicGetOutlineAlpha(int indicator) {
	return static_cast<Scintilla::Alpha>(Call(Message::IndicGetOutlineAlpha, indicator));
}

void ScintillaCall::SetExtraAscent(int extraAscent) {
	Call(Message::SetExtraAscent, extraAscent);
}

int ScintillaCall::ExtraAscent() {
	return static_cast<int>(Call(Message::GetExtraAscent));
}

void ScintillaCall::SetExtraDescent(int extraDescent) {
	Call(Message::SetExtraDescent, extraDescent);
}

int ScintillaCall::ExtraDescent() {
	return static_cast<int>(Call(Message::GetExtraDescent));
}

int ScintillaCall::MarkerSymbolDefined(int markerNumber) {
	return static_cast<int>(Call(Message::MarkerSymbolDefined, markerNumber));
}

void ScintillaCall::MarginSetText(Line line, const char *text) {
	CallString(Message::MarginSetText, line, text);
}

int ScintillaCall::MarginGetText(Line line, char *text) {
	return static_cast<int>(CallPointer(Message::MarginGetText, line, text));
}

std::string ScintillaCall::MarginGetText(Line line) {
	return CallReturnString(Message::MarginGetText, line);
}

void ScintillaCall::MarginSetStyle(Line line, int style) {
	Call(Message::MarginSetStyle, line, style);
}

int ScintillaCall::MarginGetStyle(Line line) {
	return static_cast<int>(Call(Message::MarginGetStyle, line));
}

void ScintillaCall::MarginSetStyles(Line line, const char *styles) {
	CallString(Message::MarginSetStyles, line, styles);
}

int ScintillaCall::MarginGetStyles(Line line, char *styles) {
	return static_cast<int>(CallPointer(Message::MarginGetStyles, line, styles));
}

std::string ScintillaCall::MarginGetStyles(Line line) {
	return CallReturnString(Message::MarginGetStyles, line);
}

void ScintillaCall::MarginTextClearAll() {
	Call(Message::MarginTextClearAll);
}

void ScintillaCall::MarginSetStyleOffset(int style) {
	Call(Message::MarginSetStyleOffset, style);
}

int ScintillaCall::MarginGetStyleOffset() {
	return static_cast<int>(Call(Message::MarginGetStyleOffset));
}

void ScintillaCall::SetMarginOptions(Scintilla::MarginOption marginOptions) {
	Call(Message::SetMarginOptions, static_cast<uintptr_t>(marginOptions));
}

MarginOption ScintillaCall::MarginOptions() {
	return static_cast<Scintilla::MarginOption>(Call(Message::GetMarginOptions));
}

void ScintillaCall::AnnotationSetText(Line line, const char *text) {
	CallString(Message::AnnotationSetText, line, text);
}

int ScintillaCall::AnnotationGetText(Line line, char *text) {
	return static_cast<int>(CallPointer(Message::AnnotationGetText, line, text));
}

std::string ScintillaCall::AnnotationGetText(Line line) {
	return CallReturnString(Message::AnnotationGetText, line);
}

void ScintillaCall::AnnotationSetStyle(Line line, int style) {
	Call(Message::AnnotationSetStyle, line, style);
}

int ScintillaCall::AnnotationGetStyle(Line line) {
	return static_cast<int>(Call(Message::AnnotationGetStyle, line));
}

void ScintillaCall::AnnotationSetStyles(Line line, const char *styles) {
	CallString(Message::AnnotationSetStyles, line, styles);
}

int ScintillaCall::AnnotationGetStyles(Line line, char *styles) {
	return static_cast<int>(CallPointer(Message::AnnotationGetStyles, line, styles));
}

std::string ScintillaCall::AnnotationGetStyles(Line line) {
	return CallReturnString(Message::AnnotationGetStyles, line);
}

int ScintillaCall::AnnotationGetLines(Line line) {
	return static_cast<int>(Call(Message::AnnotationGetLines, line));
}

void ScintillaCall::AnnotationClearAll() {
	Call(Message::AnnotationClearAll);
}

void ScintillaCall::AnnotationSetVisible(Scintilla::AnnotationVisible visible) {
	Call(Message::AnnotationSetVisible, static_cast<uintptr_t>(visible));
}

AnnotationVisible ScintillaCall::AnnotationGetVisible() {
	return static_cast<Scintilla::AnnotationVisible>(Call(Message::AnnotationGetVisible));
}

void ScintillaCall::AnnotationSetStyleOffset(int style) {
	Call(Message::AnnotationSetStyleOffset, style);
}

int ScintillaCall::AnnotationGetStyleOffset() {
	return static_cast<int>(Call(Message::AnnotationGetStyleOffset));
}

void ScintillaCall::ReleaseAllExtendedStyles() {
	Call(Message::ReleaseAllExtendedStyles);
}

int ScintillaCall::AllocateExtendedStyles(int numberStyles) {
	return static_cast<int>(Call(Message::AllocateExtendedStyles, numberStyles));
}

void ScintillaCall::AddUndoAction(int token, Scintilla::UndoFlags flags) {
	Call(Message::AddUndoAction, token, static_cast<intptr_t>(flags));
}

Position ScintillaCall::CharPositionFromPoint(int x, int y) {
	return Call(Message::CharPositionFromPoint, x, y);
}

Position ScintillaCall::CharPositionFromPointClose(int x, int y) {
	return Call(Message::CharPositionFromPointClose, x, y);
}

void ScintillaCall::SetMouseSelectionRectangularSwitch(bool mouseSelectionRectangularSwitch) {
	Call(Message::SetMouseSelectionRectangularSwitch, mouseSelectionRectangularSwitch);
}

bool ScintillaCall::MouseSelectionRectangularSwitch() {
	return Call(Message::GetMouseSelectionRectangularSwitch);
}

void ScintillaCall::SetMultipleSelection(bool multipleSelection) {
	Call(Message::SetMultipleSelection, multipleSelection);
}

bool ScintillaCall::MultipleSelection() {
	return Call(Message::GetMultipleSelection);
}

void ScintillaCall::SetAdditionalSelectionTyping(bool additionalSelectionTyping) {
	Call(Message::SetAdditionalSelectionTyping, additionalSelectionTyping);
}

bool ScintillaCall::AdditionalSelectionTyping() {
	return Call(Message::GetAdditionalSelectionTyping);
}

void ScintillaCall::SetAdditionalCaretsBlink(bool additionalCaretsBlink) {
	Call(Message::SetAdditionalCaretsBlink, additionalCaretsBlink);
}

bool ScintillaCall::AdditionalCaretsBlink() {
	return Call(Message::GetAdditionalCaretsBlink);
}

void ScintillaCall::SetAdditionalCaretsVisible(bool additionalCaretsVisible) {
	Call(Message::SetAdditionalCaretsVisible, additionalCaretsVisible);
}

bool ScintillaCall::AdditionalCaretsVisible() {
	return Call(Message::GetAdditionalCaretsVisible);
}

int ScintillaCall::Selections() {
	return static_cast<int>(Call(Message::GetSelections));
}

bool ScintillaCall::SelectionEmpty() {
	return Call(Message::GetSelectionEmpty);
}

void ScintillaCall::ClearSelections() {
	Call(Message::ClearSelections);
}

void ScintillaCall::SetSelection(Position caret, Position anchor) {
	Call(Message::SetSelection, caret, anchor);
}

void ScintillaCall::AddSelection(Position caret, Position anchor) {
	Call(Message::AddSelection, caret, anchor);
}

int ScintillaCall::SelectionFromPoint(int x, int y) {
	return static_cast<int>(Call(Message::SelectionFromPoint, x, y));
}

void ScintillaCall::DropSelectionN(int selection) {
	Call(Message::DropSelectionN, selection);
}

void ScintillaCall::SetMainSelection(int selection) {
	Call(Message::SetMainSelection, selection);
}

int ScintillaCall::MainSelection() {
	return static_cast<int>(Call(Message::GetMainSelection));
}

void ScintillaCall::SetSelectionNCaret(int selection, Position caret) {
	Call(Message::SetSelectionNCaret, selection, caret);
}

Position ScintillaCall::SelectionNCaret(int selection) {
	return Call(Message::GetSelectionNCaret, selection);
}

void ScintillaCall::SetSelectionNAnchor(int selection, Position anchor) {
	Call(Message::SetSelectionNAnchor, selection, anchor);
}

Position ScintillaCall::SelectionNAnchor(int selection) {
	return Call(Message::GetSelectionNAnchor, selection);
}

void ScintillaCall::SetSelectionNCaretVirtualSpace(int selection, Position space) {
	Call(Message::SetSelectionNCaretVirtualSpace, selection, space);
}

Position ScintillaCall::SelectionNCaretVirtualSpace(int selection) {
	return Call(Message::GetSelectionNCaretVirtualSpace, selection);
}

void ScintillaCall::SetSelectionNAnchorVirtualSpace(int selection, Position space) {
	Call(Message::SetSelectionNAnchorVirtualSpace, selection, space);
}

Position ScintillaCall::SelectionNAnchorVirtualSpace(int selection) {
	return Call(Message::GetSelectionNAnchorVirtualSpace, selection);
}

void ScintillaCall::SetSelectionNStart(int selection, Position anchor) {
	Call(Message::SetSelectionNStart, selection, anchor);
}

Position ScintillaCall::SelectionNStart(int selection) {
	return Call(Message::GetSelectionNStart, selection);
}

Position ScintillaCall::SelectionNStartVirtualSpace(int selection) {
	return Call(Message::GetSelectionNStartVirtualSpace, selection);
}

void ScintillaCall::SetSelectionNEnd(int selection, Position caret) {
	Call(Message::SetSelectionNEnd, selection, caret);
}

Position ScintillaCall::SelectionNEndVirtualSpace(int selection) {
	return Call(Message::GetSelectionNEndVirtualSpace, selection);
}

Position ScintillaCall::SelectionNEnd(int selection) {
	return Call(Message::GetSelectionNEnd, selection);
}

void ScintillaCall::SetRectangularSelectionCaret(Position caret) {
	Call(Message::SetRectangularSelectionCaret, caret);
}

Position ScintillaCall::RectangularSelectionCaret() {
	return Call(Message::GetRectangularSelectionCaret);
}

void ScintillaCall::SetRectangularSelectionAnchor(Position anchor) {
	Call(Message::SetRectangularSelectionAnchor, anchor);
}

Position ScintillaCall::RectangularSelectionAnchor() {
	return Call(Message::GetRectangularSelectionAnchor);
}

void ScintillaCall::SetRectangularSelectionCaretVirtualSpace(Position space) {
	Call(Message::SetRectangularSelectionCaretVirtualSpace, space);
}

Position ScintillaCall::RectangularSelectionCaretVirtualSpace() {
	return Call(Message::GetRectangularSelectionCaretVirtualSpace);
}

void ScintillaCall::SetRectangularSelectionAnchorVirtualSpace(Position space) {
	Call(Message::SetRectangularSelectionAnchorVirtualSpace, space);
}

Position ScintillaCall::RectangularSelectionAnchorVirtualSpace() {
	return Call(Message::GetRectangularSelectionAnchorVirtualSpace);
}

void ScintillaCall::SetVirtualSpaceOptions(Scintilla::VirtualSpace virtualSpaceOptions) {
	Call(Message::SetVirtualSpaceOptions, static_cast<uintptr_t>(virtualSpaceOptions));
}

VirtualSpace ScintillaCall::VirtualSpaceOptions() {
	return static_cast<Scintilla::VirtualSpace>(Call(Message::GetVirtualSpaceOptions));
}

void ScintillaCall::SetRectangularSelectionModifier(int modifier) {
	Call(Message::SetRectangularSelectionModifier, modifier);
}

int ScintillaCall::RectangularSelectionModifier() {
	return static_cast<int>(Call(Message::GetRectangularSelectionModifier));
}

void ScintillaCall::SetAdditionalSelFore(Colour fore) {
	Call(Message::SetAdditionalSelFore, fore);
}

void ScintillaCall::SetAdditionalSelBack(Colour back) {
	Call(Message::SetAdditionalSelBack, back);
}

void ScintillaCall::SetAdditionalSelAlpha(Scintilla::Alpha alpha) {
	Call(Message::SetAdditionalSelAlpha, static_cast<uintptr_t>(alpha));
}

Alpha ScintillaCall::AdditionalSelAlpha() {
	return static_cast<Scintilla::Alpha>(Call(Message::GetAdditionalSelAlpha));
}

void ScintillaCall::SetAdditionalCaretFore(Colour fore) {
	Call(Message::SetAdditionalCaretFore, fore);
}

Colour ScintillaCall::AdditionalCaretFore() {
	return static_cast<Colour>(Call(Message::GetAdditionalCaretFore));
}

void ScintillaCall::RotateSelection() {
	Call(Message::RotateSelection);
}

void ScintillaCall::SwapMainAnchorCaret() {
	Call(Message::SwapMainAnchorCaret);
}

void ScintillaCall::MultipleSelectAddNext() {
	Call(Message::MultipleSelectAddNext);
}

void ScintillaCall::MultipleSelectAddEach() {
	Call(Message::MultipleSelectAddEach);
}

int ScintillaCall::ChangeLexerState(Position start, Position end) {
	return static_cast<int>(Call(Message::ChangeLexerState, start, end));
}

Line ScintillaCall::ContractedFoldNext(Line lineStart) {
	return Call(Message::ContractedFoldNext, lineStart);
}

void ScintillaCall::VerticalCentreCaret() {
	Call(Message::VerticalCentreCaret);
}

void ScintillaCall::MoveSelectedLinesUp() {
	Call(Message::MoveSelectedLinesUp);
}

void ScintillaCall::MoveSelectedLinesDown() {
	Call(Message::MoveSelectedLinesDown);
}

void ScintillaCall::SetIdentifier(int identifier) {
	Call(Message::SetIdentifier, identifier);
}

int ScintillaCall::Identifier() {
	return static_cast<int>(Call(Message::GetIdentifier));
}

void ScintillaCall::RGBAImageSetWidth(int width) {
	Call(Message::RGBAImageSetWidth, width);
}

void ScintillaCall::RGBAImageSetHeight(int height) {
	Call(Message::RGBAImageSetHeight, height);
}

void ScintillaCall::RGBAImageSetScale(int scalePercent) {
	Call(Message::RGBAImageSetScale, scalePercent);
}

void ScintillaCall::MarkerDefineRGBAImage(int markerNumber, const char *pixels) {
	CallString(Message::MarkerDefineRGBAImage, markerNumber, pixels);
}

void ScintillaCall::RegisterRGBAImage(int type, const char *pixels) {
	CallString(Message::RegisterRGBAImage, type, pixels);
}

void ScintillaCall::ScrollToStart() {
	Call(Message::ScrollToStart);
}

void ScintillaCall::ScrollToEnd() {
	Call(Message::ScrollToEnd);
}

void ScintillaCall::SetTechnology(Scintilla::Technology technology) {
	Call(Message::SetTechnology, static_cast<uintptr_t>(technology));
}

Technology ScintillaCall::Technology() {
	return static_cast<Scintilla::Technology>(Call(Message::GetTechnology));
}

void *ScintillaCall::CreateLoader(Position bytes, Scintilla::DocumentOption documentOptions) {
	return reinterpret_cast<void *>(Call(Message::CreateLoader, bytes, static_cast<intptr_t>(documentOptions)));
}

void ScintillaCall::FindIndicatorShow(Position start, Position end) {
	Call(Message::FindIndicatorShow, start, end);
}

void ScintillaCall::FindIndicatorFlash(Position start, Position end) {
	Call(Message::FindIndicatorFlash, start, end);
}

void ScintillaCall::FindIndicatorHide() {
	Call(Message::FindIndicatorHide);
}

void ScintillaCall::VCHomeDisplay() {
	Call(Message::VCHomeDisplay);
}

void ScintillaCall::VCHomeDisplayExtend() {
	Call(Message::VCHomeDisplayExtend);
}

bool ScintillaCall::CaretLineVisibleAlways() {
	return Call(Message::GetCaretLineVisibleAlways);
}

void ScintillaCall::SetCaretLineVisibleAlways(bool alwaysVisible) {
	Call(Message::SetCaretLineVisibleAlways, alwaysVisible);
}

void ScintillaCall::SetLineEndTypesAllowed(Scintilla::LineEndType lineEndBitSet) {
	Call(Message::SetLineEndTypesAllowed, static_cast<uintptr_t>(lineEndBitSet));
}

LineEndType ScintillaCall::LineEndTypesAllowed() {
	return static_cast<Scintilla::LineEndType>(Call(Message::GetLineEndTypesAllowed));
}

LineEndType ScintillaCall::LineEndTypesActive() {
	return static_cast<Scintilla::LineEndType>(Call(Message::GetLineEndTypesActive));
}

void ScintillaCall::SetRepresentation(const char *encodedCharacter, const char *representation) {
	CallString(Message::SetRepresentation, reinterpret_cast<uintptr_t>(encodedCharacter), representation);
}

int ScintillaCall::Representation(const char *encodedCharacter, char *representation) {
	return static_cast<int>(CallPointer(Message::GetRepresentation, reinterpret_cast<uintptr_t>(encodedCharacter), representation));
}

std::string ScintillaCall::Representation(const char *encodedCharacter) {
	return CallReturnString(Message::GetRepresentation, reinterpret_cast<uintptr_t>(encodedCharacter));
}

void ScintillaCall::ClearRepresentation(const char *encodedCharacter) {
	Call(Message::ClearRepresentation, reinterpret_cast<uintptr_t>(encodedCharacter));
}

void ScintillaCall::ClearAllRepresentations() {
	Call(Message::ClearAllRepresentations);
}

void ScintillaCall::SetRepresentationAppearance(const char *encodedCharacter, Scintilla::RepresentationAppearance appearance) {
	Call(Message::SetRepresentationAppearance, reinterpret_cast<uintptr_t>(encodedCharacter), static_cast<intptr_t>(appearance));
}

RepresentationAppearance ScintillaCall::RepresentationAppearance(const char *encodedCharacter) {
	return static_cast<Scintilla::RepresentationAppearance>(Call(Message::GetRepresentationAppearance, reinterpret_cast<uintptr_t>(encodedCharacter)));
}

void ScintillaCall::SetRepresentationColour(const char *encodedCharacter, ColourAlpha colour) {
	Call(Message::SetRepresentationColour, reinterpret_cast<uintptr_t>(encodedCharacter), colour);
}

ColourAlpha ScintillaCall::RepresentationColour(const char *encodedCharacter) {
	return static_cast<ColourAlpha>(Call(Message::GetRepresentationColour, reinterpret_cast<uintptr_t>(encodedCharacter)));
}

void ScintillaCall::EOLAnnotationSetText(Line line, const char *text) {
	CallString(Message::EOLAnnotationSetText, line, text);
}

int ScintillaCall::EOLAnnotationGetText(Line line, char *text) {
	return static_cast<int>(CallPointer(Message::EOLAnnotationGetText, line, text));
}

std::string ScintillaCall::EOLAnnotationGetText(Line line) {
	return CallReturnString(Message::EOLAnnotationGetText, line);
}

void ScintillaCall::EOLAnnotationSetStyle(Line line, int style) {
	Call(Message::EOLAnnotationSetStyle, line, style);
}

int ScintillaCall::EOLAnnotationGetStyle(Line line) {
	return static_cast<int>(Call(Message::EOLAnnotationGetStyle, line));
}

void ScintillaCall::EOLAnnotationClearAll() {
	Call(Message::EOLAnnotationClearAll);
}

void ScintillaCall::EOLAnnotationSetVisible(Scintilla::EOLAnnotationVisible visible) {
	Call(Message::EOLAnnotationSetVisible, static_cast<uintptr_t>(visible));
}

EOLAnnotationVisible ScintillaCall::EOLAnnotationGetVisible() {
	return static_cast<Scintilla::EOLAnnotationVisible>(Call(Message::EOLAnnotationGetVisible));
}

void ScintillaCall::EOLAnnotationSetStyleOffset(int style) {
	Call(Message::EOLAnnotationSetStyleOffset, style);
}

int ScintillaCall::EOLAnnotationGetStyleOffset() {
	return static_cast<int>(Call(Message::EOLAnnotationGetStyleOffset));
}

bool ScintillaCall::SupportsFeature(Scintilla::Supports feature) {
	return Call(Message::SupportsFeature, static_cast<uintptr_t>(feature));
}

LineCharacterIndexType ScintillaCall::LineCharacterIndex() {
	return static_cast<Scintilla::LineCharacterIndexType>(Call(Message::GetLineCharacterIndex));
}

void ScintillaCall::AllocateLineCharacterIndex(Scintilla::LineCharacterIndexType lineCharacterIndex) {
	Call(Message::AllocateLineCharacterIndex, static_cast<uintptr_t>(lineCharacterIndex));
}

void ScintillaCall::ReleaseLineCharacterIndex(Scintilla::LineCharacterIndexType lineCharacterIndex) {
	Call(Message::ReleaseLineCharacterIndex, static_cast<uintptr_t>(lineCharacterIndex));
}

Line ScintillaCall::LineFromIndexPosition(Position pos, Scintilla::LineCharacterIndexType lineCharacterIndex) {
	return Call(Message::LineFromIndexPosition, pos, static_cast<intptr_t>(lineCharacterIndex));
}

Position ScintillaCall::IndexPositionFromLine(Line line, Scintilla::LineCharacterIndexType lineCharacterIndex) {
	return Call(Message::IndexPositionFromLine, line, static_cast<intptr_t>(lineCharacterIndex));
}

void ScintillaCall::StartRecord() {
	Call(Message::StartRecord);
}

void ScintillaCall::StopRecord() {
	Call(Message::StopRecord);
}

int ScintillaCall::Lexer() {
	return static_cast<int>(Call(Message::GetLexer));
}

void ScintillaCall::Colourise(Position start, Position end) {
	Call(Message::Colourise, start, end);
}

void ScintillaCall::SetProperty(const char *key, const char *value) {
	CallString(Message::SetProperty, reinterpret_cast<uintptr_t>(key), value);
}

void ScintillaCall::SetKeyWords(int keyWordSet, const char *keyWords) {
	CallString(Message::SetKeyWords, keyWordSet, keyWords);
}

int ScintillaCall::Property(const char *key, char *value) {
	return static_cast<int>(CallPointer(Message::GetProperty, reinterpret_cast<uintptr_t>(key), value));
}

std::string ScintillaCall::Property(const char *key) {
	return CallReturnString(Message::GetProperty, reinterpret_cast<uintptr_t>(key));
}

int ScintillaCall::PropertyExpanded(const char *key, char *value) {
	return static_cast<int>(CallPointer(Message::GetPropertyExpanded, reinterpret_cast<uintptr_t>(key), value));
}

std::string ScintillaCall::PropertyExpanded(const char *key) {
	return CallReturnString(Message::GetPropertyExpanded, reinterpret_cast<uintptr_t>(key));
}

int ScintillaCall::PropertyInt(const char *key, int defaultValue) {
	return static_cast<int>(Call(Message::GetPropertyInt, reinterpret_cast<uintptr_t>(key), defaultValue));
}

int ScintillaCall::LexerLanguage(char *language) {
	return static_cast<int>(CallPointer(Message::GetLexerLanguage, 0, language));
}

std::string ScintillaCall::LexerLanguage() {
	return CallReturnString(Message::GetLexerLanguage, 0);
}

void *ScintillaCall::PrivateLexerCall(int operation, void *pointer) {
	return reinterpret_cast<void *>(CallPointer(Message::PrivateLexerCall, operation, pointer));
}

int ScintillaCall::PropertyNames(char *names) {
	return static_cast<int>(CallPointer(Message::PropertyNames, 0, names));
}

std::string ScintillaCall::PropertyNames() {
	return CallReturnString(Message::PropertyNames, 0);
}

TypeProperty ScintillaCall::PropertyType(const char *name) {
	return static_cast<Scintilla::TypeProperty>(Call(Message::PropertyType, reinterpret_cast<uintptr_t>(name)));
}

int ScintillaCall::DescribeProperty(const char *name, char *description) {
	return static_cast<int>(CallPointer(Message::DescribeProperty, reinterpret_cast<uintptr_t>(name), description));
}

std::string ScintillaCall::DescribeProperty(const char *name) {
	return CallReturnString(Message::DescribeProperty, reinterpret_cast<uintptr_t>(name));
}

int ScintillaCall::DescribeKeyWordSets(char *descriptions) {
	return static_cast<int>(CallPointer(Message::DescribeKeyWordSets, 0, descriptions));
}

std::string ScintillaCall::DescribeKeyWordSets() {
	return CallReturnString(Message::DescribeKeyWordSets, 0);
}

LineEndType ScintillaCall::LineEndTypesSupported() {
	return static_cast<Scintilla::LineEndType>(Call(Message::GetLineEndTypesSupported));
}

int ScintillaCall::AllocateSubStyles(int styleBase, int numberStyles) {
	return static_cast<int>(Call(Message::AllocateSubStyles, styleBase, numberStyles));
}

int ScintillaCall::SubStylesStart(int styleBase) {
	return static_cast<int>(Call(Message::GetSubStylesStart, styleBase));
}

int ScintillaCall::SubStylesLength(int styleBase) {
	return static_cast<int>(Call(Message::GetSubStylesLength, styleBase));
}

int ScintillaCall::StyleFromSubStyle(int subStyle) {
	return static_cast<int>(Call(Message::GetStyleFromSubStyle, subStyle));
}

int ScintillaCall::PrimaryStyleFromStyle(int style) {
	return static_cast<int>(Call(Message::GetPrimaryStyleFromStyle, style));
}

void ScintillaCall::FreeSubStyles() {
	Call(Message::FreeSubStyles);
}

void ScintillaCall::SetIdentifiers(int style, const char *identifiers) {
	CallString(Message::SetIdentifiers, style, identifiers);
}

int ScintillaCall::DistanceToSecondaryStyles() {
	return static_cast<int>(Call(Message::DistanceToSecondaryStyles));
}

int ScintillaCall::SubStyleBases(char *styles) {
	return static_cast<int>(CallPointer(Message::GetSubStyleBases, 0, styles));
}

std::string ScintillaCall::SubStyleBases() {
	return CallReturnString(Message::GetSubStyleBases, 0);
}

int ScintillaCall::NamedStyles() {
	return static_cast<int>(Call(Message::GetNamedStyles));
}

int ScintillaCall::NameOfStyle(int style, char *name) {
	return static_cast<int>(CallPointer(Message::NameOfStyle, style, name));
}

std::string ScintillaCall::NameOfStyle(int style) {
	return CallReturnString(Message::NameOfStyle, style);
}

int ScintillaCall::TagsOfStyle(int style, char *tags) {
	return static_cast<int>(CallPointer(Message::TagsOfStyle, style, tags));
}

std::string ScintillaCall::TagsOfStyle(int style) {
	return CallReturnString(Message::TagsOfStyle, style);
}

int ScintillaCall::DescriptionOfStyle(int style, char *description) {
	return static_cast<int>(CallPointer(Message::DescriptionOfStyle, style, description));
}

std::string ScintillaCall::DescriptionOfStyle(int style) {
	return CallReturnString(Message::DescriptionOfStyle, style);
}

void ScintillaCall::SetILexer(void *ilexer) {
	CallPointer(Message::SetILexer, 0, ilexer);
}

Bidirectional ScintillaCall::Bidirectional() {
	return static_cast<Scintilla::Bidirectional>(Call(Message::GetBidirectional));
}

void ScintillaCall::SetBidirectional(Scintilla::Bidirectional bidirectional) {
	Call(Message::SetBidirectional, static_cast<uintptr_t>(bidirectional));
}

//--Autogenerated -- end of section automatically generated from Scintilla.iface */

}
