#!/usr/bin/env python
# Filters Scintilla.h to not contain generated stuff or deprecated defines

import sys
import fileinput

def main():
    inhibit = 0
    for line in fileinput.input():
        if line.startswith("/* ++Autogenerated") or line.startswith("#ifdef INCLUDE_DEPRECATED_FEATURES"):
            inhibit += 1
        if inhibit == 0:
            sys.stdout.write(line)
        if line.startswith("/* --Autogenerated") or line.startswith("#endif"):
            if (inhibit > 0):
                inhibit -= 1

if __name__ == "__main__":
    main()
