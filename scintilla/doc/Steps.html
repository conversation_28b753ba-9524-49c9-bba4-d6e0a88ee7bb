<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">
<html><head><meta content="text/html; charset=iso-8859-1" http-equiv="Content-Type"><title>How to use the Scintilla Edit Control in windows?</title></head><body bgcolor="#ffffff">
	<p><h2>How to use the Scintilla Edit Control in windows?</h2>
		<p>
			This should be a little step by step explanation how to use Scintilla in the windows environment.
		</p>
	</p>
	<p><h2>How to create Scintilla Edit Control?</h2>
		<p>
			First of all, load the Scintilla DLL with something like:
		</p>
		<pre>

	hmod = LoadLibrary(&quot;SciLexer.DLL&quot;);
		if (hmod==NULL)
		{
			MessageBox(hwndParent,
			&quot;The Scintilla DLL could not be loaded.&quot;,
			&quot;Error loading Scintilla&quot;,
			MB_OK | MB_ICONERROR);
		}
		</pre>
		<p>
			If the DLL was loaded successfully, then the DLL has registered (yes, by itself) a new
			window class. The new class called &quot;Scintilla&quot; is the new scintilla edit control.
		</p>
		<p>
			Now you can use this new control just like any other windows control.
		</p>
		<pre>

	hwndScintilla = CreateWindowEx(0,
		&quot;Scintilla&quot;,&quot;&quot;, WS_CHILD | WS_VISIBLE | WS_TABSTOP | WS_CLIPCHILDREN,
		10,10,500,400,hwndParent,(HMENU)GuiID, hInstance,NULL);
		</pre>
		<p>
			Note the new window class name: &quot;Scintilla&quot;. By reaching this point you actually included
			a Scintilla Edit Control to your windows program.
		</p>
	</p>
	<p><h2>How to control the Scintilla Edit Control?</h2>
		<p>
			You can control Scintilla by sending commands to the Edit Control.
			There a 2 ways of doing this. A simple and fast way.
		</p>
		<p><h3>The simple way to control Scintilla</h3>
			<p>
				The simple way is just like with any other windows control. You can send messages to the
				Scintilla Edit Control and receive notifications from the control. (Note that the notifications
				are sent to the parent window of the Scintilla Edit Control.)
			</p>
			<p>
				The Scintilla Edit Control knows a special message for each command.
				To send commands to the Scintilla Edit Control you can use the SendMessage function.
			</p>
			<pre>

	SendMessage(hwndScintilla,sci_command,wparam,lparam);
			</pre>
			<p>
				like:
			</p>
			<pre>

	SendMessage(hwndScintilla,SCI_CREATEDOCUMENT, 0, 0);
			</pre>
			<p>
				Some of the commands will return a value and unused parameters should be set to NULL.
			</p>
		</p>
		<p><h3>The fast way to control Scintilla</h3>
			<p>
				The fast way of controlling the Scintilla Edit Control  is to call message handling function by yourself.
				You can retrieve a pointer to the message handling function of the Scintilla Edit Control and
				call it directly to execute a command. This way is much more faster than the SendMessage() way.
			</p>
			<p>
				1st you have to use the SCI_GETDIRECTFUNCTION and SCI_GETDIRECTPOINTER commands to
				retrieve the pointer to the function and a pointer which must be the first parameter when calling the retrieved
				function pointer.
				You have to do this with the SendMessage way :)
			</p>
			<p>
				The whole thing has to look like this:
			</p>
			<pre>

	int (*fn)(void*,int,int,int);
	void * ptr;
	int canundo;

	fn = (int (__cdecl *)(void *,int,int,int))SendMessage(
		hwndScintilla,SCI_GETDIRECTFUNCTION,0,0);
	ptr = (void *)SendMessage(hwndScintilla,SCI_GETDIRECTPOINTER,0,0);

	canundo = fn(ptr,SCI_CANUNDO,0,0);
			</pre>
			<p>
				with &quot;fn&quot; as the function pointer to the message handling function of the Scintilla Control
				and &quot;ptr&quot; as the pointer that must be used as 1st parameter.
				The next parameters are the Scintilla Command with its two (optional) parameters.
			</p>

		</p>
		<p><h3>How will I receive notifications?</h3>
			<p>
				Whenever an event occurs where Scintilla wants to inform you about something, the Scintilla Edit Control
				will send notification to the parent window. This is done by a WM_NOTITY message.
				When receiving that message, you have to look in the xxx struct for the actual message.
			</p>
			<p>
				So in Scintillas parent window message handling function you have to include some code like this:
			</p>
			<pre>
	NMHDR *lpnmhdr;

	[...]

	case WM_NOTIFY:
		lpnmhdr = (LPNMHDR) lParam;

		if(lpnmhdr-&gt;hwndFrom==hwndScintilla)
		{
			switch(lpnmhdr-&gt;code)
			{
				case SCN_CHARADDED:
					/* Hey, Scintilla just told me that a new */
					/* character was added to the Edit Control.*/
					/* Now i do something cool with that char. */
				break;
			}
		}
	break;
			</pre>
		</p>
	</p>

    <p>
       <i>Page contributed by Holger Schmidt.</i>
    </p>
</body></html>

