# LSP Integration for TrySail

This document describes the Language Server Protocol (LSP) integration implemented for TrySail, specifically for Python code completion using pylsp.

## Overview

The LSP integration provides intelligent code completion for Python files by communicating with the Python Language Server (pylsp). This enhances the coding experience with context-aware suggestions, function signatures, and documentation.

## Architecture

### Components

1. **LSP Framework** (`lsp/` directory)
   - `LspClient`: Core LSP client for communication with language servers
   - `LspTypes`: LSP data structures and types
   - `LspMessages`: Base LSP message classes
   - `LspLifecycle`: Server lifecycle management (initialize, shutdown)
   - `LspDocumentSync`: Document synchronization notifications
   - `LspCompletion`: Completion request/response handling
   - `LspMessageFactory`: Message parsing and creation

2. **PyLspManager** (`PyLspManager.h/cpp`)
   - Manages Python LSP server (pylsp) lifecycle
   - Handles document operations (open, update, close)
   - Provides completion requests
   - Thread-safe operations with mutex protection

3. **CodeEditor Integration** (`CodeEditor.h/cpp`)
   - Enhanced `autoComplete()` method with LSP support
   - Falls back to traditional completion when LSP is unavailable
   - Automatic document synchronization with LSP server

## Features

### Implemented
- ✅ Python code completion using pylsp
- ✅ Automatic LSP server startup and initialization
- ✅ Document synchronization (open, update, close)
- ✅ Fallback to traditional completion when LSP fails
- ✅ Thread-safe LSP operations
- ✅ Error handling and timeout management

### Future Enhancements
- 🔄 Hover information (documentation on hover)
- 🔄 Go to definition
- 🔄 Find references
- 🔄 Diagnostics (error/warning highlighting)
- 🔄 Code formatting
- 🔄 Support for other languages (JavaScript, C++, etc.)

## Prerequisites

### Python Language Server (pylsp)
Install pylsp using pip:
```bash
pip install python-lsp-server
```

Or using conda:
```bash
conda install python-lsp-server
```

The LSP manager will automatically try to find pylsp in common locations:
- System PATH
- `/usr/local/bin/pylsp`
- `/usr/bin/pylsp`
- `~/.local/bin/pylsp`

## Usage

### Automatic Initialization
When you open a Python file (`.py` extension), the LSP integration automatically:
1. Detects the file type as Python
2. Sets the file path in the CodeEditor
3. Initializes the PyLspManager with the workspace root
4. Starts the pylsp server
5. Opens the document with the LSP server

### Code Completion
1. **Trigger**: Type characters and the completion will automatically trigger
2. **LSP Priority**: If LSP is available and ready, it provides completions
3. **Fallback**: If LSP is unavailable, falls back to traditional keyword completion
4. **Display**: Completions appear in Scintilla's autocomplete popup

### Example Workflow
```python
import os

# Type "os." and you'll see method completions from pylsp
os.path.join()

def my_function():
    return "hello"

# Type "my_f" and you'll see "my_function" in completions
my_f
```

## Implementation Details

### LSP Message Flow
1. **Initialization**:
   ```
   Client -> Server: initialize request
   Server -> Client: initialize response (capabilities)
   Client -> Server: initialized notification
   ```

2. **Document Operations**:
   ```
   Client -> Server: textDocument/didOpen
   Client -> Server: textDocument/didChange (on edits)
   Client -> Server: textDocument/didClose (when closing)
   ```

3. **Completion**:
   ```
   Client -> Server: textDocument/completion request
   Server -> Client: completion response (items list)
   ```

### Thread Safety
- All LSP operations are protected by mutex locks
- Document version tracking prevents race conditions
- Asynchronous completion requests with request ID tracking

### Error Handling
- LSP server startup timeout (10 seconds)
- Graceful fallback to traditional completion
- Error logging for debugging
- Automatic retry mechanisms

## Configuration

### Workspace Detection
The workspace root is automatically detected as the directory containing the opened Python file. This affects:
- Python path resolution
- Import completions
- Project-specific configurations

### LSP Server Configuration
The pylsp server is started with default settings. Future versions may support:
- Custom pylsp configuration files
- Plugin management
- Server-specific settings

## Debugging

### Enable Debug Logging
Add debug output to see LSP communication:
```cpp
qDebug() << "LSP message:" << message;
```

### Common Issues
1. **pylsp not found**: Ensure pylsp is installed and in PATH
2. **No completions**: Check if LSP server started successfully
3. **Slow completions**: Large projects may take time to index

### Log Messages
- "LSP server is ready for Python completion" - Server initialized
- "LSP Error: ..." - Server communication errors
- "PyLsp server started, sending initialize request..." - Server startup

## Testing

### Manual Testing
1. Open `test_lsp.py` in TrySail
2. Try typing incomplete identifiers
3. Verify completions appear
4. Test both LSP and fallback completions

### Test Cases
- Function name completion
- Method completion on objects
- Module/import completion
- Built-in type completion
- Fallback when LSP unavailable

## Code Structure

### Key Files Added/Modified
```
lsp/LspCompletion.h/cpp     - Completion message types
PyLspManager.h/cpp          - Python LSP manager
CodeEditor.h/cpp            - Enhanced with LSP support
DocumentWidget.cpp          - LSP initialization
CMakeLists.txt              - Build configuration
```

### Integration Points
1. **DocumentWidget**: Initializes LSP when opening Python files
2. **CodeEditor**: Enhanced autoComplete() with LSP support
3. **PyLspManager**: Manages pylsp server lifecycle
4. **LspClient**: Generic LSP communication framework

This integration provides a solid foundation for expanding language support and adding more LSP features in the future.
