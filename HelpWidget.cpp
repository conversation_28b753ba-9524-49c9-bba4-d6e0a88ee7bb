#include "HelpWidget.h"

#include "TrySailGlobal.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTextEdit>
#include <QLabel>
#include <QLineEdit>
#include <QGuiApplication>
#include <QStyleHints>

QHelpWidget::QHelpWidget( const enumCodeType codeType, QWidget* parent )
    : QWidget{ parent }
{
    const auto sCodeType = descriptionFromType( codeType );
    auto layoutOverall = new QVBoxLayout;

    auto layoutSearch = new QHBoxLayout;
    auto lbl = new QLabel{ u"Search %1 help:"_s.arg( sCodeType ), this };
    layoutSearch->addWidget( lbl );

    auto txtSearch = new QLineEdit{ this };
    layoutSearch->addWidget( txtSearch );

    layoutOverall->addLayout( layoutSearch );

    m_txtContents = new QTextEdit{ this };
    m_txtContents->setAcceptRichText( true );
    m_txtContents->setReadOnly( true );
    layoutOverall->addWidget( m_txtContents, 1 );

    setLayout( layoutOverall );

    const auto styleHints  = QGuiApplication::styleHints();
    const auto colorScheme = styleHints->colorScheme();

    onColorSchemeChanged( colorScheme );
    connect( styleHints, &QStyleHints::colorSchemeChanged, this, &QHelpWidget::onColorSchemeChanged );
    connect( txtSearch,  &QLineEdit::textChanged,          this, &QHelpWidget::requestHelp );
}

void QHelpWidget::onColorSchemeChanged( Qt::ColorScheme colorScheme )
{
    const auto bLight = (colorScheme != Qt::ColorScheme::Dark );

    const auto colorDefaultBackground  = bLight ? solar_base3  : solar_base03;
    const auto colorDefaultFore        = bLight ? solar_base00 : solar_base0;

    auto p = palette();
    p.setColor( QPalette::Base, colorDefaultBackground );
    p.setColor( QPalette::Text, colorDefaultFore );
    setPalette(p);
}

void QHelpWidget::onHelpAvailable( const QString& sContents )
{
    auto sContents2 = sContents;
    sContents2 = sContents2.remove( u"\\n"_s );
    m_txtContents->setText( sContents2 );
}
