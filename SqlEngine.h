#pragma once

#include "ContentEngine.h"
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

class QSqlEngine : public QContentEngine
{
    Q_OBJECT
public:
    explicit QSqlEngine();
    virtual ~QSqlEngine();

public:
    virtual mapMemoryType getMemory() override final;

    inline static const auto CBOR_ColumnName      = u"ColumnName"_s;
    inline static const auto CBOR_ColumnDefault   = u"ColumnDefault"_s;
    inline static const auto CBOR_ColumnAuto      = u"ColumnAuto"_s;
    inline static const auto CBOR_ColumnGenerated = u"ColumnGenerated"_s;
    inline static const auto CBOR_ColumnNull      = u"ColumnNull"_s;
    inline static const auto CBOR_ColumnReadOnly  = u"ColumnReadOnly"_s;
    inline static const auto CBOR_ColumnLength    = u"ColumnLength"_s;
    inline static const auto CBOR_ColumnRequired  = u"ColumnRequired"_s;
    inline static const auto CBOR_ColumnType      = u"ColumnType"_s;

protected:
    QString m_dbConnectionName;

protected:
    virtual bool processCode( const int chunkHandle, const QString& chunkCode ) override final;
    virtual void processRestart() override final;
};
