# %% [markdown]
# Demonstrating some options
## This is markdown from a jupytext file. Like Jupyter, JupyText suppport markdown and Python. Unlike Jupyter, Jupytext stores data in a plain text file, indicating markdown sections with comments.
- Markdown list A.
- Markdown list B.
- Markdown list C.
## Code
### basic loop

# %%
for j in range( 7, 28 ):
   print( f"j={j}" )

# %% [markdown]
### simple printing

# %%
print( "demonstration 1")

# %% [markdown]
## Another markdown section
# %%
for i in range( 0, 10 ):
   varJ = i * i
   print( f"varJ={varJ}" )

# %% [markdown]
# Packages
## Example: Originally internal packages, still internal.
# %%
import math
varSqrt2 = math.sqrt( 2 )

# %% [markdown]
## Example: Originally external Python only packages, now internal.
# %%
import psutil
varCpus = psutil.cpu_count()

# %% [markdown]
## Example: Originally external Python/C packages, now internal.
# %%
import numpy as np
varNumpy = np.arange(15).reshape(3, 5)

import pandas
varDataFrame = pandas.read_csv("bad_file_name.csv")

# %% [markdown]
## Example: Originally external Python/C packages, still in progress.
# %%
import matplotlib # in progress
