#include "DocumentWidget.h"
#include "ContentEngine.h"
#include "PythonEngine.h"
#include "SqlEngine.h"
#include "PythonThread.h"
#include "MainWindow.h"
#include "ConfigDialog.h"

#include <QCborArray>
#include <QCborValue>
#include <QFileDialog>
#include <QFileInfo>
#include <QGroupBox>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonParseError>
#include <QLabel>
#include <QMessageBox>
#include <QPrinter>
#include <QPushButton>
#include <QSettings>
#include <QTimer>
#include <QToolBar>
#include <QVBoxLayout>
#include <QPainter>
#include <QSaveFile>
#include <QBuffer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QJsonValue>
#include <QTextBrowser>
#include <QSvgRenderer>
#include <QPainter>

static const auto SETTINGS_ZOOM = u"ZoomLevel"_s;

QDocumentWidget::QDocumentWidget( const QString sFileName, const QByteArray& baFileContents, const bool bReadOnly, QWidget* parent )
    : QSplitter{ Qt::Orientation::Vertical, parent }
    , m_fileName( sFileName )
    , m_codeType( typeFromFileName( sFileName ) )
    , m_inputEditor( m_codeType, this )
    , m_internalsView( m_codeType, this )
    , m_outputView( *this, this )
    , m_helpWidget( m_codeType, this )
    , m_bReadOnly( bReadOnly )
{
    const auto settings   = QSettings{};
    const auto nZoomLevel = settings.value( SETTINGS_ZOOM, 1 ).toInt();
    m_inputEditor.setZoom( nZoomLevel );

    auto topSplitter = new QSplitter{ this };
    topSplitter->addWidget( new QPanelWidget( tr("Code"), &m_inputEditor, this ) );
    topSplitter->addWidget( new QPanelWidget( tr("Output"), &m_outputView, this ) );

    auto bottomSplitter = new QSplitter{ this };
    bottomSplitter->addWidget( new QPanelWidget( tr("Internals"), &m_internalsView, this ) );
    bottomSplitter->addWidget( new QPanelWidget( tr("Help"), &m_helpWidget, this ) );

    addWidget( topSplitter );
    addWidget( bottomSplitter );
    setStretchFactor( 0, 3 );
    setStretchFactor( 1, 1 );

    auto mainWindow = QTrySailMainWindow::getTrysailMainWindow();
    connect( mainWindow->actionFileSave, &QAction::triggered, this, &QDocumentWidget::onFileSave );
    connect( mainWindow->actionFileSaveAs, &QAction::triggered, this, &QDocumentWidget::onFileSaveAs );
    connect( mainWindow->actionFilePrint, &QAction::triggered, this, &QDocumentWidget::onFilePrint );
    connect( mainWindow->actionFilePdf, &QAction::triggered, this, &QDocumentWidget::onFilePdf );
    connect( mainWindow->actionEditUndo, &QAction::triggered, this, &QDocumentWidget::onEditUndo );
    connect( mainWindow->actionEditRedo, &QAction::triggered, this, &QDocumentWidget::onEditRedo );
    connect( mainWindow->actionViewToggleMemoryVisible, &QAction::triggered, this, &QDocumentWidget::onViewToggleMemoryVisible );
    connect( mainWindow->actionDatabaseSetup, &QAction::triggered, this, &QDocumentWidget::onCodeSetup );
    connect( mainWindow->actionCodeRunStart, &QAction::triggered, this, &QDocumentWidget::onCodeRunStart );
    connect( mainWindow->actionCodeRunStop, &QAction::triggered, this, &QDocumentWidget::onCodeRunStop );
    connect( mainWindow->actionCodeRunRestart, &QAction::triggered, this, &QDocumentWidget::onCodeRunRestart );
    connect( mainWindow->actionCodeRunKill, &QAction::triggered, this, &QDocumentWidget::onCodeRunKill );
    connect( mainWindow->actionCodeRunRestartRunAll, &QAction::triggered, this, &QDocumentWidget::onCodeRunRestartRunAll );
    connect( mainWindow->actionEditFind, &QAction::triggered, this, &QDocumentWidget::onEditFind );
    connect( mainWindow->actionEditFindReplace, &QAction::triggered, this, &QDocumentWidget::onEditFind );
    connect( mainWindow->actionEditFindNext, &QAction::triggered, this, &QDocumentWidget::onEditFindNext );
    connect( mainWindow->actionEditFindPrevious, &QAction::triggered, this, &QDocumentWidget::onEditFindPrevious );

    // determine appropriate engine
    auto thread = (QThread*) nullptr;
    switch ( m_codeType ) {

    case enumCodeType::codeTypePython:
        m_engine = new QPythonEngine{};
        thread   = static_cast<QThread*>(mainWindow->getThreadPython());
        break;

    case enumCodeType::codeTypeSql:
        m_engine = new QSqlEngine{};
        thread   = mainWindow->getThreadSql();
        break;

    case enumCodeType::codeTypeJson:
    case enumCodeType::codeTypeHtml:
    case enumCodeType::codeTypeMarkdown:
    case enumCodeType::codeTypeBash:
    case enumCodeType::codeTypeBatch:
    case enumCodeType::codeTypeText:
    default:
        break;
    }

    connect( &m_output,     &QOutputData::needsRebuild,      &m_outputView, &QOutputWidget::requestRebuild );
    connect( &m_helpWidget, &QHelpWidget::requestHelp,       this,          &QDocumentWidget::onRequestHelp );
    connect( this,          &QDocumentWidget::helpAvailable, &m_helpWidget, &QHelpWidget::onHelpAvailable   );

    // setup engine
    if ( m_engine != nullptr ) {

        m_engine->moveToThread( thread );

        connect( this, &QDocumentWidget::requestSetup,   m_engine, &QContentEngine::setup );
        connect( this, &QDocumentWidget::requestExecute, m_engine, &QContentEngine::execute );
        connect( this, &QDocumentWidget::requestRestart, m_engine, &QContentEngine::restart );
        connect( this, &QDocumentWidget::requestStop,    m_engine, &QContentEngine::stop );
        connect( this, &QDocumentWidget::requestKill,    m_engine, &QContentEngine::kill );

        connect( m_engine, &QContentEngine::progressJobStart,       this, &QDocumentWidget::onProgressJobStart );
        connect( m_engine, &QContentEngine::progressLineStart,      this, &QDocumentWidget::onProgressLineStart );
        connect( m_engine, &QContentEngine::progressLineAnnotation, this, &QDocumentWidget::onProgressLineAnnotation );
        connect( m_engine, &QContentEngine::progressLineText,       this, &QDocumentWidget::onProgressLineText );
        connect( m_engine, &QContentEngine::progressLineError,      this, &QDocumentWidget::onProgressLineError );
        connect( m_engine, &QContentEngine::progressLineSvg,        this, &QDocumentWidget::onProgressLineSvg );
        connect( m_engine, &QContentEngine::progressLineImage,      this, &QDocumentWidget::onProgressLineImage );
        connect( m_engine, &QContentEngine::progressLineEnd,        this, &QDocumentWidget::onProgressLineEnd );
        connect( m_engine, &QContentEngine::progressJobEnd,         this, &QDocumentWidget::onProgressJobEnd );
        connect( m_engine, &QContentEngine::progressRestartBegin,   this, &QDocumentWidget::onProgressRestartBegin );
        connect( m_engine, &QContentEngine::progressRestartEnd,     this, &QDocumentWidget::onProgressRestartEnd );
        connect( m_engine, &QContentEngine::progressMemoryUpdate,   &m_inputEditor, &QCodeEditor::onProgressMemoryUpdate );
        connect( m_engine, &QContentEngine::progressMemoryUpdate,   &m_internalsView, &QInternalsView::onProgressMemoryUpdate );
    }

    const auto fileFormat = formatFromFileName( m_fileName );
    QTimer::singleShot( 0, this, [ this, fileFormat, baFileContents ]() {
        readFileContents( fileFormat, baFileContents );
    } );
}

QDocumentWidget::~QDocumentWidget()
{
    const auto nZoomLevel = m_inputEditor.zoom();

    auto settings = QSettings{};
    settings.setValue( SETTINGS_ZOOM, QVariant::fromValue( nZoomLevel ) );

    if ( m_engine != nullptr ) {

        // qqq give engine a chance to finalize and cleanup?
        m_engine->m_stateRequested = QContentEngine::EngineState::Waiting;

        delete m_engine;
        m_engine = nullptr;
    }
}

QString QDocumentWidget::fileName() const
{
    return m_fileName;
}


void QDocumentWidget::syncRunActionsWithEngine()
{
    const auto activeWidget   = qApp->focusWidget();
    const auto activeDocument = findParentOfType< QDocumentWidget >( activeWidget );
    if ( activeDocument != this )
        return;

    const auto bHasEngine  = ( m_engine != nullptr );
    const auto bStateMatch = ( m_engine != nullptr ) && ( m_engine->m_stateRequested == m_engine->m_stateActual );
    const auto bSupportsSetup = ( m_engine != nullptr ) && ( m_engine->m_supportsSetup );
    const auto bEngineRunning = ( m_engine != nullptr ) && ( m_engine->m_stateActual == QContentEngine::EngineState::Running );

    auto pMainWindow = QTrySailMainWindow::getTrysailMainWindow();

    pMainWindow->actionDatabaseSetup->setEnabled( bHasEngine && bStateMatch && ( !bEngineRunning ) && bSupportsSetup );
    pMainWindow->actionCodeRunStart->setEnabled( bHasEngine && bStateMatch && ( !bEngineRunning ) );
    pMainWindow->actionCodeRunKill->setEnabled( bHasEngine && ( m_engine->m_stateRequested == QContentEngine::EngineState::Waiting ) && ( m_engine->m_stateActual == QContentEngine::EngineState::Running ) );
    pMainWindow->actionCodeRunRestart->setEnabled( bHasEngine && bStateMatch && ( !bEngineRunning ) );
    pMainWindow->actionCodeRunRestartRunAll->setEnabled( bHasEngine && bStateMatch && ( !bEngineRunning ) );
    pMainWindow->actionCodeRunStop->setEnabled( bHasEngine && bStateMatch && bEngineRunning );
}

bool QDocumentWidget::canClose()
{
    const bool bDirty = m_inputEditor.modify();
    if ( ! bDirty )
        return true;

    const auto nDecision = QMessageBox::warning( this, tr("TrySail"), tr("The document has been modified.\nDo you want to save your changes?"),
                                    QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel, QMessageBox::Save );

    switch (nDecision) {
    case QMessageBox::Save:
        onFileSave();
        return (! bDirty );
        break;
    case QMessageBox::Discard:
        return true;
        break;
    default:
    case QMessageBox::Cancel:
        return false;
        break;
    }
}

void QDocumentWidget::onProgressJobStart()
{
    syncRunActionsWithEngine();
}
void QDocumentWidget::onProgressRestartBegin()
{
    syncRunActionsWithEngine();
}
void QDocumentWidget::onProgressRestartEnd()
{
    syncRunActionsWithEngine();
}

void QDocumentWidget::onRequestHelp( const QString& sSearch )
{
    if ( ! isFocal() )
        return;

    switch ( m_codeType ) {

    case enumCodeType::codeTypePython: {

            m_engine->m_stateRequested = QContentEngine::EngineState::Running;
            syncRunActionsWithEngine();

            const auto sImport = u"import pydoc"_s;
            const auto sHelp   = u"pydoc.render_doc(\"%1\", renderer=pydoc.HTMLDoc())"_s.arg( sSearch );
            //const auto sHelp = u"pydoc.help.help('%1', is_cli=True)"_s.arg(sSearch);
            const auto chunkHandles = { HELP_HANDLE, HELP_HANDLE };
            const auto chunkCodes   = { sImport, sHelp };
            emit requestExecute( chunkHandles, chunkCodes );
        }
        break;
    default: {
            const auto sCodeType = descriptionFromType( m_codeType );
            m_helpWidget.onHelpAvailable( u"%1: No help available for '%2'"_s.arg( sCodeType, sSearch) );
            break;
        }
    }
}

void QDocumentWidget::onProgressLineStart( const int executionHandle, const QString& sCode, const int executionSequence )
{
    if ( executionHandle != HELP_HANDLE ) {
        m_inputEditor.setExecutionStatus( executionHandle, enumExecutionStatus::executionStatusExecuting );
        m_output.startExecution( executionHandle, sCode, executionSequence );
    }
}

void QDocumentWidget::onProgressLineEnd( const int executionHandle, const bool bError )
{
    if ( executionHandle != HELP_HANDLE ) {
        const auto nStatus = bError ? enumExecutionStatus::executionStatusDoneError : enumExecutionStatus::executionStatusDoneOk;
        m_inputEditor.setExecutionStatus( executionHandle, nStatus );
    }
}

void QDocumentWidget::onProgressLineAnnotation( const int executionHandle, const QString& sAnnotation )
{
    if ( executionHandle != HELP_HANDLE ) {
        m_inputEditor.setAnnotation( executionHandle, sAnnotation, false );
    }
}

void QDocumentWidget::onProgressLineText( const int executionHandle, const QString& sText, const enumContentFormat contentFormat )
{
    if ( executionHandle == HELP_HANDLE ) {
        emit helpAvailable( sText );
    } else {
        m_output.addTextOutput( executionHandle, sText, contentFormat, false );
    }
}
void QDocumentWidget::onProgressLineError( const int executionHandle, const QString& sSummary, const QString& sDetail )
{
    if ( executionHandle == HELP_HANDLE ) {
        emit helpAvailable( sSummary );
    } else {
        m_output.addTextOutput( executionHandle, sDetail, enumContentFormat::contentFormatPlain, true );
        m_inputEditor.setAnnotation( executionHandle, sSummary, true );
    }
}

void QDocumentWidget::onProgressLineSvg( const int executionHandle, const QByteArray& baSvg )
{
    if ( executionHandle != HELP_HANDLE ) {
        m_output.addSvgOutput( executionHandle, baSvg );
    }
}
void QDocumentWidget::onProgressLineImage( const int executionHandle, const QImage& img )
{
    if ( executionHandle != HELP_HANDLE ) {
        m_output.addImageOutput( executionHandle, img );
    }
}

void QDocumentWidget::onProgressJobEnd( const bool /*bSuccess*/ )
{
    syncRunActionsWithEngine();
    m_inputEditor.resetAllPendingExecutionStatus();
}

bool QDocumentWidget::isFocal() const
{
    const auto activeWidget   = qApp->focusWidget();
    const auto activeDocument = findParentOfType< QDocumentWidget >( activeWidget );
    return ( activeDocument == this );
}

void QDocumentWidget::onCodeSetup()
{
    if ( ! isFocal() )
        return;

    // qqq need to improve switch with signal/slot or overloading

    if ( m_codeType == enumCodeType::codeTypeSql ) {

        const auto dlgDb = new QFileDialog{ this, tr( "Select Database" ), m_fileName, tr( "Database Files (*.db);;All files (*.*)" ) };
        dlgDb->setAcceptMode( QFileDialog::AcceptMode::AcceptOpen );
        dlgDb->setFileMode( QFileDialog::ExistingFile );
        dlgDb->setAttribute( Qt::WA_DeleteOnClose );
        dlgDb->setDefaultSuffix( "db" );
        connect( dlgDb, &QFileDialog::fileSelected, this, [this] (const QString dbName ) {
            auto mapSetup = QMap< QString, QString >{};
            mapSetup.insert( "DATABASE_NAME", dbName );
            emit requestSetup( mapSetup );
        } );
        dlgDb->open();
    }
}

void QDocumentWidget::onCodeRunStart()
{
    if ( ! isFocal() )
        return;

    if ( m_engine == nullptr )
        return;

    m_engine->m_stateRequested = QContentEngine::EngineState::Running;
    syncRunActionsWithEngine();

    // find out what code we are running
    auto posStart  = m_inputEditor.selectionStart();
    auto posEnd    = m_inputEditor.selectionEnd();
    auto lineStart = m_inputEditor.lineFromPosition( posStart );
    auto lineEnd   = m_inputEditor.lineFromPosition( posEnd );

    // nothing selected; implicitly select the current line
    if ( lineStart == -1 || lineEnd == -1 ) {
        lineStart = m_inputEditor.currentLine();
        lineEnd   = lineStart;
    }

    // expand; need to go up and down to get to a viable start and stopping place
    // for markdown; do all adjacent markdown; could be lists, etc., that need
    // renumbering cohesively for code; need to get to start/end of indention

    // go up/down to get to cogent block
    lineStart = m_inputEditor.findStartFromLine( lineStart );
    lineEnd   = m_inputEditor.findEndFromLine( lineEnd );

    auto chunkHandles = QList< int >{};
    auto chunkCodes   = QList< QString >{};

    // execute range in blocks
    for ( auto lineStartExec = lineStart; lineStartExec <= lineEnd; ) {

        const auto nLineType = m_inputEditor.getLineType( lineStartExec );
        const auto lineEndExec = m_inputEditor.findEndFromLine( lineStartExec );

        if ( nLineType != enumLineType::lineTypeMarkdown ) {

            auto       listToRun   = QStringList{};
            for ( auto lineToRun = lineStartExec; lineToRun <= lineEndExec; ++lineToRun ) {

                const auto sCodeToRun = m_inputEditor.getLine( lineToRun );
                listToRun.append( QString::fromUtf8( sCodeToRun ) );
            }

            const auto chunkToRun = listToRun.join( QString{} );

            if ( !chunkToRun.trimmed().isEmpty() ) {

                // add request to queue
                const auto executionHandle = m_inputEditor.getExecutionHandleFromLine( lineEndExec );
                m_inputEditor.setExecutionStatus( executionHandle, enumExecutionStatus::executionStatusRequested );
                chunkHandles.append( executionHandle );
                chunkCodes.append( chunkToRun );
            }
        }

        lineStartExec = lineEndExec + 1;
    }
    emit requestExecute( chunkHandles, chunkCodes );

    if ( m_codeType != enumCodeType::codeTypeMarkdown )
        m_inputEditor.gotoLine( lineEnd + 1 );
}

void QDocumentWidget::onCodeRunStop()
{
    if ( ! isFocal() )
        return;

    m_engine->m_stateRequested = QContentEngine::EngineState::Waiting;
    emit requestStop();
    syncRunActionsWithEngine();
}

void QDocumentWidget::onCodeRunKill()
{
    if ( ! isFocal() )
        return;

    QPythonThread::sendInterrupt();

    m_engine->m_stateRequested = QContentEngine::EngineState::Waiting;
    emit requestKill();
    syncRunActionsWithEngine();
}

void QDocumentWidget::onCodeRunRestart()
{
    if ( ! isFocal() )
        return;

    m_engine->m_stateRequested = QContentEngine::EngineState::Restarting;
    emit requestRestart();
    syncRunActionsWithEngine();
    m_output.clear();
    m_inputEditor.annotationClearAll();
    for ( auto line = 0; line < m_inputEditor.lineCount(); ++line ) {
        const auto executionHandle = m_inputEditor.getExecutionHandleFromLine( line );
        m_inputEditor.setExecutionStatus( executionHandle, enumExecutionStatus::executionStatusNone );
    }
}
void QDocumentWidget::onCodeRunRestartRunAll()
{
    if ( ! isFocal() )
        return;

    // restart
    onCodeRunRestart();

    // run all
    m_inputEditor.selectAll();
    onCodeRunStart();
}

void QDocumentWidget::onFileSave()
{
    if ( m_bReadOnly ) {

        onFileSaveAs();
        return;
    }

    if ( ! isFocal() )
        return;

    saveFile( m_fileName );
}

QStringView suffixFromCodeType( const enumCodeType codeType ) {

    switch ( codeType ) {

    case (enumCodeType::codeTypeMarkdown):
        return SUFFIX_MD;
        break;
    case (enumCodeType::codeTypePython):
        return SUFFIX_PY;
        break;
    case (enumCodeType::codeTypeSql):
        return SUFFIX_SQL;
        break;
    case (enumCodeType::codeTypeBash):
        return SUFFIX_SH;
        break;
    case (enumCodeType::codeTypeBatch):
        return SUFFIX_BAT;
        break;
    case (enumCodeType::codeTypeText):
        return SUFFIX_TXT;
        break;
    case (enumCodeType::codeTypeJson):
        return SUFFIX_JSON;
        break;
    case (enumCodeType::codeTypeHtml):
        return SUFFIX_HTML;
        break;
    default:
        return u""_s;
        break;
    }

}

void QDocumentWidget::onFileSaveAs()
{
    if ( ! isFocal() )
        return;

    const auto suffix  = suffixFromCodeType( m_codeType );
    const auto dlgSave = new QFileDialog{ this, tr( "Save" ), m_fileName };
    dlgSave->setAcceptMode( QFileDialog::AcceptMode::AcceptSave );
    dlgSave->setFileMode( QFileDialog::AnyFile );
    dlgSave->setAttribute( Qt::WA_DeleteOnClose );
    dlgSave->setDefaultSuffix( suffix.toString() );
    connect( dlgSave, &QFileDialog::fileSelected, this, &QDocumentWidget::saveFile );
    dlgSave->open();
}

void QDocumentWidget::saveFileData( const QString& fileName, const QByteArray& baData )
{
    auto fileSave = QSaveFile{ fileName };
    if ( fileSave.open( QIODeviceBase::WriteOnly ) ) {

        const auto len1 = baData.length();
        const auto len2 = fileSave.write( baData );

        if ( len1 == len2 ) {

            fileSave.commit();

            // mark as clean
            m_inputEditor.setSavePoint();
            m_fileName = fileName;
            m_bReadOnly = false;

            // rename tab
            auto pMainWindow = QTrySailMainWindow::getTrysailMainWindow();
            const auto nTab = pMainWindow->m_tabDocuments.indexOf( this );
            if ( nTab >= 0 ) {
                pMainWindow->m_tabDocuments.setTabText( nTab, fileName );
            }
        }
    }
}

QByteArray QDocumentWidget::writeFileContents( const enumFileFormat fileFormat )
{
    switch ( fileFormat ) {

    case enumFileFormat::formatJuypter:
        return writeFileContentsJupyter();
        break;

    case enumFileFormat::formatPython:
        return writeFileContentsPython();
        break;

    case enumFileFormat::formatMarkdown:
    case enumFileFormat::formatText:
    case enumFileFormat::formatBash:
    case enumFileFormat::formatBatch:
    case enumFileFormat::formatSql:
    case enumFileFormat::formatJson:
    case enumFileFormat::formatHtml:
    default:
        return writeFileContentsPlain();
        break;
    }
}


void QDocumentWidget::appendMarkdown( QJsonArray& jsonCells, const int lineStart, const int lineEnd )
{
    auto jsonCell = QJsonObject{};
    jsonCell[ "cell_type" ] = u"markdown"_s;
    jsonCell[ "metadata" ] = QJsonObject{};

    auto jsonCellSource = QJsonArray{};
    for ( auto lineNumber = lineStart; lineNumber <= lineEnd; ++lineNumber ) {

        const auto code = m_inputEditor.getLine( lineNumber );
        jsonCellSource.append( QString::fromUtf8( code ) );
    }
    jsonCell[ "source" ] = jsonCellSource;
    jsonCells.append( jsonCell );
}

void QDocumentWidget::appendCode( QJsonArray& jsonCells, const int lineStart, const int lineEnd )
{
    auto jsonCell = QJsonObject{};
    jsonCell[ "cell_type" ] = u"code"_s;
    jsonCell[ "metadata" ] = QJsonObject{};

    const auto executionHandle = m_inputEditor.getExecutionHandleFromLine( lineStart );
    const auto execCount       = m_output.sequence( executionHandle );
    jsonCell[ "execution_count" ] = (execCount > -1) ? u"%1"_s.arg( execCount ) : QString{};
    auto jsonCellSource = QJsonArray{};
    for ( auto lineNumber = lineStart; lineNumber <= lineEnd; ++lineNumber ) {

        const auto code = m_inputEditor.getLine( lineNumber );

        jsonCellSource.append( QString::fromUtf8( code ) );
    }
    jsonCell[ "source" ] = jsonCellSource;


    auto jsonCellOutputs = QJsonArray{};
    for ( const auto& pElement : m_output.elements( executionHandle ) ) {

        const auto pTextElement = qobject_cast< QOutputTextElement* >( pElement );
        if ( pTextElement != nullptr ) {

            if ( pTextElement->m_Error ) {

                auto jsonCellOutputError = QJsonObject{};
                jsonCellOutputError[ "output_type" ] = u"error"_s;
                jsonCellOutputError[ "ename" ] = pTextElement->m_text;
                jsonCellOutputError[ "evalue" ] = pTextElement->m_text; // qqq save number?
                jsonCellOutputs.append( jsonCellOutputError );

            } else {

                // stream version
                {
                    auto jsonCellOutputStream = QJsonObject{};
                    jsonCellOutputStream[ "output_type" ] = u"stream"_s;
                    jsonCellOutputStream[ "name" ] = u"stdout"_s;
                    jsonCellOutputStream[ "text" ] = pTextElement->m_text;
                    jsonCellOutputs.append( jsonCellOutputStream );
                }

                // display data
                {
                    auto jsonCellOutputDisplay = QJsonObject{};
                    jsonCellOutputDisplay[ "output_type" ] = u"display_data"_s;
                    auto jsonCellOutputDisplayData = QJsonObject{};
                    jsonCellOutputDisplayData[ "text/plain" ] = pTextElement->m_text;
                    jsonCellOutputDisplay[ "data" ] = jsonCellOutputDisplayData;
                    jsonCellOutputs.append( jsonCellOutputDisplay );
                }

            }
        }

        const auto pImageElement = qobject_cast< QOutputImageElement* >( pElement );
        if ( pImageElement != nullptr ) {

            auto jsonCellOutputDisplay = QJsonObject{};
            jsonCellOutputDisplay[ "output_type" ] = u"display_data"_s;
            auto jsonCellOutputDisplayData = QJsonObject{};
            auto baPng = QByteArray{};
            auto buffer = QBuffer{ &baPng };
            buffer.open(QIODevice::WriteOnly);
            pImageElement->m_img.save(&buffer, "PNG");
            const auto baPng64 = baPng.toBase64();
            jsonCellOutputDisplayData[ "image/png" ] = QString::fromUtf8( baPng64 );
            jsonCellOutputDisplay[ "data" ] = jsonCellOutputDisplayData;
            jsonCellOutputs.append( jsonCellOutputDisplay );
        }

        const auto pSvgElement = qobject_cast< QOutputSvgElement* >( pElement ); // qqq ?
        if ( pSvgElement != nullptr ) {

            auto renderer = QSvgRenderer{ pSvgElement->m_baSvg };

            auto image = QImage{ renderer.defaultSize(), QImage::Format_ARGB32 };
            auto painter = QPainter{ &image };
            renderer.render( &painter );

            auto jsonCellOutputDisplay = QJsonObject{};
            jsonCellOutputDisplay[ "output_type" ] = u"display_data"_s;
            auto jsonCellOutputDisplayData = QJsonObject{};
            auto baPng = QByteArray{};
            auto buffer = QBuffer{ &baPng };
            buffer.open(QIODevice::WriteOnly);
            image.save(&buffer, "PNG");
            const auto baPng64 = baPng.toBase64();
            jsonCellOutputDisplayData[ "image/png" ] = QString::fromUtf8( baPng64 );
            jsonCellOutputDisplay[ "data" ] = jsonCellOutputDisplayData;
            jsonCellOutputs.append( jsonCellOutputDisplay );
        }
    }
    jsonCell[ "outputs" ] = jsonCellOutputs;
    jsonCells.append( jsonCell );
}

QByteArray QDocumentWidget::writeFileContentsJupyter()
{
    auto jsonCells = QJsonArray{};

    for ( auto lineStart = 0; lineStart < m_inputEditor.lineCount(); ) {

        const auto lineEnd = m_inputEditor.findEndFromLine( lineStart );

        const auto typeCurrent = m_inputEditor.getLineType( lineStart );

        if ( typeCurrent == enumLineType::lineTypeMarkdown ) {

            appendMarkdown( jsonCells, lineStart, lineEnd );

        } else {

            appendCode( jsonCells, lineStart, lineEnd );

        }

        lineStart = lineEnd + 1;
    }


    auto jsonMetaData = QJsonObject{};
    //auto jsonMetaDataKernelSpec = QJsonObject{};
    // jsonMetaDataKernelSpec[ "display_name" ] = "Python 3";
    // jsonMetaDataKernelSpec[ "language" ] = "python";
    // jsonMetaDataKernelSpec[ "name" ] = "python3";
    //jsonMetaData[ "kernelspec"] = jsonMetaDataKernelSpec;

    auto jsonMain = QJsonObject{};
    jsonMain[ "metadata" ] = jsonMetaData;

                       // "language_info": {
                       //     "codemirror_mode": {
                       //         "name": "ipython",
                       //         "version": 3
                       //     },
                       //     "file_extension": ".py",
                       //     "mimetype": "text/x-python",
                       //     "name": "python",
                       //     "nbconvert_exporter": "python",
                       //     "pygments_lexer": "ipython3",
                       //     "version": "3.5.4"
                       // }

    jsonMain[ "nbformat" ] = 4;
    jsonMain[ "nbformat_minor" ] = 0;
    jsonMain[ "cells" ] = jsonCells;

    const auto jsonDoc = QJsonDocument{ jsonMain };
    const auto baData = jsonDoc.toJson();
    return baData;
}

QByteArray QDocumentWidget::writeFileContentsPython()
{
    auto baData = QByteArray{};
    auto typePrior = enumLineType::lineTypeCode;
    for ( auto lineNumber = 0; lineNumber < m_inputEditor.lineCount(); ++lineNumber ) {

        const auto typeCurrent = m_inputEditor.getLineType( lineNumber );
        if ( typeCurrent != typePrior ) {

            if ( typeCurrent == enumLineType::lineTypeMarkdown ) {

                // start markdown
                baData.append( "# %% [markdown]\n" );

            } else {

                // end markdown
                baData.append( "# %%\n" );
            }
            typePrior = typeCurrent;
        }
        const auto codeCurrent = m_inputEditor.getLine( lineNumber );
        baData.append( codeCurrent );
    }
    // ending on markdown?
    if ( typePrior == enumLineType::lineTypeMarkdown ) {

        baData.append( "# %%" );
    }
    return baData;
}

QByteArray QDocumentWidget::writeFileContentsPlain()
{
    // dump all contents
    const auto len = m_inputEditor.length();
    const auto baData = m_inputEditor.getText( len );
    return baData;
}

void QDocumentWidget::saveFile( const QString& fileName )
{
    const auto fileInfo = QFileInfo{ fileName };
    const auto suffix   = fileInfo.suffix();

    if ( suffix.compare( SUFFIX_IPYNB, Qt::CaseSensitivity::CaseInsensitive ) == 0  ) {

        const auto baData = writeFileContentsJupyter();
        saveFileData( fileName, baData );

    } else if ( suffix.compare( SUFFIX_PY, Qt::CaseSensitivity::CaseInsensitive ) == 0  ) {

        const auto baData = writeFileContentsPython();
        saveFileData( fileName, baData );

    } else {

        const auto baData = writeFileContentsPlain();
        saveFileData( fileName, baData );
    }

    auto mainWindow = QTrySailMainWindow::getTrysailMainWindow();
    mainWindow->addFileToRecentFiles( fileName );
}

void QDocumentWidget::onFilePrint()
{
    if ( ! isFocal() )
        return;

    QMessageBox::information( this, tr("Print"), tr("Print (TODO)") );
}

void QDocumentWidget::onFilePdf()
{
    if ( ! isFocal() )
        return;

    auto pdfFileName = QFileDialog::getSaveFileName( this, tr("Export PDF"), QString{}, u"*.pdf"_s );
    if ( !pdfFileName.isEmpty() ) {

        const auto fiPdf = QFileInfo{ pdfFileName };

        if ( fiPdf.suffix().isEmpty() ) {
            pdfFileName.append( u".pdf"_s );
        }

        auto tempDoc = m_outputView.document();
        const auto sizePageSave  = tempDoc->pageSize();
        const auto timestampPrinted = QDateTime::currentDateTime();

        constexpr auto heightHeader = 27;
        constexpr auto heightFooter = 27;
        constexpr auto marginLR     = 18;
        constexpr auto marginTB     = 18;
        constexpr auto margins      = QMarginsF{ marginLR, marginTB + heightHeader, marginLR, marginTB + heightFooter };

        const auto sUserName = QConfigDialog::userName();
        auto printer = QPrinter{ QPrinter::PrinterResolution };
        printer.setPageSize( QPageSize::Letter );
        printer.setPageMargins( margins, QPageLayout::Point);
        printer.setOutputFormat( QPrinter::PdfFormat );
        printer.setOutputFileName( pdfFileName );

        const auto sizePageF     = printer.pageRect( QPrinter::DevicePixel ).size();
        tempDoc->setPageSize( sizePageF );
        const auto sizePage      = tempDoc->pageSize().toSize();

        const auto pageCount  = tempDoc->pageCount();
        const auto rectPageF  = QRectF{ 0, 0, sizePageF.width(), sizePageF.height() };
        const auto rectHeader = QRect{ 0, 0 -heightHeader, rectPageF.toRect().width(), heightHeader };
        const auto rectFooter = QRect{ 0, rectPageF.toRect().height(), rectPageF.toRect().width(), heightFooter };
        auto painter = QPainter{ &printer };
        for ( auto pageNo = 0; pageNo < pageCount; ++pageNo ) {

            if ( pageNo > 0 )
                printer.newPage();

            const auto rectText = QRect{ 0, pageNo * sizePage.height(), sizePage.width(), sizePage.height() };
            painter.save();
            painter.setClipRect( rectPageF );
            painter.translate( 0, -rectText.top() );
            tempDoc->drawContents( &painter );
            painter.restore();

            painter.drawLine( rectHeader.left(), rectHeader.top() + heightHeader, rectHeader.right(), rectHeader.top() + heightHeader );
            painter.drawText( rectHeader, Qt::AlignTop   | Qt::AlignLeft, fileName() );
            painter.drawText( rectHeader, Qt::AlignTop   | Qt::AlignRight, timestampPrinted.toString( Qt::DateFormat::TextDate ) );

            painter.drawLine( rectFooter.left(), rectFooter.top(), rectFooter.right(), rectFooter.top() );
            painter.drawText( rectFooter, Qt::AlignBottom | Qt::AlignRight, tr("Page %1/%2").arg(pageNo+1).arg(pageCount));
            painter.drawText( rectFooter, Qt::AlignBottom | Qt::AlignLeft,  sUserName );
        }
        tempDoc->setPageSize( sizePageSave );

        //m_outputView.print( &printer );
    }
}
void QDocumentWidget::onEditUndo()
{
    if ( ! isFocal() )
        return;

    // qqq
    m_inputEditor.undo();
}
void QDocumentWidget::onEditRedo()
{
    if ( ! isFocal() )
        return;

    m_inputEditor.redo();
}

void QDocumentWidget::onViewToggleMemoryVisible()
{
    if ( ! isFocal() )
        return;

    m_inputEditor.toggleMemoryVisible();
}

void QDocumentWidget::readFileContents( const enumFileFormat fileFormat, const QByteArray& baFileContents )
{
    switch ( fileFormat ) {

    case enumFileFormat::formatJuypter:
        readFileContentsJupyter( baFileContents );
        break;

    case enumFileFormat::formatPython:
        readFileContentsPython( baFileContents );
        break;

    case enumFileFormat::formatMarkdown:
        readFileContentsMarkdown( baFileContents );
        break;

    case enumFileFormat::formatSql:
    case enumFileFormat::formatText:
    case enumFileFormat::formatBash:
    case enumFileFormat::formatBatch:
    case enumFileFormat::formatJson:
    case enumFileFormat::formatHtml:
        readFileContentsPlain( baFileContents );
        break;
    }

    m_inputEditor.emptyUndoBuffer();
    m_inputEditor.setSavePoint();
    m_inputEditor.setSel( 0, 0 );
    m_inputEditor.setFilePath( m_fileName );

    // Get workspace root (directory containing the file)
    QFileInfo fileInfo( m_fileName );
    QString workspaceRoot = fileInfo.absoluteDir().absolutePath();

    // Initialize LSP
    m_inputEditor.initializeLsp( workspaceRoot );

    // sync
    connect( &m_inputEditor, &ScintillaEdit::updateUi, &m_outputView, &QOutputWidget::onUpdateUi );

    // rebuild on change
    m_inputEditor.setModEventMask( SC_MOD_INSERTTEXT | SC_MOD_DELETETEXT);
    connect( &m_inputEditor, &ScintillaEdit::modified, &m_outputView, &QOutputWidget::onModified );
    m_outputView.requestRebuild();
}

void QDocumentWidget::readFileContentsJupyter( const QByteArray& baFileContents )
{
    // read existing JSON
    auto       jsonError = QJsonParseError{};
    const auto jsonDoc   = QJsonDocument::fromJson( baFileContents, &jsonError );

    if ( jsonDoc.isObject() ) {

        const auto jsonObject  = jsonDoc.object();
        const auto objectCells = jsonObject.value( u"cells"_s );
        const auto arrayCells  = objectCells.toArray();

        for ( const auto& cell : arrayCells ) {
            const auto cellObject     = cell.toObject();
            const auto cellTypeString = cellObject.value( u"cell_type"_s ).toString();
            const auto cellType       = ( cellTypeString == u"markdown"_s ) ? enumLineType::lineTypeMarkdown : enumLineType::lineTypeCode;

            // source
            const auto cellSourceArray = cellObject.value( u"source"_s ).toArray();
            for ( auto nCellSourceLine = 0; nCellSourceLine < cellSourceArray.size(); ++nCellSourceLine ) {

                const auto cellSourceLine       = cellSourceArray.at( nCellSourceLine );
                const auto cellSourceLineString = cellSourceLine.toString();
                m_inputEditor.addSourceText( cellType, cellSourceLineString );
            }

            const auto lastLine        = m_inputEditor.lineCount() - 1;
            const auto executionHandle = m_inputEditor.getExecutionHandleFromLine( lastLine );

            // outputs
            auto       sStreamDisplay  = QString{};
            auto       sStreamError    = QString{};
            const auto cellOutputArray = cellObject.value( u"outputs"_s ).toArray();
            for ( auto nCellOutputLine = 0; nCellOutputLine < cellOutputArray.size(); ++nCellOutputLine ) {

                const auto cellOutputObject = cellOutputArray.at( nCellOutputLine ).toObject();
                const auto cellOutputType   = cellOutputObject.value( u"output_type"_s ).toString();
                if ( cellOutputType == u"stream"_s ) {

                    const auto cellOutputName      = cellOutputObject.value( u"name"_s ).toString();
                    const auto bError              = cellOutputName == u"stderr"_s;
                    const auto cellOutputTextArray = cellObject.value( u"text"_s ).toArray();
                    for ( auto nCellOutputTextLine = 0; nCellOutputTextLine < cellOutputTextArray.size(); ++nCellOutputTextLine ) {

                        const auto cellOutputTextLine = cellOutputTextArray.at( nCellOutputTextLine );
                        const auto cellOutputTextLineString = cellOutputTextLine.toString();
                        if ( bError )
                            sStreamError += cellOutputTextLineString;
                        else
                            sStreamDisplay += cellOutputTextLineString;
                    }

                } else if ( ( cellOutputType == u"display_data"_s ) || ( cellOutputType == u"execute_result"_s ) ) {

                    const auto cellOutputData  = cellOutputObject.value( u"data"_s ).toObject();
                    const auto cellOutputImage = cellOutputData.value( u"image/png"_s );
                    if ( !cellOutputImage.isUndefined() ) {

                        const auto sString   = cellOutputImage.toString();
                        const auto arrayByte = QByteArray::fromBase64( sString.toUtf8() );
                        auto       img       = QImage{};
                        img.loadFromData( arrayByte );
                        m_output.addImageOutput( executionHandle, img );
                    }
                    auto       sDataHtml           = QString{};
                    const auto cellOutputHtmlArray = cellOutputData.value( u"text/html"_s ).toArray();
                    if ( !cellOutputHtmlArray.isEmpty() ) {

                        auto sStringList = QStringList{};
                        for ( auto cellOutputHtmlArrayLine : cellOutputHtmlArray ) {

                            const auto sString = cellOutputHtmlArrayLine.toString();
                            sStringList.append( sString );
                        }
                        sDataHtml = sStringList.join( QString{} );
                    }
                    auto sDataPlain = QString{};
                    if ( sDataHtml.isEmpty() ) {
                        const auto cellOutputPlainArray = cellOutputData.value( u"text/plain"_s ).toArray();
                        if ( !cellOutputPlainArray.isEmpty() ) {

                            auto sStringList = QStringList{};
                            for ( auto cellOutputPlainArrayLine : cellOutputPlainArray ) {

                                const auto sString = cellOutputPlainArrayLine.toString();
                                sStringList.append( sString );
                            }
                            sDataPlain = sStringList.join( QString{} );
                        }
                    }
                    const auto formatContent = ( sDataHtml.isEmpty() ) ? enumContentFormat::contentFormatPlain : enumContentFormat::contentFormatHtml;
                    m_output.addTextOutput( executionHandle, sDataPlain, formatContent, false );
                }
            }
        }
    }
}

void QDocumentWidget::readFileContentsPython( const QByteArray& baFileContents )
{
    auto lineTypeCurrent = enumLineType::lineTypeCode;
    auto stream          = QTextStream{ baFileContents };

    auto line = QString{};
    while ( stream.readLineInto( &line ) ) {

        // ignore cell starts/stops if markdown but use to change linetype
        if ( line.startsWith( u"# %% [markdown]"_s ) ) {
            lineTypeCurrent = enumLineType::lineTypeMarkdown;
            continue;

        } else if ( (lineTypeCurrent == enumLineType::lineTypeMarkdown) && line.startsWith( u"# %%"_s ) ) {
            lineTypeCurrent = enumLineType::lineTypeCode;
            continue;
        }

        // add any other lines as the current line type
        m_inputEditor.addSourceText( lineTypeCurrent, line );
    }
}

void QDocumentWidget::readFileContentsPlain( const QByteArray& baFileContents )
{
    auto stream = QTextStream( baFileContents );
    auto line = QString{};
    while ( stream.readLineInto( &line ) ) {

        m_inputEditor.addSourceText( enumLineType::lineTypeCode, line );
    }
}

void QDocumentWidget::readFileContentsMarkdown( const QByteArray& baFileContents )
{
    auto stream = QTextStream( baFileContents );
    auto line = QString{};
    while ( stream.readLineInto( &line ) ) {

        m_inputEditor.addSourceText( enumLineType::lineTypeMarkdown, line );
    }
}


void QDocumentWidget::onEditFind()
{
    if ( ! isFocal() )
        return;

    const auto dlgSearch = new QSearchDialog{ m_contextSearch, this };
    dlgSearch->setAttribute( Qt::WA_DeleteOnClose );
    connect( dlgSearch, &QSearchDialog::search, this, &QDocumentWidget::onSearch );
    dlgSearch->open();
}

void QDocumentWidget::onSearch( const QSearchDialog::SearchContext& contextSearch, const QSearchDialog::enumSearchAction actionSearch )
{
    m_contextSearch = contextSearch;

    auto flagsSearch = SCFIND_NONE;
    if ( contextSearch.m_MatchCase == Qt::CheckState::Checked )
        flagsSearch += SCFIND_MATCHCASE;

    if ( contextSearch.m_WholeWord == Qt::CheckState::Checked )
        flagsSearch += SCFIND_WHOLEWORD;
    m_inputEditor.setSearchFlags( flagsSearch );

    const auto bSearchForward = (actionSearch != QSearchDialog::enumSearchAction::searchFindPrevious);
    const auto posCurrent = m_inputEditor.currentPos();
    if ( bSearchForward ) {

        const auto posEnd = m_inputEditor.length() - 1;
        m_inputEditor.setTargetStart( posCurrent );
        m_inputEditor.setTargetEnd( posEnd );

    } else {

        m_inputEditor.setTargetStart( posCurrent - 1 ); // move back one to keep from selecting the same text on repeated previous searches
        m_inputEditor.setTargetEnd( 0 );

    }

    const auto utfFind = contextSearch.m_Find.toUtf8();
    const auto resultSearch = m_inputEditor.searchInTarget( utfFind.length(), utfFind.constData() );
    if ( resultSearch > -1 ) {

        switch ( actionSearch ) {

        case QSearchDialog::enumSearchAction::searchFindNext:
        case QSearchDialog::enumSearchAction::searchFindPrevious:
            m_inputEditor.setSel( m_inputEditor.targetStart(), m_inputEditor.targetEnd() );
            break;

        case QSearchDialog::enumSearchAction::searchFindReplaceOne:
        case QSearchDialog::enumSearchAction::searchFindReplaceAll: {

            const auto utfReplace = contextSearch.m_Replace.toUtf8();
            m_inputEditor.replaceTarget( utfReplace.length(), utfReplace.constData() );
            m_inputEditor.setSel( m_inputEditor.targetStart(), m_inputEditor.targetEnd() );
        }
            if ( actionSearch == QSearchDialog::enumSearchAction::searchFindReplaceAll ) {

                onSearch( contextSearch, actionSearch ); // recursive until no longer found

            }
            break;

        default:
            break;
        }
    }
}

void QDocumentWidget::onEditFindNext()
{
    if ( ! isFocal() )
        return;

    onSearch( m_contextSearch, QSearchDialog::enumSearchAction::searchFindNext );
}

void QDocumentWidget::onEditFindPrevious()
{
    if ( ! isFocal() )
        return;

    onSearch( m_contextSearch, QSearchDialog::enumSearchAction::searchFindPrevious );
}
