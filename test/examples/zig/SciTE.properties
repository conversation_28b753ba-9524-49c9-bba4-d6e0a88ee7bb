lexer.*.zig=zig
fold=1
keywords.*.zig=False None True _ and as assert async await break case class continue def del elif else except finally for from global if import in is lambda match nonlocal not or pass raise return try while with yield addrspace align allowzero and anyframe anytype asm async await break callconv catch comptime const continue defer else enum errdefer error export extern false fn for if inline linksection noalias noinline nosuspend null opaque or orelse packed pub resume return struct suspend switch test threadlocal true try undefined union unreachable usingnamespace var volatile while
keywords2.*.zig=anyerror anyopaque bool f128 f16 f32 f64 f80 i128 i16 i32 i64 i8 isize noreturn type u128 u16 u32 u64 u8 usize void
keywords3.*.zig=Timestamp
keywords4.*.zig=Vec3
