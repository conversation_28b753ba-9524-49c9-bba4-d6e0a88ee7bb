{5}def{0} {9}dbg_args{10}({11}a{10},{0} {11}b{10}={4}1{10},{0} {11}c{14}:{10},{0} {14}d:{0} {4}6{10},{0} {10}&{11}block{10}){0} {10}={0} {11}puts{10}({6}"Args passed: {10}#{[{11}a{10},{0} {11}b{10},{0} {11}c{10},{0} {11}d{10},{0} {11}block{10}.{11}call{10}]}{6}"{10}){0}
{11}dbg_args{10}({4}0{10},{0} {14}c:{0} {4}5{10}){0} {10}{{0} {4}7{0} {10}}{0}

{5}class{0} {8}A{0}
	{5}def{0} {9}attr{0} {10}={0} {16}@attr{0}
	{5}def{0} {9}attr={10}({11}value{10}){0}
		{16}@attr{0} {10}={0} {11}value{0}
	{5}end{0}
	{5}def{0} {9}attr?{0} {10}={0} {10}!!{16}@attr{0}
	{5}def{0} {9}attr!{0} {10}={0} {16}@attr{0} {10}={0} {5}true{0}
	{2}# unary operator{0}
	{5}def{0} {10}-@{0} {10}={0} {4}1{0}
	{5}def{0} {10}+@{0} {10}={0} {4}1{0}
	{5}def{0} {10}!{0} {10}={0} {4}1{0}
	{5}def{0} {10}!@{0} {10}={0} {4}1{0}
	{2}# binary operator{0}
	{5}def{0} {10}+({11}value{10}){0} {10}={0} {4}1{0} {10}+{0} {11}value{0}
	{5}def{0} {10}-({11}value{10}){0} {10}={0} {4}1{0} {10}-{0} {11}value{0}
	{5}def{0} {10}*({11}value{10}){0} {10}={0} {4}1{0} {10}*{0} {11}value{0}
	{5}def{0} {10}**({11}value{10}){0} {10}={0} {4}1{0} {10}**{0} {11}value{0}
	{5}def{0} {10}/({11}value{10}){0} {10}={0} {4}1{0} {10}/{0} {11}value{0}
	{5}def{0} {10}%({11}value{10}){0} {10}={0} {4}1{0} {10}%{0} {11}value{0}
	{5}def{0} {10}&({11}value{10}){0} {10}={0} {4}1{0} {10}&{0} {11}value{0}
	{5}def{0} {10}^({11}value{10}){0} {10}={0} {4}1{0} {10}^{0} {11}value{0}
	{5}def{0} {10}>>({11}value{10}){0} {10}={0} {4}1{0} {10}>>{0} {11}value{0}
	{5}def{0} {10}<<({11}value{10}){0} {10}={0} {4}1{0} {10}<<{0} {11}value{0}
	{5}def{0} {10}==({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}!=({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}===({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}=~({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}<=>({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}<({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}<=({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}>({11}other{10}){0} {10}={0} {5}true{0}
	{5}def{0} {10}>=({11}other{10}){0} {10}={0} {5}true{0}
	{2}# element reference and assignment{0}
	{5}def{0} {10}[]({11}a{10},{0} {11}b{10}){0} {10}={0} {11}puts{10}({11}a{0} {10}+{0} {11}b{10}){0}
	{5}def{0} {10}[]=({11}a{10},{0} {11}b{10},{0} {11}c{10}){0}
		{11}puts{0} {11}a{0} {10}+{0} {11}b{0} {10}+{0} {11}c{0}
	{5}end{0}
	{2}# array decomposition{0}
	{5}def{0} {9}dec{10}((({11}a{10},{0} {11}b{10}),{0} {11}c{10})){0} {10}={0} {11}puts{10}({11}a{0} {10}+{0} {11}b{0} {10}+{0} {11}c{10}){0}
	{2}# class method{0}
	{5}def{0} {29}self{10}::{9}say{10}(*{11}s{10}){0} {10}={0} {11}puts{10}({11}s{10}){0}
	{5}def{0} {29}self{10}.{9}say{10}(*{11}s{10}){0} {10}={0} {11}puts{10}({11}s{10}){0}
	{2}# test short method name{0}
	{5}def{0} {9}a{0} {10}={0} {4}1{0}
	{5}def{0} {9}ab{0} {10}={0} {4}1{0}
{5}end{0}

{2}# class method{0}
{5}def{0} {11}String{10}.{9}hello{0}
  {6}"Hello, world!"{0}
{5}end{0}
{2}# singleton method{0}
{11}greeting{0} {10}={0} {6}"Hello"{0}
{5}def{0} {11}greeting{10}.{9}broaden{0}
  {5}self{0} {10}+{0} {6}", world!"{0}
{5}end{0}
{2}# one line definition{0}
{5}def{0} {9}a{10}({11}b{10},{0} {11}c{10}){0} {11}b{10};{0} {11}c{0} {5}end{0}
{2}# parentheses omitted{0}
{5}def{0} {9}ab{0} {11}c{0}
	{11}puts{0} {11}c{0}
{5}end{0}

{2}# Test folding of multi-line SCE_RB_STRING_QW{0}
{11}puts{0} {28}%W(
a
b
c
){0}
