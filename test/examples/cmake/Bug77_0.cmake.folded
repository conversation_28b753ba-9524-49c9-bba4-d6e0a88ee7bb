 2 400 401 + if(MSVC80)
 0 401 401 |   # 1
 0 401 401 | elseif(MSVC90)
 0 401 401 |   # 2
 0 401 401 | elseif(APPLE)
 0 401 401 |   # 3
 0 401 401 | else()
 0 401 401 |   # 4
 0 401 400 | endif()
 0 400 400   
 2 400 401 + if(MSVC80)
 0 401 401 |   # 1
 0 401 401 | elseif(MSVC90)
 0 401 401 |   # 2
 0 401 400 | endif()
 0 400 400   
 2 400 401 + if(MSVC80)
 0 401 401 |   # 1
 0 401 401 | else()
 0 401 401 |   # 2
 0 401 400 | endif()
 0 400 400   
 2 400 401 + if(MSVC80)
 0 401 401 |   # 1
 0 401 400 | endif()
 0 400 400   