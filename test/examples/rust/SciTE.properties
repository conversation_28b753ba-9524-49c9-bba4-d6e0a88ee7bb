lexer.*.rs=rust

keywords.*.rs= \
alignof as be box break const continue crate do else enum extern false fn for if impl in let loop match mod mut offsetof once priv proc pub pure ref return self sizeof static struct super trait true type typeof unsafe unsized use virtual while yield

keywords2.*.rs= \
bool char f32 f64 i16 i32 i64 i128 i8 int str u16 u32 u64 u128 u8 uint

keywords3.*.rs=Self

fold=1
fold.comment=1
