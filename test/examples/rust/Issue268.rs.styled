{2}// coding: utf-8{0}

{21}b"foo"{16};{0} {22}br"foo"{0}            {2}// foo{0}
{21}b"\"foo\""{16};{0} {22}br#""foo""#{16};{0}   {2}// "foo"{0}

{21}b"foo #\"# bar"{16};{0}
{22}br##"foo #"# bar"##{16};{0}       {2}// foo #"# bar{0}

{21}b"\x52"{16};{0} {21}b"R"{16};{0} {22}br"R"{0}       {2}// R{0}
{21}b"\\x52"{16};{0} {22}br"\x52"{0}         {2}// \x52{0}

{24}c"æ"{0}                       {2}// LATIN SMALL LETTER AE (U+00E6){0}
{24}c"\u{00E6}"{16};{0}
{24}c"\xC3\xA6"{16};{0}

{24}c"foo"{16};{0} {25}cr"foo"{0}           {2}// foo{0}
{24}c"\"foo\""{16};{0} {25}cr#""foo""#{16};{0}  {2}// "foo"{0}

{24}c"foo #\"# bar"{16};{0}
{25}cr##"foo #"# bar"##{16};{0}      {2}// foo #"# bar{0}

{24}c"\x52"{16};{0} {24}c"R"{16};{0} {25}cr"R"{0}      {2}// R{0}
{24}c"\\x52"{16};{0} {25}cr"\x52"{0}        {2}// \x52{0}

{13}"foo"{16};{0} {14}r"foo"{0}             {2}// foo{0}
{13}"\"foo\""{16};{0} {14}r#""foo""#{16};{0}    {2}// "foo"{0}

{13}"foo #\"# bar"{16};{0}
{14}r##"foo #"# bar"##{16};{0}       {2}// foo #"# bar{0}

{13}"\x52"{16};{0} {13}"R"{16};{0} {14}r"R"{0}         {2}// R{0}
{13}"\\x52"{16};{0} {14}r"\x52"{0}          {2}// \x52{0}

{13}"æ"{0}                       {2}// LATIN SMALL LETTER AE (U+00E6){0}
{13}"\u{00E6}"{16};{0}
{13}"\xC3\xA6"{16};