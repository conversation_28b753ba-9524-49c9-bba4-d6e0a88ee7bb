<?xml version="1.0" encoding="UTF-8"?>
<html xmlns="http://www.w3.org/1999/xhtml">
<script type="text/javascript">
var b = /abc/i.test('abc');
let b = 1;
'x\
</t>'
// issue 214 fix to behave same as single quote escaped eol
"x\
</t>"
</script>
<head>
    <meta name="Date.Modified" content="20010515" />
    <title>SinkWorld - Portability</title>
    §
    <unknown>SinkWorld - Portability</unknown>
    <img src="SciTEIco.png" height=64 width=64 />
    <link rel="stylesheet" type="text/css" from="SW.css">
    <destination href="SW.css" height=64 width=64></destination>
</head>
</html>
