{4}// Enumerate all primary styles: 0 to 14
{0}
{2}{
  SCE_PAS_DEFAULT=0
  SCE_PAS_IDENTIFIER=1
  SCE_PAS_COMMENT=2
  SCE_PAS_COMMENT2=3
  SCE_PAS_COMMENTLINE=4
  SCE_PAS_PREPROCESSOR=5
  SCE_PAS_PREPROCESSOR2=6
  SCE_PAS_NUMBER=7
  SCE_PAS_HEXNUMBER=8
  SCE_PAS_WORD=9
  SCE_PAS_STRING=10
  SCE_PAS_STRINGEOL=11
  SCE_PAS_CHARACTER=12
  SCE_PAS_OPERATOR=13
  SCE_PAS_ASM=14
}{0}

{4}// default=0
{0}   
{4}// identifier=1
{9}function{0} {1}functionname{13}({9}var{0} {1}paramerter1{13}:{0} {1}type1{13}):{1}result1{13};{0}
{9}procedure{0} {1}procedurename{13}({9}const{0} {1}parameter2{13}:{0} {1}type2{13});{0}

{4}// comment=2
{2}{comment text}{0}

{4}// comment2=3
{3}(* comment text *){0}

{4}// commentline=4 
// example line
{0}
{4}// preprocessor=5
{5}{$DEFINE xyz}{0}

{5}{$IFDEF xyz}{0}
   {1}codeblock{0} {7}1{0}
{5}{$else}{0}
   {1}codeblock{0} {7}2{0}
{5}{$endif}{0}

{4}// preprocessor2=6
{6}(*$DEFINE xyz*){0}

{4}// number=7
{7}123{0}
{7}1.23{0}
{13}-{7}123{0}
{13}-{7}12.3{0}
{13}+{7}123{0}
{7}123{0}
{7}1.23e2{0}
{13}-{7}1.23E2{0}

{4}// hexnumber=8
{8}$123{0}
{8}$123ABCDEF{0}
{8}$ABCDEF123{0}

{4}// word=9
{9}absolute{0} {9}abstract{0} {9}and{0} {9}array{0} {9}as{0} 

{4}// string=10
{10}'string'{0}

{4}// stringeol=11
{11}'string
{0}
{4}// character=12
{12}#65{0}

{4}// operator=13 
{8}${0} {13}&{0} {13}*{0} {13}+{0} {13}/{0} {13}<{0} {13}={0} {13}>{0} {13}^{0}  

{4}// asm
{9}asm{14} 
  this is 
  inside assembler
{9}end{0}
