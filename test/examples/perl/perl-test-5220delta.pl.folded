 2 400 401 + # -*- coding: utf-8 -*-
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # perl-test-5220delta.pl
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # REF: https://metacpan.org/pod/distribution/perl/pod/perldelta.pod
 0 401 401 | # maybe future ref: https://metacpan.org/pod/distribution/perl/pod/perl5220delta.pod
 0 401 401 | # also: http://perltricks.com/article/165/2015/4/10/A-preview-of-Perl-5-22
 0 401 401 | #
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Kein-<PERSON> Man <<EMAIL>> Public Domain 20151217
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # 20151217	initial document
 0 401 401 | # 20151218	updated tests and comments
 0 401 400 | #--------------------------------------------------------------------------
 1 <USER> <GROUP>   
 0 400 400   use v5.22;			# may be needed
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # New bitwise operators
 0 401 400 | #--------------------------------------------------------------------------
 1 <USER> <GROUP>   
 0 400 400   use feature 'bitwise'		# enable feature, warning enabled
 0 400 400   use experimental "bitwise";	# enable feature, warning disabled
 1 400 400   
 0 400 400   # numerical operands
 0 400 400   10&20  10|20   10^20 ~10
 0 400 400   $a&"8" $a|"8" $a^"8" ~$a ~"8"
 1 400 400   
 0 400 400   # string operands
 0 400 400   '0'&."8" '0'|."8" '0'^."8" ~.'0' ~."8"
 2 400 401 + # the following is AMBIGUOUS, perl sees 10 and not .10 only when bitwise feature is enabled
 0 401 400 | # so it's feature-setting-dependent, no plans to change current behaviour
 0 400 400    $a&.10   $a|.10   $a^.10  ~.$a  ~.10
 1 400 400   
 0 400 400   # assignment variants
 0 400 400   $a&=10;    $a|=10;    $a^=10;
 0 400 400   $b&.='20'; $b|.='20'; $b^.='20';
 0 400 400   $c&="30";  $c|="30";  $c^="30";
 0 400 400   $d&.=$e;   $d|.=$e;   $d^.=$e;
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # New double-diamond operator
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # <<>> is like <> but each element of @ARGV will be treated as an actual file name
 1 400 400   
 0 400 400   # example snippet from brian d foy's blog post
 2 400 401 + while( <<>> ) {  # new, safe line input operator
 0 401 401 | 	...;
 0 401 400 | 	}
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # New \b boundaries in regular expressions
 0 401 400 | #--------------------------------------------------------------------------
 1 <USER> <GROUP>   
 0 400 400   qr/\b{gcb}/
 0 400 400   qr/\b{wb}/
 0 400 400   qr/\b{sb}/
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Non-Capturing Regular Expression Flag
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # disables capturing and filling in $1, $2, etc
 1 400 400   
 0 400 400   "hello" =~ /(hi|hello)/n; # $1 is not set
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Aliasing via reference
 0 401 401 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Variables and subroutines can now be aliased by assigning to a reference
 1 400 400   
 0 400 400   \$c = \$d;
 0 400 400   \&x = \&y;
 1 400 400   
 0 400 400   # Aliasing can also be applied to foreach iterator variables
 1 400 400   
 0 400 400   foreach \%hash (@array_of_hash_refs) { ... }
 1 400 400   
 0 400 400   # example snippet from brian d foy's blog post
 1 400 400   
 0 400 400   use feature qw(refaliasing);
 1 400 400   
 0 400 400   \%other_hash = \%hash;
 1 400 400   
 0 400 400   use v5.22;
 0 400 400   use feature qw(refaliasing);
 1 400 400   
 2 400 401 + foreach \my %hash ( @array_of_hashes ) { # named hash control variable
 2 401 402 + 	foreach my $key ( keys %hash ) { # named hash now!
 0 402 402 | 		...;
 0 402 401 | 		}
 0 401 400 | 	}
 1 400 400   
 2 400 401 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # New :const subroutine attribute
 0 401 400 | #--------------------------------------------------------------------------
 1 <USER> <GROUP>   
 0 400 400   my $x = 54321;
 0 400 400   *INLINED = sub : const { $x };
 0 400 400   $x++;
 1 400 400   
 2 400 401 + # more examples of attributes
 0 401 401 | # (not 5.22 stuff, but some general examples for study, useful for
 0 401 400 | #  handling subroutine signature and subroutine prototype highlighting)
 1 400 400   
 0 400 400   sub foo : lvalue ;
 1 400 400   
 2 400 401 + package X;
 0 401 401 | sub Y::x : lvalue { 1 }
 1 401 401 | 
 2 400 401 + package X;
 0 401 401 | sub foo { 1 }
 2 400 401 + package Y;
 0 401 401 | BEGIN { *bar = \&X::foo; }
 2 400 401 + package Z;
 0 401 401 | sub Y::bar : lvalue ;
 1 401 401 | 
 0 401 401 | # built-in attributes for subroutines:
 0 401 401 | lvalue method prototype(..) locked const
 1 401 401 | 
 2 401 402 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Repetition in list assignment
 0 402 401 | #--------------------------------------------------------------------------
 1 <USER> <GROUP> | 
 0 401 401 | # example snippet from brian d foy's blog post
 0 401 401 | use v5.22;
 0 401 401 | my(undef, $card_num, (undef)x3, $count) = split /:/;
 1 401 401 | 
 0 401 401 | (undef,undef,$foo) = that_function()
 0 401 401 | # is equivalent to 
 0 401 401 | ((undef)x2, $foo) = that_function()
 1 401 401 | 
 2 401 402 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Floating point parsing has been improved
 0 402 402 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Hexadecimal floating point literals
 1 401 401 | 
 2 401 402 + # some hex floats from a program by Rick Regan
 0 402 402 | # appropriated and extended from Lua 5.2.x test cases
 0 402 401 | # tested on perl 5.22/cygwin
 1 401 401 | 
 0 401 401 | 0x1p-1074;
 0 401 401 | 0x3.3333333333334p-5;
 0 401 401 | 0xcc.ccccccccccdp-11;
 0 401 401 | 0x1p+1;
 0 401 401 | 0x1p-6;
 0 401 401 | 0x1.b7p-1;
 0 401 401 | 0x1.fffffffffffffp+1023;
 0 401 401 | 0x1p-1022;
 0 401 401 | 0X1.921FB4D12D84AP+1;
 0 401 401 | 0x1.999999999999ap-4;
 1 401 401 | 
 0 401 401 | # additional test cases for characterization
 0 401 401 | 0x1p-1074.		# dot is a string operator
 0 401 401 | 0x.ABCDEFp10		# legal, dot immediately after 0x
 0 401 401 | 0x.p10			# perl allows 0x as a zero, then concat with p10 bareword
 0 401 401 | 0x.p 0x0.p		# dot then bareword
 0 401 401 | 0x_0_.A_BC___DEF_p1_0	# legal hex float, underscores are mostly allowed
 0 401 401 | 0x0._ABCDEFp10		# _ABCDEFp10 is a bareword, no underscore allowed after dot
 1 401 401 | 
 0 401 401 | # illegal, but does not use error highlighting
 0 401 401 | 0x0p1ABC		# illegal, highlighted as 0x0p1 abut with bareword ABC 
 1 401 401 | 
 0 401 401 | # allowed to FAIL for now
 0 401 401 | 0x0.ABCDEFp_10		# ABCDEFp_10 is a bareword, '_10' exponent not allowed
 0 401 401 | 0xp 0xp1 0x0.0p		# syntax errors
 0 401 401 | 0x41.65.65 		# hex dot number, but lexer now fails with 0x41.65 left as a partial hex float
 1 401 401 | 
 2 401 402 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # Support for ?PATTERN? without explicit operator has been removed
 0 402 402 | #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # ?PATTERN? must now be written as m?PATTERN?
 1 401 401 | 
 0 401 401 | ?PATTERN?	# does not work in current LexPerl anyway, NO ACTION NEEDED
 0 401 401 | m?PATTERN?
 1 401 401 | 
 2 401 402 + #--------------------------------------------------------------------------
 0 <USER> <GROUP> | # end of test file
 0 402 401 | #--------------------------------------------------------------------------
 0 <USER>   <GROUP> | 