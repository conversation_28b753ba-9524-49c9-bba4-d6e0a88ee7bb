#!/usr/bin/env bash
set -e
# -----------------------------------------------------------------------------
# Voluptatem dolore magnam eius quisquam eius dolor labore. Porro dolor amet ut.
# Numquam labore amet modi. Dolorem velit numquam porro est quiquia ipsum quisquam.
# Magnam consectetur est voluptatem aliquam adipisci. Sed dolorem quaerat quiquia.
# -----------------------------------------------------------------------------
export PYTHONPATH="scripts:$PYTHONPATH"

if [[ -z "$1" ]]; then
  PROJECT_DIR=$(rlwrap -S "Enter source path: " -e '' -i -o cat)
  PROJECT_PATH="$(pwd)/$PROJECT_DIR"
else
  PROJECT_PATH="$(pwd)/${1}"
fi

OUT_FILE=${PROJECT_PATH}/testing.txt

(cat<<EOF
  Last run $(date +'%Y-%m-%d') at $(date +'%H:%M:%S.%2N')
EOF
) > $OUT_FILE

# Issue 188, keyword before redirection operator
pwd>>$OUT_FILE

find "$PROJECT_PATH/src" -maxdepth 1 -type f |\
while read -r f; do
  {
    python3 -c "print();print('='*50)";\
    echo `basename "$f"` | tr -d 'x';\
    python3 -c "print('='*50)";\
    python3 "$f";\
  } >> $OUT_FILE
done

# Issue 137, should be shift but here-doc was detected
echo $(( x << END ))
pwd
END

# Issue 194, failed to understand character escaping so string unterminated
echo "foo `echo foo \\" bar` bar"
echo "xinput set-prop bla \"blub\" `grep bla $WRITE_APPENDIX/var/lib/path.txt | cut -d \\" -f 4`" >/some_file.sh

# Issue 194, $ before end of backticks is literal
echo `echo \$`
echo `echo \$bar\$`
echo `echo $`
echo `echo $bar$`

INVALID_NUMBER=0#0000
