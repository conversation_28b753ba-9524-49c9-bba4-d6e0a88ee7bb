lexer.*.bsh;*.zsh=bash
fold=1
fold.comment=1
keywords.*.bsh;*.zsh=case cat do done echo else esac exit export fi find for if in print pwd set setopt then while

# Can use substyles for identifiers and scalars
substyles.bash.8=1
substylewords.8.1.*.bsh=map
substyles.bash.9=1
substylewords.9.1.*.bsh=CWD

lexer.bash.styling.inside.string=0
lexer.bash.styling.inside.backticks=0
lexer.bash.styling.inside.parameter=0
lexer.bash.styling.inside.heredoc=0
lexer.bash.command.substitution=0

match Issue180.bsh
	lexer.bash.styling.inside.string=1

match Issue182.bsh
	lexer.bash.styling.inside.string=1

match Issue184.bsh
	lexer.bash.styling.inside.string=1
	lexer.bash.command.substitution=1

match Issue184Copy.bsh
	lexer.bash.styling.inside.string=1
	lexer.bash.command.substitution=1
	lexer.bash.special.parameter=*@#?-$!%<

match NestedStyledInside.bsh
	lexer.bash.styling.inside.string=1
	lexer.bash.styling.inside.backticks=1
	lexer.bash.styling.inside.parameter=1
	lexer.bash.styling.inside.heredoc=1
	lexer.bash.command.substitution=1

match NestedRich.bsh
	lexer.bash.command.substitution=2
