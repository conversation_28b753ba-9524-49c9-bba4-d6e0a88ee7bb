' coding: utf-8
' Enumerate all styles: 0 .. 12

' comment=1

' whitespace=0
	' w

' number=2
37
54.3345612e7
&B01111100
&O1163
&H_E635_9A60

' keyword=3
string

' double-quoted-string=4
"str"
"a"c

' preprocessor=5
#Const preproc = True

' operator=6
- 1

' identifier=7
identifier = 7
' non-ASCII
π_is_pi_ω = 3.14
' vb may add a type character like $ to identifiers; vbscript doesn't have this
identifier$ = "s"

' date=8
d=#05/11/2003#

' unclosed-string=9
" unclosed

' keyword2=10
key2

' keyword3=11
key3

' keyword4=12
key4
