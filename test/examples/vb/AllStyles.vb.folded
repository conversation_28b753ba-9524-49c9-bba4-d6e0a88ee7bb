 1 400   0   ' coding: utf-8
 1 400   0   ' Enumerate all styles: 0 .. 12
 1 400   0   
 1 400   0   ' comment=1
 1 400   0   
 1 400   0   ' whitespace=0
 1 408   0 | 	' w
 1 400   0   
 1 400   0   ' number=2
 0 400   0   37
 0 400   0   54.3345612e7
 0 400   0   &B01111100
 0 400   0   &O1163
 0 400   0   &H_E635_9A60
 1 400   0   
 1 400   0   ' keyword=3
 0 400   0   string
 1 400   0   
 1 400   0   ' double-quoted-string=4
 0 400   0   "str"
 0 400   0   "a"c
 1 400   0   
 1 400   0   ' preprocessor=5
 0 400   0   #Const preproc = True
 1 400   0   
 1 400   0   ' operator=6
 0 400   0   - 1
 1 400   0   
 1 400   0   ' identifier=7
 0 400   0   identifier = 7
 1 400   0   ' non-ASCII
 0 400   0   π_is_pi_ω = 3.14
 1 400   0   ' vb may add a type character like $ to identifiers; vbscript doesn't have this
 0 400   0   identifier$ = "s"
 1 400   0   
 1 400   0   ' date=8
 0 400   0   d=#05/11/2003#
 1 400   0   
 1 400   0   ' unclosed-string=9
 0 400   0   " unclosed
 1 400   0   
 1 400   0   ' keyword2=10
 0 400   0   key2
 1 400   0   
 1 400   0   ' keyword3=11
 0 400   0   key3
 1 400   0   
 1 400   0   ' keyword4=12
 0 400   0   key4
 0 400   0   