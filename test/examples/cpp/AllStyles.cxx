// Enumerate all primary styles: 0 to 27 and secondary styles 64 to 91

// default=0
   

// comment=1
/* */

/* commentline=2 */
// example line

// commentdoc=3
/** */

// number=4
123

// word=5
int

// string=6
"string"

// character=7
'c'

// uuid=8
uuid(3fd43029-1354-42f0-a5be-4a484c9c5250)

// preprocessor=9
#define xxx 1

// operator=10
{}

// identifier=11
identifier

// stringeol=12
"

// verbatim=13
@"verbatim"

// regex=14
(/regex/)

// commentlinedoc=15
/// example

// word2=16
second

// commentdockeyword=17
/** @file */

// commentdockeyworderror=18
/** @wrongkey */

// globalclass=19
global

// stringraw=20
R"( )"

// tripleverbatim=21
""" xx """

// hashquotedstring=22
#" xx "

// preprocessorcomment=23
#define /* comment */

// preprocessorcommentdoc=24
#define /** comment */

// userliteral=25
1_date_

// taskmarker=26
/* TODO: sleep */

// escapesequence=27
"\001 \b"

// identifier substyles.11.1=128
vector

// identifier substyles.11.2=129
std

// commentdockeyword substyles.17.1=130
/** @module */

// Secondary styles inside preprocessor excluded section

#if 0

// default=0
   

// comment=1
/* */

/* commentline=2 */
// example line

// commentdoc=3
/** */

// number=4
123

// word=5
int

// string=6
"string"

// character=7
'c'

// uuid=8
uuid(3fd43029-1354-42f0-a5be-4a484c9c5250)

// preprocessor=9
#define xxx 1

// operator=10
{}

// identifier=11
identifier

// stringeol=12
"

// verbatim=13
@"verbatim"

// regex=14
(/regex/)

// commentlinedoc=15
/// example

// word2=16
second

// commentdockeyword=17
/** @file */

// commentdockeyworderror=18
/** @wrongkey */

// globalclass=19
global

// stringraw=20
R"( )"

// tripleverbatim=21
""" xx """

// hashquotedstring=22
#" xx "

// preprocessorcomment=23
#define /* comment */

// preprocessorcommentdoc=24
#define /** comment */

// userliteral=25
1_date_

// taskmarker=26
/* TODO: sleep */

// escapesequence=27
"\001 \b"

// identifier substyles.75.1=192
vector

// identifier substyles.75.2=193
std

// commentdockeyword substyles.81.1=194
/** @module */

#endif
