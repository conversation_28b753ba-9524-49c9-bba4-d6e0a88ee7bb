#include "PyLspManager.h"
#include <QDebug>
#include <QDir>
#include <QUrl>
#include <QStandardPaths>
#include <QMutexLocker>
#include <QCoreApplication>

PyLspManager::PyLspManager(QObject* parent)
    : QObject(parent)
    , m_lspClient(nullptr)
    , m_isInitialized(false)
    , m_isReady(false)
    , m_initializationTimer(new QTimer(this))
    , m_nextVersion(1)
{
    // Setup initialization timeout
    m_initializationTimer->setSingleShot(true);
    m_initializationTimer->setInterval(10000); // 10 seconds timeout
    connect(m_initializationTimer, &QTimer::timeout, this, &PyLspManager::onInitializationTimeout);
}

PyLspManager::~PyLspManager()
{
    shutdown();
}

bool PyLspManager::initialize(const QString& workspaceRoot)
{
    QMutexLocker locker(&m_mutex);
    
    if (m_isInitialized) {
        return true;
    }

    m_workspaceRoot = workspaceRoot;
    
    // Create LSP client
    m_lspClient = std::make_unique<LspClient>(this);
    
    // Connect signals
    connect(m_lspClient.get(), &LspClient::serverStarted, this, &PyLspManager::onLspServerStarted);
    connect(m_lspClient.get(), &LspClient::initialized, this, &PyLspManager::onLspInitialized);
    connect(m_lspClient.get(), &LspClient::completionReceived, this, &PyLspManager::onLspCompletionReceived);
    connect(m_lspClient.get(), &LspClient::serverError, this, &PyLspManager::onLspError);

    // Start the LSP server
    startLspServer();
    
    return true;
}

void PyLspManager::shutdown()
{
    QMutexLocker locker(&m_mutex);
    
    if (m_lspClient) {
        if (m_isInitialized) {
            m_lspClient->shutdown();
            m_lspClient->sendExit();
        }
        m_lspClient->stopServer();
        m_lspClient.reset();
    }
    
    m_isInitialized = false;
    m_isReady = false;
    m_initializationTimer->stop();
}

bool PyLspManager::isReady() const
{
    QMutexLocker locker(&m_mutex);
    return m_isReady;
}

void PyLspManager::openDocument(const QString& filePath, const QString& content)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isReady || !m_lspClient) {
        return;
    }

    QString uri = filePathToUri(filePath);
    int version = m_nextVersion++;
    m_documentVersions[filePath] = version;

    Lsp::TextDocumentItem textDoc;
    textDoc.uri = uri;
    textDoc.languageId = "python";
    textDoc.version = version;
    textDoc.text = content;

    m_lspClient->didOpenTextDocument(textDoc);
}

void PyLspManager::updateDocument(const QString& filePath, const QString& content, int version)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isReady || !m_lspClient) {
        return;
    }

    QString uri = filePathToUri(filePath);
    
    // Update version tracking
    if (version <= 0) {
        version = m_nextVersion++;
    }
    m_documentVersions[filePath] = version;

    Lsp::VersionedTextDocumentIdentifier versionedDoc;
    versionedDoc.uri = uri;
    versionedDoc.version = version;

    // Create a full document change event
    Lsp::TextDocumentContentChangeEvent changeEvent;
    changeEvent.text = content;
    // For full document updates, we don't set range

    QList<Lsp::TextDocumentContentChangeEvent> changes;
    changes.append(changeEvent);

    m_lspClient->didChangeTextDocument(versionedDoc, changes);
}

void PyLspManager::closeDocument(const QString& filePath)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isReady || !m_lspClient) {
        return;
    }

    QString uri = filePathToUri(filePath);
    m_documentVersions.remove(filePath);

    Lsp::TextDocumentIdentifier textDoc;
    textDoc.uri = uri;

    m_lspClient->didCloseTextDocument(textDoc);
}

int PyLspManager::requestCompletion(const QString& filePath, int line, int character)
{
    QMutexLocker locker(&m_mutex);
    
    if (!m_isReady || !m_lspClient) {
        return -1;
    }

    QString uri = filePathToUri(filePath);

    Lsp::TextDocumentIdentifier textDoc;
    textDoc.uri = uri;

    Lsp::Position position;
    position.line = line;
    position.character = character;

    return m_lspClient->requestCompletion(textDoc, position);
}

void PyLspManager::onLspServerStarted()
{
    qDebug() << "PyLsp server started, sending initialize request...";
    
    // Create initialization parameters
    Lsp::InitializeParams initParams;
    initParams.processId = QCoreApplication::applicationPid();
    
    // Set client info
    Lsp::ClientInfo clientInfo;
    clientInfo.name = "TrySail";
    clientInfo.version = "0.1.11";
    initParams.clientInfo = clientInfo;
    
    // Set root URI
    initParams.rootUri = filePathToUri(m_workspaceRoot);
    
    // Set client capabilities for completion
    Lsp::ClientCapabilities capabilities;
    QJsonObject textDocCaps;
    
    // Completion capabilities
    QJsonObject completionCaps;
    completionCaps["dynamicRegistration"] = false;
    QJsonObject completionItem;
    completionItem["snippetSupport"] = false;
    completionItem["commitCharactersSupport"] = true;
    completionItem["documentationFormat"] = QJsonArray{"plaintext"};
    completionCaps["completionItem"] = completionItem;
    textDocCaps["completion"] = completionCaps;
    
    // Synchronization capabilities
    QJsonObject syncCaps;
    syncCaps["dynamicRegistration"] = false;
    syncCaps["willSave"] = false;
    syncCaps["willSaveWaitUntil"] = false;
    syncCaps["didSave"] = false;
    textDocCaps["synchronization"] = syncCaps;
    
    capabilities.textDocument = textDocCaps;
    initParams.capabilities = capabilities;
    
    // Start initialization timeout
    m_initializationTimer->start();
    
    // Send initialize request
    int requestId = m_lspClient->initialize(initParams);
    qDebug() << "Sent initialize request with ID:" << requestId;
}

void PyLspManager::onLspInitialized(const Lsp::InitializeResult& result)
{
    qDebug() << "PyLsp server initialized successfully!";
    
    m_initializationTimer->stop();
    
    // Send initialized notification
    m_lspClient->sendInitialized();
    
    {
        QMutexLocker locker(&m_mutex);
        m_isInitialized = true;
        m_isReady = true;
    }
    
    emit ready();
}

void PyLspManager::onLspCompletionReceived(int requestId, const Lsp::CompletionList& completionList)
{
    emit completionReceived(requestId, completionList.items);
}

void PyLspManager::onLspError(int exitCode, QProcess::ExitStatus exitStatus)
{
    QString errorMsg = QString("PyLsp server error: exit code %1, status %2")
                       .arg(exitCode)
                       .arg(exitStatus == QProcess::NormalExit ? "Normal" : "Crash");
    
    qWarning() << errorMsg;
    
    {
        QMutexLocker locker(&m_mutex);
        m_isInitialized = false;
        m_isReady = false;
    }
    
    emit error(errorMsg);
}

void PyLspManager::onInitializationTimeout()
{
    qWarning() << "PyLsp server initialization timeout";
    
    {
        QMutexLocker locker(&m_mutex);
        m_isInitialized = false;
        m_isReady = false;
    }
    
    emit error("LSP server initialization timeout");
}

void PyLspManager::startLspServer()
{
    qDebug() << "Starting PyLsp server";
    m_lspClient->startServer(qApp->applicationFilePath(), {"-c", "from jedi_language_server.cli import cli; cli()"});
}

QString PyLspManager::filePathToUri(const QString& filePath) const
{
    QUrl url = QUrl::fromLocalFile(QDir(filePath).absolutePath());
    return url.toString();
}

QString PyLspManager::uriToFilePath(const QString& uri) const
{
    QUrl url(uri);
    return url.toLocalFile();
}

QStringList PyLspManager::extractCompletionItems(const Lsp::CompletionList& completionList) const
{
    QStringList items;
    
    for (const Lsp::CompletionItem& item : completionList.items) {
        items.append(item.label);
    }
    
    return items;
}
