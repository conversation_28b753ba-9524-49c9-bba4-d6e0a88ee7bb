#pragma once

#include <QTextObjectInterface>
#include <QTextCursor>
#include <QTextFormat>
#include <QTextDocument>
#include <QPainter>

class QSvgTextObject : public QObject, public QTextObjectInterface
{
    Q_OBJECT
    Q_INTERFACES(QTextObjectInterface)

public:
    enum { SvgTextFormat = QTextFormat::UserObject + 1 };
    enum SvgProperties { SvgData = 1, SvgSize = 2 };

    static void addSvg( QTextCursor& cursor, const int nMaxWidth, const QByteArray& baSvg );

public:
    virtual QSizeF intrinsicSize( QTextDocument *doc, int posInDocument, const QTextFormat &format) override;
    virtual void drawObject( QPainter* painter, const QRectF& rect, QTextDocument* doc, int posInDocument, const QTextFormat& format ) override;
};
