#pragma once

#include <QTextDocument>
#include <QTextEdit>

#include "ScintillaTypes.h"
#include "TrySailGlobal.h"

class QDocumentWidget;


class QOutputWidget : public QTextEdit
{
    Q_OBJECT
public:
    explicit QOutputWidget( QDocumentWidget& mainDocument, QWidget* parent );

protected:
    QDocumentWidget& m_mainDocument;

    void rebuildDocument();
    void syncOutputWithInput();
    bool isFocal() const;

    std::atomic_bool m_bDirty{ false };

    QTextTableFormat      m_formatTableCode;
    QTextTableCellFormat  m_formatTableCellCode;
    QTextCharFormat       m_formatCharError;
    QTextCharFormat       m_formatCharOutput;
    QTextFrameFormat      m_formatFrameCode;
    QTextFrameFormat      m_formatFrameOutput;

    typedef struct typeHandleInfo {
        int m_posStart{ 0 };
        int m_posEnd{ 0 };
    } typeHandleInfo;

    QMap< int, typeHandleInfo > m_mapHandlePositions;
    int m_handleCurrentExecution{ -1 };

    void rebuildChunk( const long lineChunkStart, const long lineChunkEnd, const int executionHandle );
    void changeFont( QTextCursor& cursor, const int posStart, const int posEnd, const char* nameFont );
    enumCodeType chunkType( const long lineChunkStart );

public Q_SLOTS:
    void onEditCopy();
    void onEditSelectAll();
    void onUpdateUi( Scintilla::Update updated );
    void onModified( Scintilla::ModificationFlags type, Scintilla::Position position, Scintilla::Position length, Scintilla::Position linesAdded,
                                    const QByteArray &text, Scintilla::Position line, Scintilla::FoldLevel foldNow, Scintilla::FoldLevel foldPrev);
    void onColorSchemeChanged( Qt::ColorScheme colorScheme );
    void requestRebuild();
    void showContextMenu( const QPoint& pos );
};
