#include "OutputData.h"


void QOutputData::startExecution( const int executionHandle, const QString& sCode, const int executionSequence )
{
    // remove any prior
    auto values = m_outputElements.values( executionHandle );
    for ( auto element : values ) {
        delete element;
    }
    m_outputElements.remove( executionHandle );

    // start new output record
    const auto sCodeTrim = sCode.trimmed();
    m_outputSequence.insert( executionHandle, executionSequence );
    m_outputCode.insert( executionHandle, sCodeTrim );
    emit needsRebuild();
}

void QOutputData::addTextOutput( const int executionHandle, const QString& sText, const enumContentFormat formatContent, const bool bError )
{
    auto pElement = new QOutputTextElement( sText, formatContent, bError );
    m_outputElements.insert( executionHandle, pElement );
    emit needsRebuild();
}

void QOutputData::addImageOutput(  const int executionHandle, const QImage& img )
{
    auto pElement = new QOutputImageElement( img );
    m_outputElements.insert( executionHandle, pElement );
    emit needsRebuild();
}
void QOutputData::addSvgOutput( const int executionHandle, const QByteArray& baSvg )
{
    auto pElement = new QOutputSvgElement( baSvg );
    m_outputElements.insert( executionHandle, pElement );
    emit needsRebuild();
}

QOutputData::~QOutputData()
{
    clear();
}

int QOutputData::sequence( const int executionHandle ) const
{
    return m_outputSequence.value( executionHandle, -1 );
}

QString QOutputData::code( const int executionHandle ) const
{
    return m_outputCode.value( executionHandle, QString{} );
}

QList< QOutputAbstractElement* > QOutputData::elements( const int executionHandle ) const
{
    auto lstReversed = m_outputElements.values( executionHandle );
    std::reverse( lstReversed.begin(), lstReversed.end() );
    return lstReversed;
}

void QOutputData::clear()
{
    m_outputCode.clear();
    m_outputSequence.clear();

    for ( auto pElement : m_outputElements ){
        delete pElement;
    }
    m_outputElements.clear();

    emit needsRebuild();
}
