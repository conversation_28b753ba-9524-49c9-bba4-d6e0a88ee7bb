#include "InternalView.h"

#include "ContentEngine.h"
#include "SqlEngine.h"

#include <QGuiApplication>
#include <QStyleHints>
#include <QCborArray>
#include <QCborMap>
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

void QInternalsModel::rebuild( const QCborMap& cborInternals )
{
    beginResetModel();
    m_cborStructure = cborInternals;
    endResetModel();
}

QInternalsModel::QInternalsModel( const enumCodeType codeType )
    : m_codeType{codeType}
{
    const auto styleHints  = QGuiApplication::styleHints();
    const auto colorScheme = styleHints->colorScheme();

    onColorSchemeChanged( colorScheme );
    connect( styleHints, &QStyleHints::colorSchemeChanged, this, &QInternalsModel::onColorSchemeChanged );
}

void QInternalsModel::onColorSchemeChanged( Qt::ColorScheme colorScheme )
{
    const auto bLight = (colorScheme != Qt::ColorScheme::Dark );

    const auto colorDefaultBackground  = bLight ? solar_base3  : solar_base03;
    const auto colorDefaultFore        = bLight ? solar_base00 : solar_base0;

    m_brushBackground  = QBrush{ colorDefaultBackground };
    m_brushForeground  = QBrush{ colorDefaultFore };

    beginResetModel();
    endResetModel();
}

constexpr auto PARENT_IS_ROOT = static_cast<quintptr>(-1);

QModelIndex QInternalsModel::parent(const QModelIndex &idx) const
{
    if ( !idx.isValid() )
        return {};

    const auto nParentId = idx.internalId();

    if ( nParentId == PARENT_IS_ROOT ) {

        return {};

    } else {

        const auto nParentRow = nParentId;
        return createIndex( nParentRow, 0, PARENT_IS_ROOT );
    }
}

QModelIndex QInternalsModel::index( int row, int column, const QModelIndex& parent ) const
{
    if ( !hasIndex( row, column, parent ) )
        return {};

    if ( parent.isValid() ) {
        return createIndex( row, column, parent.row() );

    } else {
        return createIndex( row, column, PARENT_IS_ROOT );
    }
}

int QInternalsModel::rowCount( const QModelIndex& parent ) const
{
    if ( m_cborStructure.isEmpty() )
        return 0;

    if ( parent.isValid() ) {
        if ( parent.parent().isValid() ) {
            return 0;

        } else {
            switch ( m_codeType ) {
            case enumCodeType::codeTypeSql: {

                const auto sTableName       = parent.data(Qt::DisplayRole).toString();
                const auto cborColumnValue  = m_cborStructure.value( sTableName );
                const auto cborColumnsArray = cborColumnValue.toArray();
                const auto nColumns         = cborColumnsArray.size();
                return nColumns;
            }
            case enumCodeType::codeTypePython:
            default:
                return 0;
            }
        }

    } else {
        return m_cborStructure.size();
    }

    return 0;
}

int QInternalsModel::columnCount( const QModelIndex& parent ) const
{
    if ( m_cborStructure.isEmpty() ) {
        return 0;
    }

    if ( parent.isValid() ) {
        return 9;

    } else {
        switch ( m_codeType ) {
        case enumCodeType::codeTypeSql:
            return 9;
        case enumCodeType::codeTypePython:
        default:
            return 2;
        }
    }
    return 0;
}

QVariant QInternalsModel::data( const QModelIndex& index, int role ) const
{
    if ( role == Qt::BackgroundRole )
        return m_brushBackground;

    if ( role == Qt::ForegroundRole )
        return m_brushForeground;

    if ( role != Qt::DisplayRole )
        return {};

    const auto row    = index.row();
    const auto column = index.column();

    if ( m_codeType == enumCodeType::codeTypePython ) {

        const auto keys = m_cborStructure.keys();
        const auto key  = keys.at( row );
        switch ( column ) {
        case 0:
            return key.toVariant();
        case 1:
            return m_cborStructure.value( key ).toVariant();
        default:
            return QVariant{};
        }

    } else if ( m_codeType == enumCodeType::codeTypeSql ) {

        const auto idxParent = index.parent();

        if ( idxParent.isValid() ) {

            const auto sTableName       = idxParent.data().toString();
            const auto cborColumnValue  = m_cborStructure.value( sTableName );
            const auto cborColumnsArray = cborColumnValue.toArray();
            const auto cborColumn       = cborColumnsArray.at( row ).toMap();

            switch ( column ) {
            case 0:
                return cborColumn.value( QSqlEngine::CBOR_ColumnName ).toVariant();
            case 1:
                return cborColumn.value( QSqlEngine::CBOR_ColumnType ).toVariant();
            case 2:
                return cborColumn.value( QSqlEngine::CBOR_ColumnLength ).toVariant();
            case 3:
                return cborColumn.value( QSqlEngine::CBOR_ColumnDefault ).toVariant();
            case 4:
                return cborColumn.value( QSqlEngine::CBOR_ColumnAuto ).toVariant();
            case 5:
                return cborColumn.value( QSqlEngine::CBOR_ColumnGenerated ).toVariant();
            case 6:
                return cborColumn.value( QSqlEngine::CBOR_ColumnNull ).toVariant();
            case 7:
                return cborColumn.value( QSqlEngine::CBOR_ColumnReadOnly ).toVariant();
            case 8:
                return cborColumn.value( QSqlEngine::CBOR_ColumnRequired ).toVariant();
            }

        } else {

            switch ( column ) {
            case 0: {
                const auto keys   = m_cborStructure.keys();
                const auto key    = keys.at( row );
                const auto sTable = key.toString();
                return sTable;
            }
            default:
                return {};
            }
        }
    }
    return {};
}

QVariant QInternalsModel::headerData( int section, Qt::Orientation orientation, int role ) const
{
    if ( orientation == Qt::Horizontal && role == Qt::DisplayRole ) {

        if ( m_codeType == enumCodeType::codeTypePython ) {

            switch ( section ) {
            case 0:
                return tr("Variable");
            case 1:
                return tr("Value");
            }

        } else if ( m_codeType == enumCodeType::codeTypeSql ) {

            switch ( section ) {
            case 0:
                return tr("Column");
            case 1:
                return tr("Type");
            case 2:
                return tr("Length");
            case 3:
                return tr("Default");
            case 4:
                return tr("Auto");
            case 5:
                return tr("Generated");
            case 6:
                return tr("Null");
            case 7:
                return tr("ReadOnly");
            case 8:
                return tr("Required");
            }
        }
    }
    return QAbstractItemModel::headerData( section, orientation, role );
}

QInternalsView::QInternalsView( const enumCodeType codeType, QWidget* parent )
    : QTreeView{ parent }
    , m_internalsModel{ codeType }
{
    setModel( &m_internalsModel );
    expandAll();

    const auto styleHints  = QGuiApplication::styleHints();
    const auto colorScheme = styleHints->colorScheme();

    onColorSchemeChanged( colorScheme );
    connect( styleHints, &QStyleHints::colorSchemeChanged, this, &QInternalsView::onColorSchemeChanged );
}

void QInternalsView::onColorSchemeChanged( Qt::ColorScheme colorScheme )
{
    const auto bLight = (colorScheme != Qt::ColorScheme::Dark );

    const auto colorDefaultBackground  = bLight ? solar_base3  : solar_base03;
    const auto colorDefaultFore        = bLight ? solar_base00 : solar_base0;

    auto p = palette();
    p.setColor( QPalette::Base, colorDefaultBackground );
    p.setColor( QPalette::Text, colorDefaultFore );
    setPalette(p);
}

void QInternalsView::onProgressMemoryUpdate( const QCborMap &cborMemory, const QStringList /*listFunctions*/ )
{
    m_internalsModel.rebuild( cborMemory );
    expandAll();
}
