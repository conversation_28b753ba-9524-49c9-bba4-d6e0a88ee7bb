#include "MainWindow.h"

#include "ConfigDialog.h"
#include "ContentEngine.h"
#include "PythonThread.h"
#include "DocumentWidget.h"
#include "GithubDialog.h"
#include "AboutDialog.h"
#include "TourWidget.h"

#include <QApplication>
#include <QSettings>
#include <QComboBox>
#include <QFileDialog>
#include <QFileInfo>
#include <QImage>
#include <QImageReader>
#include <QInputDialog>
#include <QLabel>
#include <QMenuBar>
#include <QPoint>
#include <QStatusBar>
#include <QTabWidget>
#include <QTextEdit>
#include <QTimer>
#include <QToolBar>
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

Q_GLOBAL_STATIC( QIcon, iconFileNew,           u":/Resources/document-new-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconFileOpenLocal,     u":/Resources/document-open-5.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconFileSave,          u":/Resources/document-save-3.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconFileSaveAs,        u":/Resources/document-save-as-3.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconFilePrint,         u":/Resources/document-print-5.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditUndo,          u":/Resources/edit-undo-7.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditRedo,          u":/Resources/edit-redo-7.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditCut,           u":/Resources/edit-cut-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditCopy,          u":/Resources/edit-copy-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditPaste,         u":/Resources/edit-paste-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditFind,          u":/Resources/edit-find-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditFindNext,      u":/Resources/edit-find-next.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditFindPrevious,  u":/Resources/edit-find-previous.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconEditFindReplace,   u":/Resources/edit-find-replace-2.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconZoomIn,            u":/Resources/zoom-in-5.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconZoomOut,           u":/Resources/zoom-out-5.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconCodeExecute,       u":/Resources/arrow-right-3.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconCodeStop,          u":/Resources/pictograms-road_signs-yield_roadsign.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconCodeKill,          u":/Resources/process-stop-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconCodeRestart,       u":/Resources/view-refresh-4.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconCodeRestartRunAll, u":/Resources/system-reboot-2.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconDatabase,          u":/Resources/server-database.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconJupyter,           u":/Resources/logoJupyter.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconPdf,               u":/Resources/application-pdf.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconLineType,          u":/Resources/pencil.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconMemory,            u":/Resources/memory.svg"_s );
Q_GLOBAL_STATIC( QIcon, iconPreferences,       u":/Resources/preferences.svg"_s );

void QTrySailMainWindow::openDocument( const QString& sFileName, const QByteArray& baFileContents, const bool bReadOnly )
{
    auto pNewDocument = new QDocumentWidget{ sFileName, baFileContents, bReadOnly, this };
    auto indexTab     = m_tabDocuments.addTab( pNewDocument, pNewDocument->fileName() );
    m_tabDocuments.setCurrentIndex( indexTab );
}

Q_GLOBAL_STATIC( QList< QStringView >, lstDocumentTypes, { SUFFIX_PY, SUFFIX_IPYNB, SUFFIX_MD, SUFFIX_MARKDOWN, SUFFIX_SQL, SUFFIX_TXT, SUFFIX_SH, SUFFIX_BAT, SUFFIX_JSON, SUFFIX_HTML });

void QTrySailMainWindow::onOpenFile( const QString &sFileName )
{
    // don't open again; switch to if already open
    for ( auto nTab = 0; nTab < m_tabDocuments.count(); ++nTab ) {

        auto widget = m_tabDocuments.widget( nTab );
        auto doc    = findParentOfType< QDocumentWidget >( widget );
        if ( doc != nullptr ) {

            if ( doc->m_fileName.compare( sFileName ) == 0 ) {

                m_tabDocuments.setCurrentIndex( nTab );
                return;
            }
        }
    }

    auto fileInfo = QFileInfo{ sFileName };
    auto suffix   = fileInfo.suffix().toLower();

    if ( lstDocumentTypes->contains( suffix ) ) {

        // directly open local if local; otherwise request remote
        if ( fileInfo.exists() ) {

            auto file = QFile{ sFileName };

            if ( file.open( QIODevice::ReadOnly ) ) {

                auto baFileContents = QByteArray{};
                baFileContents      = file.readAll();
                const auto bReadOnly = (!fileInfo.isWritable());
                openDocument( sFileName, baFileContents, bReadOnly );

                addFileToRecentFiles( sFileName );
            }

        } else {
            // try github
            QGithubDialog::downloadFile( sFileName );
        }
    }
}

void QTrySailMainWindow::addFileToRecentFiles( const QString& sFullPath )
{
    // remove if already in recent list
    constexpr auto MAX_RECENT_FILES = 20;
    m_recentFilePaths.removeAll( sFullPath );
    m_recentFilePaths.prepend( sFullPath );
    if ( m_recentFilePaths.length() > MAX_RECENT_FILES )
        m_recentFilePaths.resize( MAX_RECENT_FILES );

    saveRecentFiles();
}

void QTrySailMainWindow::onOpenFiles(const QStringList &lstFileNames)
{
    for ( const auto& fileName : lstFileNames ) {
        onOpenFile( fileName );
    }
}

QTrySailMainWindow::QTrySailMainWindow()
    : QMainWindow{ nullptr, Qt::WindowFlags() }
    , m_tabDocuments{ this }
    , m_fileExplorer{ &m_tabDocuments }
{
    setWindowTitle( QApplication::instance()->applicationName() );
    setUnifiedTitleAndToolBarOnMac( true );

    m_tabDocuments.addTab( &m_fileExplorer, tr("Files") );
    m_tabDocuments.setTabsClosable( true );
    m_tabDocuments.setDocumentMode( true );
    m_tabDocuments.setMovable( true );

    // remove close button from file explorer
    m_tabDocuments.tabBar()->tabButton( 0, QTabBar::RightSide )->deleteLater();
    m_tabDocuments.tabBar()->setTabButton( 0, QTabBar::RightSide, nullptr );

    setCentralWidget( &m_tabDocuments );

    createInterface();

    loadRecentFiles();

    connect( &m_tabDocuments, &QTabWidget::tabCloseRequested, this, &QTrySailMainWindow::onTabCloseRequested );
    connect( &m_fileExplorer, &QFileExplorerWidget::openFile, this, &QTrySailMainWindow::onOpenFile );
    connect( qApp, &QApplication::focusChanged, this, &QTrySailMainWindow::onFocusChanged );

#ifdef QT_DEBUG
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Sql.sql"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Jupyter.ipynb"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Python.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Thread2.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Thread1.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/Demo_Jupytext.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u"/Users/<USER>/Dropbox/Projects/TrySail/Demo_Pillow.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u"/Users/<USER>/Dropbox/Projects/TrySail/Demo_Pandas.py"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u"/Users/<USER>/Dropbox/Projects/TrySail/Demo_MatplotLib.py"_s ); } );
#endif
    QTimer::singleShot( 0, this, &QTrySailMainWindow::updateRecentFilesMenu );
    QTimer::singleShot( 0, this, [ this ]() { onOpenFile( u":/README.md"_s ); } );
    QTimer::singleShot( 0, this, [ this ]() { onTour( false ); } );
}

QTrySailMainWindow::~QTrySailMainWindow()
{
    // close all
    for ( auto index = m_tabDocuments.count() - 1; index >= 0; --index ) {

        auto widget = m_tabDocuments.widget( index );
        if ( widget != &m_fileExplorer ) {
            m_tabDocuments.removeTab( index );
            delete widget;
        }
    }

    // ask threads to quit
    if ( m_threadPython != nullptr ) {
        m_threadPython->quit();
    }
    if ( m_threadSql != nullptr ) {
        m_threadSql->quit();
    }

    // wait and delete
    if ( m_threadPython != nullptr ) {
        m_threadPython->wait();
        delete m_threadPython;
        m_threadPython = nullptr;
    }
    if ( m_threadSql != nullptr ) {
        m_threadSql->wait();
        delete m_threadSql;
        m_threadSql = nullptr;
    }
}

void QTrySailMainWindow::updateRecentFilesMenu()
{
    m_recentFilesMenu->clear();  // will also delete all actions

    // Add new actions based on recentFilePaths
    for ( const auto& filePath : std::as_const(m_recentFilePaths) ) {

        const auto fileInfo = QFileInfo{ filePath };
        const auto sFilePath = QFileInfo{ filePath }.filePath();
        auto action = new QAction{ sFilePath, this };
        action->setData( filePath );
        connect( action, &QAction::triggered, this, [this, sFilePath ]{ onOpenFile( sFilePath ); } );
        m_recentFilesMenu->addAction( action );
    }
}

constexpr auto SETTINGS_RECENT_FILES = "recentFiles";
void QTrySailMainWindow::loadRecentFiles()
{
    auto settings = QSettings{};
    m_recentFilePaths = settings.value( SETTINGS_RECENT_FILES ).toStringList();
}

void QTrySailMainWindow::saveRecentFiles()
{
    auto settings = QSettings{};
    settings.setValue( SETTINGS_RECENT_FILES, m_recentFilePaths );
}

void QTrySailMainWindow::onFocusChanged( QWidget* /*oldWidget*/, QWidget* newWidget )
{
    // document level
    auto newDocumentWidgetParent = findParentOfType< QDocumentWidget >( newWidget );
    auto newFileExplorer = findParentOfType< QFileExplorerWidget >( newWidget );

    const auto bFileExplorerAndSelected = (newFileExplorer != nullptr) && (newFileExplorer->m_treeExplorer->selectionModel()->hasSelection() );
    actionOpenSelectedFile->setEnabled( bFileExplorerAndSelected );
    actionCopyFilePath->setEnabled( bFileExplorerAndSelected );

    if ( newDocumentWidgetParent == nullptr ) {

        // no document level actions possible
        actionFileSave->setEnabled( false );
        actionFileSaveAs->setEnabled( false );
        actionFilePrint->setEnabled( false );
        actionFilePdf->setEnabled( false );
        actionEditUndo->setEnabled( false );
        actionEditRedo->setEnabled( false );
        actionViewToggleMemoryVisible->setEnabled( false );

        actionDatabaseSetup->setEnabled( false );
        actionCodeRunStart->setEnabled( false );
        actionCodeRunStop->setEnabled( false );
        actionCodeRunKill->setEnabled( false );
        actionCodeRunRestart->setEnabled( false );
        actionCodeRunRestartRunAll->setEnabled( false );

        actionEditCut->setEnabled( false );
        actionEditCopy->setEnabled( false );
        actionEditPaste->setEnabled( false );
        actionEditSelectAll->setEnabled( false );
        actionEditFind->setEnabled( false );
        actionEditFindReplace->setEnabled( false );
        actionEditFindNext->setEnabled( false );
        actionEditFindPrevious->setEnabled( false );
        actionEditToggleCellType->setEnabled( false );
        actionEditZoomActual->setEnabled( false );
        actionEditZoomIn->setEnabled( false );
        actionEditZoomOut->setEnabled( false );

        // actionEditSpelling->setEnabled( false );

        // auto newDisplayWidget = findParentOfType< QDisplayWidget >( newWidget );
        // if ( newDisplayWidget != nullptr ) {
        // }

    } else {
        // new document has at least some of these actions
        auto codeEditor   = findParentOfType< QCodeEditor >( newWidget );
        auto outputViewer = findParentOfType< QOutputWidget >( newWidget );

        actionFileSave->setEnabled( true );
        actionFileSaveAs->setEnabled( true );
        actionFilePrint->setEnabled( true );
        actionFilePdf->setEnabled( true );
        actionEditUndo->setEnabled( true );
        actionEditRedo->setEnabled( true );
        actionViewToggleMemoryVisible->setEnabled( true );
        actionViewToggleMemoryVisible->setChecked( newDocumentWidgetParent->m_inputEditor.memoryVisible() );
        actionEditZoomActual->setEnabled( ( codeEditor != nullptr ) );
        actionEditZoomIn->setEnabled( ( codeEditor != nullptr )  );
        actionEditZoomOut->setEnabled( ( codeEditor != nullptr )  );
        actionEditToggleCellType->setEnabled( (codeEditor != nullptr) && (newDocumentWidgetParent->m_codeType == enumCodeType::codeTypePython) );

        newDocumentWidgetParent->syncRunActionsWithEngine();

        actionEditCut->setEnabled( true );
        actionEditCopy->setEnabled( true );
        actionEditPaste->setEnabled( true );
        actionEditSelectAll->setEnabled( true );
        actionEditFind->setEnabled( true );
        actionEditFindReplace->setEnabled( true );
        actionEditFindNext->setEnabled( true );
        actionEditFindPrevious->setEnabled( true );
        // actionEditSpelling->setEnabled( true );
    }
}

void QTrySailMainWindow::onTabCloseRequested(int index)
{
    auto widget = m_tabDocuments.widget( index );

    // never close file explorer, ignore
    if ( widget == &m_fileExplorer )
        return;

    auto widgetDocument = findParentOfType< QDocumentWidget >( widget );
    if ( widgetDocument != nullptr ) {

        if ( ! widgetDocument->canClose() )
            return;

    }

    m_tabDocuments.removeTab( index );
    delete widget;
}

void QTrySailMainWindow::onFileClose()
{
    const auto index = m_tabDocuments.currentIndex();
    onTabCloseRequested( index );
}
void QTrySailMainWindow::onFileCloseAll()
{
    for ( auto index = m_tabDocuments.count() - 1; index >= 0; --index )
        onTabCloseRequested( index );
}

// constexpr auto MRU_FILE_COUNT = 30;

void QTrySailMainWindow::createInterface()
{
    auto sb = statusBar();
    sb->showMessage( tr( "Loading..." ) );

    const auto sVersion = tr( "Version: %1 " ).arg( QApplication::applicationVersion() );
    sb->addPermanentWidget( new QLabel{ sVersion, sb } );

    // menu initialize
    auto menuOverall = menuBar();
    auto menuFile    = menuOverall->addMenu( tr( "File" ) );
    auto menuEdit    = menuOverall->addMenu( tr( "Edit" ) );
    auto menuView    = menuOverall->addMenu( tr( "View" ) );
    auto menuCode    = menuOverall->addMenu( tr( "Code" ) );
    auto menuHelp    = menuOverall->addMenu( tr( "Help" ) );

    // toolbar initialize
    auto toolbarFileNew = addToolBar( tr( "New" ) );
    auto toolbarFile    = addToolBar( tr( "File" ) );
    auto toolbarEdit    = addToolBar( tr( "Edit" ) );
    auto toolbarPrint   = addToolBar( tr( "Print" ) );
    auto toolbarView    = addToolBar( tr( "View" ) );
    auto toolbarCode    = addToolBar( tr( "Code" ) );
    auto toolbarHelp    = addToolBar( tr( "Help" ) );

    for ( auto pToolbar : { toolbarFileNew, toolbarFile, toolbarEdit, toolbarPrint, toolbarView, toolbarCode, toolbarHelp } ) {
        pToolbar->setToolButtonStyle( Qt::ToolButtonTextUnderIcon );
        if ( pToolbar != toolbarCode )
            pToolbar->hide();
    }

    // setup file menu
    auto menuFileNew = menuFile->addMenu( *iconFileNew, tr( "&New" ) );

    actionFileNewJupyter = addAction( tr( "&Jupyter Notebook" ), tr( "Create a new Jupyter Notebook" ), QKeySequence::New, menuFileNew, toolbarFileNew, true, this, &QTrySailMainWindow::onFileNewJupyter, iconJupyter );
    actionFileNewJupyter->setIconText( u"Jupyter"_s );
    actionFileNewPython = addAction( tr( "&Python file" ), tr( "Create a new Python file" ), QKeySequence::UnknownKey, menuFileNew, toolbarFileNew, true, this, &QTrySailMainWindow::onFileNewPython, iconPython() );
    actionFileNewPython->setIconText( u"Python"_s );
    actionFileNewSql = addAction( tr( "&SQL file" ), tr( "Create a new SQL file" ), QKeySequence::UnknownKey, menuFileNew, toolbarFileNew, true, this, &QTrySailMainWindow::onFileNewSql, iconSql() );
    actionFileNewSql->setIconText( u"SQL"_s );
    actionFileNewMarkdown = addAction( tr( "&Markdown file" ), tr( "Create a new Markdown file" ), QKeySequence::UnknownKey, menuFileNew, toolbarFileNew, true, this, &QTrySailMainWindow::onFileNewMarkdown, iconMarkdown() );
    actionFileNewMarkdown->setIconText( u"Markdown"_s );

    menuFile->addSeparator();
    actionFileOpenLocal    = addAction( tr( "&Open..." ), tr( "Open an existing file" ), QKeySequence::Open, menuFile, toolbarFile, true, this, &QTrySailMainWindow::onFileOpenLocal, iconFileOpenLocal );
    actionFileOpenGithub   = addAction( tr( "Open &Github..." ), tr( "Open a file from Github" ), QKeySequence::UnknownKey, menuFile, toolbarFile, true, this, &QTrySailMainWindow::onFileOpenGithub, iconGithub() );
    actionOpenSelectedFile = addAction( tr( "Open Selected File" ), tr( "Open selected file" ), QKeySequence::UnknownKey, menuFile, toolbarFile, true, &m_fileExplorer, &QFileExplorerWidget::onOpenSelectedFile, iconFileOpenLocal );

    auto menuFileOpenDemo      = menuFile->addMenu( *iconFileOpenLocal, tr( "Open Demo" ) );
    actionFileOpenDemoJupyter  = addAction( tr( "&Jupyter Demo" ),tr( "Open a Jupyter notebook demonstration" ), QKeySequence::UnknownKey, menuFileOpenDemo, nullptr, true, this, &QTrySailMainWindow::onFileOpenDemoJuypter );
    actionFileOpenDemoPython   = addAction( tr( "&Python Demo" ), tr( "Open a Python demonstration" ), QKeySequence::UnknownKey, menuFileOpenDemo, nullptr, true, this, &QTrySailMainWindow::onFileOpenDemoPython );
    actionFileOpenDemoJupytext = addAction( tr( "&Jupytext Demo" ), tr( "Open a Jupytext demonstration" ), QKeySequence::UnknownKey, menuFileOpenDemo, nullptr, true, this, &QTrySailMainWindow::onFileOpenDemoJupytext );
    actionFileOpenDemoMarkdown = addAction( tr( "&Markdown Demo" ), tr( "Open a Markdown demonstration" ), QKeySequence::UnknownKey, menuFileOpenDemo, nullptr, true, this, &QTrySailMainWindow::onFileOpenDemoMarkdown );
    actionFileOpenDemoSql      = addAction( tr( "&Sql Demo" ), tr( "Open a SQL demonstration" ), QKeySequence::UnknownKey, menuFileOpenDemo, nullptr, true, this, &QTrySailMainWindow::onFileOpenDemoSql );

    m_recentFilesMenu = menuFile->addMenu(tr("&Recent Files"));
    connect( m_recentFilesMenu, &QMenu::aboutToShow, this, &QTrySailMainWindow::updateRecentFilesMenu );

    menuFile->addSeparator();
    actionFileClose    = addAction( tr( "Cl&ose" ), tr( "Close this file" ), QKeySequence::Close, menuFile, nullptr, true, this, &QTrySailMainWindow::onFileClose );
    actionFileCloseAll = addAction( tr( "Close &All" ), tr( "Close all open files" ), QKeySequence::UnknownKey, menuFile, nullptr, true, this, &QTrySailMainWindow::onFileCloseAll );
    actionFileSave     = addAction( tr( "&Save" ), tr( "Save this file to disk" ), QKeySequence::Save, menuFile, toolbarFile, false, iconFileSave );
    actionFileSaveAs   = addAction( tr( "Save &As..." ), tr( "Save this file under a new name" ), QKeySequence::SaveAs, menuFile, nullptr, false, iconFileSaveAs );
    menuFile->addSeparator();
    actionFilePrint = addAction( tr( "&Print..." ), tr( "Print this document" ), QKeySequence::Print, menuFile, toolbarPrint, false, iconFilePrint );
    actionFilePdf   = addAction( tr( "Export to Pdf..." ), tr( "Export this document to PDF" ), QKeySequence::UnknownKey, menuFile, toolbarPrint, false, iconPdf );
    actionFilePdf->setIconText( u"PDF"_s );
    menuFile->addSeparator();
    actionFileExit = addAction( tr( "Exit" ), tr( "Exit TrySail\u00AE" ), QKeySequence::Quit, menuFile, nullptr, true, this, &QTrySailMainWindow::onFileExit );

    // setup edit menu
    actionEditUndo = addAction( tr( "Undo" ), tr( "Undo the last action" ), QKeySequence::Undo, menuEdit, nullptr, false, iconEditUndo );
    actionEditRedo = addAction( tr( "Redo" ), tr( "Redo the last action" ), QKeySequence::Redo, menuEdit, nullptr, false, iconEditRedo );
    menuEdit->addSeparator();
    actionEditCut   = addAction( tr( "Cut" ), tr( "Cut the selection and put it on the clipboard" ), QKeySequence::Cut, menuEdit, nullptr, false, iconEditCut );
    actionEditCopy  = addAction( tr( "Copy" ), tr( "Copy the selection and put it on the clipboard" ), QKeySequence::Copy, menuEdit, nullptr, false, iconEditCopy );
    actionEditPaste = addAction( tr( "Paste" ), tr( "Paste the clipboard contents" ), QKeySequence::Paste, menuEdit, nullptr, false, iconEditPaste );
    menuEdit->addSeparator();
    actionEditSelectAll = addAction( tr( "Select All" ), tr( "Select all document contents" ), QKeySequence::SelectAll, menuEdit, nullptr, false, nullptr );
    menuEdit->addSeparator();
    actionEditFind        = addAction( tr( "Find" ), tr( "Find specified text" ), QKeySequence::Find, menuEdit, toolbarEdit, false, iconEditFind );
    actionEditFindPrevious = addAction( tr( "Find Previous" ), tr( "Find the previous occurrence of specified text" ), QKeySequence::FindPrevious, menuEdit, toolbarEdit, false, iconEditFindPrevious );
    actionEditFindNext    = addAction( tr( "Find Next" ), tr( "Find the next occurrence of specified text" ), QKeySequence::FindNext, menuEdit, toolbarEdit, false, iconEditFindNext );
    actionEditFindReplace = addAction( tr( "Find and Replace" ), tr( "Find and replace specified text" ), QKeySequence::Replace, menuEdit, toolbarEdit, false, iconEditFindReplace );
    menuEdit->addSeparator();
    actionEditToggleCellType = addAction( tr( "Line Type" ), tr( "Change current line between code and markdown" ), ( Qt::CTRL | Qt::Key_M ), menuEdit, toolbarEdit, false, iconLineType );
    menuEdit->addSeparator();
    actionCopyFilePath = addAction( tr( "Copy Selected File Path" ), tr( "Copy the full path name of the selected file" ), QKeySequence::UnknownKey, menuEdit, toolbarEdit, true, &m_fileExplorer, &QFileExplorerWidget::onCopyFilePath, nullptr );

    // setup view menu
    actionViewToggleMemoryVisible = addAction( tr( "Memory changes" ), tr( "Memory changes" ), QKeySequence::UnknownKey, menuView, toolbarView, false, iconMemory );
    actionViewToggleMemoryVisible->setIconText( u"Memory"_s );
    actionViewToggleMemoryVisible->setCheckable( true );
    menuView->addSeparator();
    actionEditZoomActual  = addAction( tr( "Actual Size" ), tr( "Actual Size" ), Qt::ControlModifier | Qt::Key_0, menuView, toolbarView, false, nullptr );
    actionEditZoomIn  = addAction( tr( "Zoom In" ), tr( "Zoom in" ), QKeySequence::ZoomIn, menuView, toolbarView, false, iconZoomIn );
    actionEditZoomOut = addAction( tr( "Zoom Out" ), tr( "Zoom out" ), QKeySequence::ZoomOut, menuView, toolbarView, false, iconZoomOut );

    // setup code menu
    actionDatabaseSetup = addAction( tr( "Open Database" ), tr( "Open database" ), QKeySequence::UnknownKey, menuCode, toolbarCode, false, iconDatabase );
    actionDatabaseSetup->setIconText( u"Database"_s );
    menuCode->addSeparator();
    actionCodeRunStart = addAction( tr( "Run Code" ), tr( "Execute the current code" ), ( Qt::CTRL | Qt::Key_Return ), menuCode, toolbarCode, false, iconCodeExecute );
    actionCodeRunStop = addAction( tr( "Stop" ), tr( "Stop the currently executing code" ), QKeySequence::UnknownKey, menuCode, toolbarCode, false, iconCodeStop );
    actionCodeRunKill  = addAction( tr( "Kill" ), tr( "Kill the currently executing code" ), QKeySequence::UnknownKey, menuCode, toolbarCode, false, iconCodeKill );
    actionCodeRunRestart = addAction( tr( "Restart" ), tr( "Restart" ), QKeySequence::UnknownKey, menuCode, toolbarCode, false, iconCodeRestart );
    actionCodeRunRestartRunAll = addAction( tr( "Restart and Run All" ), tr( "Restart and run all the cells" ), QKeySequence::UnknownKey, menuCode, toolbarCode, false, iconCodeRestartRunAll );
    actionCodeRunRestartRunAll->setIconText( u"Restart/Run All"_s );

    menuOverall->addSeparator();

    // setup help menu
    menuHelp->addSeparator();
    actionPreferences = addAction( tr( "Preferences..." ), tr( "Change preferences" ), QKeySequence::Preferences, menuHelp, toolbarHelp, true, this, &QTrySailMainWindow::onFilePreferences, iconPreferences );
#ifndef TARGET_OS_MAC
    menuHelp->addSeparator();
#endif
    addAction( tr( "About TrySail\u00AE" ), tr( "Display program information, version number, and copyright" ), QKeySequence::UnknownKey, menuHelp, nullptr, true, this, &QTrySailMainWindow::onHelpAbout, iconTrySail() );
    actionHelpTour = addAction( tr( "Take a &Tour" ), tr( "Take a tour of TrySail" ), QKeySequence::UnknownKey, menuHelp, nullptr, true, this, &QTrySailMainWindow::onHelpTour, iconTrySail() );
    actionHelpTour->setIconText( u"Tour"_s );

    sb->clearMessage();
}

QAction *QTrySailMainWindow::addAction( const QString& sLabel, const QString& sStatusTip, const QKeySequence& shortcut, QMenu* mnu, QToolBar* toolbar, const bool bEnabled, const QIcon* pIcon )
{
    auto action = new QAction{ sLabel, this };
    action->setText( sLabel );

    if ( pIcon != nullptr ) {
        action->setIcon( *pIcon );
    }
    if ( shortcut != QKeySequence::UnknownKey ) {
        action->setShortcut( shortcut );
        action->setShortcutVisibleInContextMenu( true );
        action->setToolTip( u"%1 (%2)"_s.arg( sLabel, action->shortcut().toString( QKeySequence::SequenceFormat::NativeText ) ) );
    }
    action->setStatusTip( sStatusTip );

    if ( mnu != nullptr )
        mnu->addAction( action );

    if ( toolbar != nullptr )
        toolbar->addAction( action );

    action->setEnabled( bEnabled );
    return action;
}

QPythonThread *QTrySailMainWindow::getThreadPython()
{
    if ( m_threadPython == nullptr ) {

        m_threadPython = new QPythonThread{};
        m_threadPython->start( QThread::Priority::LowPriority );
        connect( m_threadPython, &QPythonThread::requestUserInput, this, &QTrySailMainWindow::onRequestUserInput, Qt::BlockingQueuedConnection );
    }
    return m_threadPython;
}

QThread *QTrySailMainWindow::getThreadSql()
{
    if ( m_threadSql == nullptr ) {
        m_threadSql = new QThread{};
        m_threadSql->start( QThread::Priority::LowPriority );
    }
    return m_threadSql;
}

void QTrySailMainWindow::onFilePreferences()
{
    auto dlg = new QConfigDialog{ this };
    dlg->show();
}

void QTrySailMainWindow::onFileExit()
{
    emit QApplication::closeAllWindows();
}


QDocumentWidget* QTrySailMainWindow::onFileNew( const QStringView& sSuffix )
{
    static int nFileNumber = 0;
    nFileNumber++;

    auto pNewDocument = new QDocumentWidget{ tr("Untitled-%1.%2").arg( nFileNumber ).arg( sSuffix ), QByteArray{}, true, this };
    auto index        = m_tabDocuments.addTab( pNewDocument, pNewDocument->fileName() );
    m_tabDocuments.setCurrentIndex( index );
    return pNewDocument;
}

QDocumentWidget* QTrySailMainWindow::onFileNewJupyter()
{
    return onFileNew( SUFFIX_IPYNB );
}
QDocumentWidget* QTrySailMainWindow::onFileNewPython()
{
    return onFileNew( SUFFIX_PY );
}
QDocumentWidget* QTrySailMainWindow::onFileNewSql()
{
    return onFileNew( SUFFIX_SQL );
}
QDocumentWidget* QTrySailMainWindow::onFileNewMarkdown()
{
    return onFileNew( SUFFIX_MD );
}

void QTrySailMainWindow::onFileOpenGithub()
{
    const auto dlgFileOpen = new QGithubDialog{ this };
    dlgFileOpen->setAttribute( Qt::WA_DeleteOnClose );
    connect( dlgFileOpen, &QGithubDialog::filesSelected, this, &QTrySailMainWindow::onOpenFiles );
    dlgFileOpen->open();
}

void QTrySailMainWindow::onFileOpenDemoJuypter()
{
    onOpenFile( u":/Demo_Jupyter.ipynb"_s );
}
void QTrySailMainWindow::onFileOpenDemoPython()
{
    onOpenFile( u":/Demo_Python.py"_s );
}
void QTrySailMainWindow::onFileOpenDemoJupytext()
{
    onOpenFile( u":/Demo_Jupytext.py"_s );
}
void QTrySailMainWindow::onFileOpenDemoMarkdown()
{
    onOpenFile( u":/README.md"_s );
}
void QTrySailMainWindow::onFileOpenDemoSql()
{
    onOpenFile( u":/Demo_Sql.sql"_s );
}

void QTrySailMainWindow::onFileOpenLocal()
{
    const auto sFileTypes = tr( "All TrySail files (*.%1 *.%2 *.%3 *.%4 *.%5 *.%6 *.%7 *.%8 *.%9 *.%10);;"
                                "Jupyter Notebooks (*.%1);;"
                                "Jupytext Files (*.%2);;"
                                "Python Files (*.%2);;"
                                "SQL Files (*.%3);;"
                                "Markdown Files (*.%4 *.%5);;"
                                "Text Files (*.%6);;"
                                "Shell Files (*.%7);;"
                                "Batch Files (*.%8);;"
                                "JSON Files (*.%9);;"
                                "HTML Files (*.%10);;"
                                "All files (*.*)" )
                                .arg( SUFFIX_IPYNB, SUFFIX_PY, SUFFIX_SQL, SUFFIX_MD, SUFFIX_MARKDOWN, SUFFIX_TXT, SUFFIX_SH, SUFFIX_BAT, SUFFIX_JSON, SUFFIX_HTML );
    const auto dlgFileOpen = new QFileDialog{ this, tr( "Open" ), QString{}, sFileTypes };
    dlgFileOpen->setAcceptMode( QFileDialog::AcceptMode::AcceptOpen );
    dlgFileOpen->setFileMode( QFileDialog::ExistingFiles );
    dlgFileOpen->setAttribute( Qt::WA_DeleteOnClose );
    connect( dlgFileOpen, &QFileDialog::filesSelected, this, &QTrySailMainWindow::onOpenFiles );
    dlgFileOpen->open();
}

void QTrySailMainWindow::onFileOpenRecent()
{
    const auto action = qobject_cast< QAction* >( sender() );
    if ( action == nullptr )
        return;

    const auto fileName = action->data().toString();
    onOpenFile( fileName );
}

void QTrySailMainWindow::onHelpAbout()
{
    auto dlgAbout = new QAboutDialog{ this };
    dlgAbout->setAttribute( Qt::WA_DeleteOnClose );
    dlgAbout->open();
}

void QTrySailMainWindow::onHelpTour()
{
    onTour( true );
}

void QTrySailMainWindow::onTour( const bool bAlways )
{
    constexpr auto settingTourDone = "TourDone";

    auto settings = QSettings{};
    settings.beginGroup( QApplication::applicationVersion() );

    const auto bRunTour = bAlways || ( ! settings.value( settingTourDone, false ).toBool() );

    if ( ! bRunTour )
        return;

    takeTour( this );

    settings.setValue( settingTourDone, true );
}

QTrySailMainWindow *getTrySailMainWindowHelper()
{
    auto lstTopLevelWidgets = QApplication::topLevelWidgets();

    for ( auto pWidget : std::as_const( lstTopLevelWidgets ) ) {

        auto pMainWindow = qobject_cast< QTrySailMainWindow* >( pWidget );
        if ( pMainWindow != nullptr )
            return pMainWindow;
    }
    return nullptr;
}

QTrySailMainWindow *QTrySailMainWindow::getTrysailMainWindow()
{
    static auto pMainWindow = getTrySailMainWindowHelper();
    return pMainWindow;
}

QString QTrySailMainWindow::onRequestUserInput(const QString &sPrompt)
{
    const auto sResponse = QInputDialog::getText( this, tr("Input"), sPrompt );
    return sResponse;
}
