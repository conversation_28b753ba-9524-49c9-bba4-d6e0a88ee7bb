#ifndef LSP_TYPES_SIGNATUREHELP_H
#define LSP_TYPES_SIGNATUREHELP_H

#include "MarkupContent.h"
#include <QString>
#include <QJsonObject>
#include <QJsonArray>
#include <QList>
#include <QVariant>

namespace Lsp {

/**
 * Represents a parameter of a callable-signature. A parameter can
 * have a label and a doc-comment.
 */
class ParameterInformation {
public:
    ParameterInformation() = default;
    ParameterInformation(const QString& label);

    // Getters
    const QVariant& label() const { return m_label; } // Can be string or [start, end] array
    const QVariant& documentation() const { return m_documentation; } // Can be string or MarkupContent
    bool hasDocumentation() const { return m_hasDocumentation; }

    // Setters
    void setLabel(const QString& label) { m_label = label; }
    void setLabel(int start, int end) { 
        QList<int> range = {start, end};
        m_label = QVariant::fromValue(range);
    }
    void setDocumentation(const QString& doc) { 
        m_documentation = doc; 
        m_hasDocumentation = true;
    }
    void setDocumentation(const MarkupContent& doc) { 
        m_documentation = QVariant::fromValue(doc); 
        m_hasDocumentation = true;
    }

    // JSON serialization
    QJsonObject toJson() const;
    static ParameterInformation fromJson(const QJsonObject& json);

private:
    QVariant m_label; // string | [number, number]
    QVariant m_documentation; // string | MarkupContent
    bool m_hasDocumentation = false;
};

/**
 * Represents the signature of something callable. A signature
 * can have a label, like a function-name, a doc-comment, and
 * a set of parameters.
 */
class SignatureInformation {
public:
    SignatureInformation() = default;
    SignatureInformation(const QString& label);

    // Getters
    const QString& label() const { return m_label; }
    const QVariant& documentation() const { return m_documentation; }
    const QList<ParameterInformation>& parameters() const { return m_parameters; }
    int activeParameter() const { return m_activeParameter; }
    bool hasDocumentation() const { return m_hasDocumentation; }
    bool hasActiveParameter() const { return m_hasActiveParameter; }

    // Setters
    void setLabel(const QString& label) { m_label = label; }
    void setDocumentation(const QString& doc) { 
        m_documentation = doc; 
        m_hasDocumentation = true;
    }
    void setDocumentation(const MarkupContent& doc) { 
        m_documentation = QVariant::fromValue(doc); 
        m_hasDocumentation = true;
    }
    void setParameters(const QList<ParameterInformation>& parameters) { m_parameters = parameters; }
    void addParameter(const ParameterInformation& parameter) { m_parameters.append(parameter); }
    void setActiveParameter(int activeParameter) { 
        m_activeParameter = activeParameter; 
        m_hasActiveParameter = true;
    }

    // JSON serialization
    QJsonObject toJson() const;
    static SignatureInformation fromJson(const QJsonObject& json);

private:
    QString m_label;
    QVariant m_documentation; // string | MarkupContent
    QList<ParameterInformation> m_parameters;
    int m_activeParameter = -1;
    bool m_hasDocumentation = false;
    bool m_hasActiveParameter = false;
};

/**
 * Signature help represents the signature of something
 * callable. There can be multiple signature but only one
 * active and only one active parameter.
 */
class SignatureHelp {
public:
    SignatureHelp() = default;

    // Getters
    const QList<SignatureInformation>& signatures() const { return m_signatures; }
    int activeSignature() const { return m_activeSignature; }
    int activeParameter() const { return m_activeParameter; }
    bool hasActiveSignature() const { return m_hasActiveSignature; }
    bool hasActiveParameter() const { return m_hasActiveParameter; }

    // Setters
    void setSignatures(const QList<SignatureInformation>& signatures) { m_signatures = signatures; }
    void addSignature(const SignatureInformation& signature) { m_signatures.append(signature); }
    void setActiveSignature(int activeSignature) { 
        m_activeSignature = activeSignature; 
        m_hasActiveSignature = true;
    }
    void setActiveParameter(int activeParameter) { 
        m_activeParameter = activeParameter; 
        m_hasActiveParameter = true;
    }

    // JSON serialization
    QJsonObject toJson() const;
    static SignatureHelp fromJson(const QJsonObject& json);

private:
    QList<SignatureInformation> m_signatures;
    int m_activeSignature = -1;
    int m_activeParameter = -1;
    bool m_hasActiveSignature = false;
    bool m_hasActiveParameter = false;
};

} // namespace Lsp

Q_DECLARE_METATYPE(QList<int>)

#endif // LSP_TYPES_SIGNATUREHELP_H
