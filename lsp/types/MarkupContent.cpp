#include "MarkupContent.h"

namespace Lsp {

MarkupContent::MarkupContent(MarkupKind kind, const QString& value)
    : m_kind(kind), m_value(value) {
}

QJsonObject MarkupContent::toJson() const {
    QJsonObject obj;
    obj["kind"] = markupKindToString(m_kind);
    obj["value"] = m_value;
    return obj;
}

MarkupContent MarkupContent::fromJson(const QJsonObject& json) {
    MarkupContent content;
    content.m_kind = markupKindFromString(json["kind"].toString());
    content.m_value = json["value"].toString();
    return content;
}

QString markupKindToString(MarkupKind kind) {
    switch (kind) {
        case MarkupKind::PlainText:
            return "plaintext";
        case MarkupKind::Markdown:
            return "markdown";
    }
    return "plaintext";
}

MarkupKind markupKindFromString(const QString& str) {
    if (str == "markdown") {
        return MarkupKind::Markdown;
    }
    return MarkupKind::PlainText;
}

} // namespace Lsp
