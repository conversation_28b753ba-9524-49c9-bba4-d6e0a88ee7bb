#ifndef LSP_TYPES_RENAMEPARAMS_H
#define LSP_TYPES_RENAMEPARAMS_H

#include "TextDocumentPositionParams.h"
#include <QString>
#include <QJsonObject>

namespace Lsp {

/**
 * Parameters for the rename request.
 */
class RenameParams : public TextDocumentPositionParams {
public:
    RenameParams() = default;
    RenameParams(const QString& newName);

    // Getters
    const QString& newName() const { return m_newName; }

    // Setters
    void setNewName(const QString& newName) { m_newName = newName; }

    // JSON serialization
    QJsonObject toJson() const;
    static RenameParams fromJson(const QJsonObject& json);

private:
    QString m_newName;
};

} // namespace Lsp

#endif // LSP_TYPES_RENAMEPARAMS_H
