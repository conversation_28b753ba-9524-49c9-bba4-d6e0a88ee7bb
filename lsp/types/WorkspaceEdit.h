#ifndef LSP_TYPES_WORKSPACEEDIT_H
#define LSP_TYPES_WORKSPACEEDIT_H

#include "LspTypesCommon.h"

namespace Lsp {

/**
 * A workspace edit represents changes to many resources managed in the workspace.
 */
struct WorkspaceEdit {
    std::optional<QJsonObject> changes; // Map from DocumentUri to TextEdit[]
    std::optional<QJsonArray> documentChanges;

    QJsonObject toJson() const;
    static WorkspaceEdit fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_WORKSPACEEDIT_H
