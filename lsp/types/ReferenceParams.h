#ifndef LSP_TYPES_REFERENCEPARAMS_H
#define LSP_TYPES_REFERENCEPARAMS_H

#include "TextDocumentPositionParams.h"
#include <QJsonObject>

namespace Lsp {

/**
 * Reference context for the find references request.
 */
class ReferenceContext {
public:
    ReferenceContext() = default;
    ReferenceContext(bool includeDeclaration);

    // Getters
    bool includeDeclaration() const { return m_includeDeclaration; }

    // Setters
    void setIncludeDeclaration(bool includeDeclaration) { m_includeDeclaration = includeDeclaration; }

    // JSON serialization
    QJsonObject toJson() const;
    static ReferenceContext fromJson(const QJsonObject& json);

private:
    bool m_includeDeclaration = false;
};

/**
 * Parameters for the find references request.
 */
class ReferenceParams : public TextDocumentPositionParams {
public:
    ReferenceParams() = default;

    // Getters
    const ReferenceContext& context() const { return m_context; }

    // Setters
    void setContext(const ReferenceContext& context) { m_context = context; }

    // JSON serialization
    QJsonObject toJson() const;
    static ReferenceParams fromJson(const QJsonObject& json);

private:
    ReferenceContext m_context;
};

} // namespace Lsp

#endif // LSP_TYPES_REFERENCEPARAMS_H
