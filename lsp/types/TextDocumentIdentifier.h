#ifndef LSP_TYPES_TEXTDOCUMENTIDENTIFIER_H
#define LSP_TYPES_TEXTDOCUMENTIDENTIFIER_H

#include "LspTypesCommon.h"

namespace Lsp {

/**
 * A literal to identify a text document in the client.
 */
struct TextDocumentIdentifier {
    DocumentUri uri;

    TextDocumentIdentifier() = default;
    TextDocumentIdentifier(const DocumentUri& u) : uri(u) {}

    QJsonObject toJson() const;
    static TextDocumentIdentifier fromJson(const QJsonObject& json);
    bool operator==(const TextDocumentIdentifier& other) const;
};

} // namespace Lsp

#endif // LSP_TYPES_TEXTDOCUMENTIDENTIFIER_H
