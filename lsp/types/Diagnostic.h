#ifndef LSP_TYPES_DIAGNOSTIC_H
#define LSP_TYPES_DIAGNOSTIC_H

#include "Range.h"

namespace Lsp {

/**
 * Represents a diagnostic, such as a compiler error or warning.
 */
struct Diagnostic {
    enum Severity {
        Error = 1,
        Warning = 2,
        Information = 3,
        Hint = 4
    };

    Range range;
    std::optional<Severity> severity;
    std::optional<QString> code;
    std::optional<QString> source;
    QString message;
    std::optional<QJsonArray> relatedInformation;
    std::optional<QJsonArray> tags;

    Diagnostic() = default;
    Diagnostic(const Range& r, const QString& msg) : range(r), message(msg) {}

    QJsonObject toJson() const;
    static Diagnostic fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_DIAGNOSTIC_H
