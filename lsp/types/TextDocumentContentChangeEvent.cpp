#include "TextDocumentContentChangeEvent.h"

namespace Lsp {

QJsonObject TextDocumentContentChangeEvent::toJson() const {
    QJsonObject obj;
    if (range.has_value()) {
        obj["range"] = range->toJson();
    }
    if (rangeLength.has_value()) {
        obj["rangeLength"] = static_cast<int>(rangeLength.value());
    }
    obj["text"] = text;
    return obj;
}

TextDocumentContentChangeEvent TextDocumentContentChangeEvent::fromJson(const QJsonObject& json) {
    TextDocumentContentChangeEvent event;
    if (json.contains("range")) {
        event.range = Range::fromJson(json["range"].toObject());
    }
    if (json.contains("rangeLength")) {
        event.rangeLength = static_cast<uinteger>(json["rangeLength"].toInt());
    }
    event.text = json["text"].toString();
    return event;
}

} // namespace Lsp
