#ifndef LSP_TYPES_MARKUPCONTENT_H
#define LSP_TYPES_MARKUPCONTENT_H

#include <QString>
#include <QJsonObject>

namespace Lsp {

/**
 * Describes the content type that a client supports in various
 * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.
 */
enum class MarkupKind {
    PlainText,
    Markdown
};

/**
 * A `MarkupContent` literal represents a string value which content is interpreted base on its
 * kind flag. Currently the protocol supports `plaintext` and `markdown` as markup kinds.
 */
class MarkupContent {
public:
    MarkupContent() = default;
    MarkupContent(MarkupKind kind, const QString& value);

    // Getters
    MarkupKind kind() const { return m_kind; }
    const QString& value() const { return m_value; }

    // Setters
    void setKind(MarkupKind kind) { m_kind = kind; }
    void setValue(const QString& value) { m_value = value; }

    // JSON serialization
    QJsonObject toJson() const;
    static MarkupContent fromJson(const QJsonObject& json);

    // Utility methods
    bool isEmpty() const { return m_value.isEmpty(); }

private:
    MarkupKind m_kind = MarkupKind::PlainText;
    QString m_value;
};

/**
 * Convert MarkupKind to string
 */
QString markupKindToString(MarkupKind kind);

/**
 * Convert string to MarkupKind
 */
MarkupKind markupKindFromString(const QString& str);

} // namespace Lsp

#endif // LSP_TYPES_MARKUPCONTENT_H
