#ifndef LSP_TYPES_DOCUMENTHIGHLIGHT_H
#define LSP_TYPES_DOCUMENTHIGHLIGHT_H

#include "Range.h"
#include <QJsonObject>

namespace Lsp {

/**
 * A document highlight kind.
 */
enum class DocumentHighlightKind {
    Text = 1,
    Read = 2,
    Write = 3
};

/**
 * A document highlight is a range inside a text document which deserves
 * special attention. Usually a document highlight is visualized by changing
 * the background color of its range.
 */
class DocumentHighlight {
public:
    DocumentHighlight() = default;
    DocumentHighlight(const Range& range, DocumentHighlightKind kind = DocumentHighlightKind::Text);

    // Getters
    const Range& range() const { return m_range; }
    DocumentHighlightKind kind() const { return m_kind; }

    // Setters
    void setRange(const Range& range) { m_range = range; }
    void setKind(DocumentHighlightKind kind) { m_kind = kind; }

    // JSON serialization
    QJsonObject toJson() const;
    static DocumentHighlight fromJson(const QJsonObject& json);

private:
    Range m_range;
    DocumentHighlightKind m_kind = DocumentHighlightKind::Text;
};

/**
 * Convert DocumentHighlightKind to JSON value
 */
int documentHighlightKindToJson(DocumentHighlightKind kind);

/**
 * Convert JSON value to DocumentHighlightKind
 */
DocumentHighlightKind documentHighlightKindFromJson(int value);

} // namespace Lsp

#endif // LSP_TYPES_DOCUMENTHIGHLIGHT_H
