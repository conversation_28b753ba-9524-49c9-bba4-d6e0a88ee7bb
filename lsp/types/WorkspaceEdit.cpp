#include "WorkspaceEdit.h"

namespace Lsp {

QJsonObject WorkspaceEdit::toJson() const {
    QJsonObject obj;
    if (changes.has_value()) {
        obj["changes"] = changes.value();
    }
    if (documentChanges.has_value()) {
        obj["documentChanges"] = documentChanges.value();
    }
    return obj;
}

WorkspaceEdit WorkspaceEdit::fromJson(const QJsonObject& json) {
    WorkspaceEdit edit;
    if (json.contains("changes")) {
        edit.changes = json["changes"].toObject();
    }
    if (json.contains("documentChanges")) {
        edit.documentChanges = json["documentChanges"].toArray();
    }
    return edit;
}

} // namespace Lsp
