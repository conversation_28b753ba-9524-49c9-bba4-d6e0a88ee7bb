#include "Command.h"

namespace Lsp {

QJsonObject Command::toJson() const {
    QJsonObject obj;
    obj["title"] = title;
    obj["command"] = command;
    if (arguments.has_value()) {
        obj["arguments"] = arguments.value();
    }
    return obj;
}

Command Command::fromJson(const QJsonObject& json) {
    Command cmd;
    cmd.title = json["title"].toString();
    cmd.command = json["command"].toString();
    if (json.contains("arguments")) {
        cmd.arguments = json["arguments"].toArray();
    }
    return cmd;
}

} // namespace Lsp
