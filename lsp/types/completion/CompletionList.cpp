#include "CompletionList.h"

namespace Lsp {

QJsonObject CompletionList::toJson() const {
    QJsonObject obj;
    obj["isIncomplete"] = isIncomplete;
    QJsonArray itemsArray;
    for (const CompletionItem& item : items) {
        itemsArray.append(item.toJson());
    }
    obj["items"] = itemsArray;
    return obj;
}

CompletionList CompletionList::fromJson(const QJsonObject& json) {
    CompletionList list;
    list.isIncomplete = json["isIncomplete"].toBool();
    QJsonArray itemsArray = json["items"].toArray();
    for (const QJsonValue& val : itemsArray) {
        list.items.append(CompletionItem::fromJson(val.toObject()));
    }
    return list;
}

} // namespace Lsp
