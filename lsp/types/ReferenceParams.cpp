#include "ReferenceParams.h"

namespace Lsp {

// ReferenceContext implementation
ReferenceContext::ReferenceContext(bool includeDeclaration)
    : m_includeDeclaration(includeDeclaration) {
}

QJsonObject ReferenceContext::toJson() const {
    QJsonObject obj;
    obj["includeDeclaration"] = m_includeDeclaration;
    return obj;
}

ReferenceContext ReferenceContext::fromJson(const QJsonObject& json) {
    ReferenceContext context;
    context.m_includeDeclaration = json["includeDeclaration"].toBool(false);
    return context;
}

// ReferenceParams implementation
QJsonObject ReferenceParams::toJson() const {
    QJsonObject obj = TextDocumentPositionParams::toJson();
    obj["context"] = m_context.toJson();
    return obj;
}

ReferenceParams ReferenceParams::fromJson(const QJsonObject& json) {
    ReferenceParams params;
    static_cast<TextDocumentPositionParams&>(params) = TextDocumentPositionParams::fromJson(json);
    params.m_context = ReferenceContext::fromJson(json["context"].toObject());
    return params;
}

} // namespace Lsp
