#ifndef LSP_TYPES_TEXTDOCUMENTCONTENTCHANGEEVENT_H
#define LSP_TYPES_TEXTDOCUMENTCONTENTCHANGEEVENT_H

#include "Range.h"
#include <optional>

namespace Lsp {

/**
 * An event describing a change to a text document.
 */
struct TextDocumentContentChangeEvent {
    std::optional<Range> range;
    std::optional<uinteger> rangeLength;
    QString text;

    QJsonObject toJson() const;
    static TextDocumentContentChangeEvent fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_TEXTDOCUMENTCONTENTCHANGEEVENT_H
