#ifndef LSP_TYPES_CODEACTIONPARAMS_H
#define LSP_TYPES_CODEACTIONPARAMS_H

#include "TextDocumentIdentifier.h"
#include "Range.h"
#include "CodeAction.h"
#include <QJsonObject>

namespace Lsp {

/**
 * Parameters for the code action request.
 */
class CodeActionParams {
public:
    CodeActionParams() = default;
    CodeActionParams(const TextDocumentIdentifier& textDocument, const Range& range, const CodeActionContext& context);

    // Getters
    const TextDocumentIdentifier& textDocument() const { return m_textDocument; }
    const Range& range() const { return m_range; }
    const CodeActionContext& context() const { return m_context; }

    // Setters
    void setTextDocument(const TextDocumentIdentifier& textDocument) { m_textDocument = textDocument; }
    void setRange(const Range& range) { m_range = range; }
    void setContext(const CodeActionContext& context) { m_context = context; }

    // JSON serialization
    QJsonObject toJson() const;
    static CodeActionParams fromJson(const QJsonObject& json);

private:
    TextDocumentIdentifier m_textDocument;
    Range m_range;
    CodeActionContext m_context;
};

} // namespace Lsp

#endif // LSP_TYPES_CODEACTIONPARAMS_H
