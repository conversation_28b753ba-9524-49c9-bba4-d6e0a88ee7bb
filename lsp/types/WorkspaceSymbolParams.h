#ifndef LSP_TYPES_WORKSPACESYMBOLPARAMS_H
#define LSP_TYPES_WORKSPACESYMBOLPARAMS_H

#include <QString>
#include <QJsonObject>

namespace Lsp {

/**
 * Parameters for the workspace symbol request.
 */
class WorkspaceSymbolParams {
public:
    WorkspaceSymbolParams() = default;
    WorkspaceSymbolParams(const QString& query);

    // Getters
    const QString& query() const { return m_query; }

    // Setters
    void setQuery(const QString& query) { m_query = query; }

    // JSON serialization
    QJsonObject toJson() const;
    static WorkspaceSymbolParams fromJson(const QJsonObject& json);

private:
    QString m_query;
};

} // namespace Lsp

#endif // LSP_TYPES_WORKSPACESYMBOLPARAMS_H
