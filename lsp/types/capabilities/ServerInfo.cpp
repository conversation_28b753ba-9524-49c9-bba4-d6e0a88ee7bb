#include "ServerInfo.h"

namespace Lsp {

QJsonObject ServerInfo::toJson() const {
    QJsonObject obj;
    obj["name"] = name;
    if (version.has_value()) {
        obj["version"] = version.value();
    }
    return obj;
}

ServerInfo ServerInfo::fromJson(const QJsonObject& json) {
    ServerInfo info;
    info.name = json["name"].toString();
    if (json.contains("version")) {
        info.version = json["version"].toString();
    }
    return info;
}

} // namespace Lsp
