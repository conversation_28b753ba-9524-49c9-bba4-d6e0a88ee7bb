#ifndef LSP_TYPES_CAPABILITIES_CLIENTINFO_H
#define LSP_TYPES_CAPABILITIES_CLIENTINFO_H

#include "../LspTypesCommon.h"

namespace Lsp {

/**
 * Information about the client.
 */
struct ClientInfo {
    QString name;
    std::optional<QString> version;

    QJsonObject toJson() const;
    static ClientInfo fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_CAPABILITIES_CLIENTINFO_H
