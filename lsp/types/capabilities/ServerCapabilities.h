#ifndef LSP_TYPES_CAPABILITIES_SERVERCAPABILITIES_H
#define LSP_TYPES_CAPABILITIES_SERVERCAPABILITIES_H

#include "../LspTypesCommon.h"

namespace Lsp {

/**
 * Defines the capabilities provided by a language server.
 */
struct ServerCapabilities {
    std::optional<QJsonValue> textDocumentSync;
    std::optional<bool> hoverProvider;
    std::optional<QJsonValue> completionProvider;
    std::optional<QJsonValue> signatureHelpProvider;
    std::optional<bool> definitionProvider;
    std::optional<bool> typeDefinitionProvider;
    std::optional<bool> implementationProvider;
    std::optional<bool> referencesProvider;
    std::optional<bool> documentHighlightProvider;
    std::optional<bool> documentSymbolProvider;
    std::optional<bool> workspaceSymbolProvider;
    std::optional<bool> codeActionProvider;
    std::optional<QJsonValue> codeLensProvider;
    std::optional<bool> documentFormattingProvider;
    std::optional<bool> documentRangeFormattingProvider;
    std::optional<QJsonValue> documentOnTypeFormattingProvider;
    std::optional<bool> renameProvider;
    std::optional<QJsonValue> documentLinkProvider;
    std::optional<QJsonValue> executeCommandProvider;
    std::optional<QJsonObject> experimental;
    std::optional<QJsonObject> workspace;

    QJsonObject toJson() const;
    static ServerCapabilities fromJson(const QJsonObject& json);
};

} // namespace Lsp

#endif // LSP_TYPES_CAPABILITIES_SERVERCAPABILITIES_H
