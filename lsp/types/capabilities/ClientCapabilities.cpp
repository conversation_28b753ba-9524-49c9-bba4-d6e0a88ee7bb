#include "ClientCapabilities.h"

namespace Lsp {

QJsonObject ClientCapabilities::toJson() const {
    QJsonObject obj;
    if (workspace.has_value()) {
        obj["workspace"] = workspace.value();
    }
    if (textDocument.has_value()) {
        obj["textDocument"] = textDocument.value();
    }
    if (window.has_value()) {
        obj["window"] = window.value();
    }
    if (general.has_value()) {
        obj["general"] = general.value();
    }
    if (experimental.has_value()) {
        obj["experimental"] = experimental.value();
    }
    return obj;
}

ClientCapabilities ClientCapabilities::fromJson(const QJsonObject& json) {
    ClientCapabilities caps;
    if (json.contains("workspace")) {
        caps.workspace = json["workspace"].toObject();
    }
    if (json.contains("textDocument")) {
        caps.textDocument = json["textDocument"].toObject();
    }
    if (json.contains("window")) {
        caps.window = json["window"].toObject();
    }
    if (json.contains("general")) {
        caps.general = json["general"].toObject();
    }
    if (json.contains("experimental")) {
        caps.experimental = json["experimental"].toObject();
    }
    return caps;
}

} // namespace Lsp
