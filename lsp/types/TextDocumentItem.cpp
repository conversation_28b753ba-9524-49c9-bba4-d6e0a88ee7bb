#include "TextDocumentItem.h"

namespace Lsp {

QJsonObject TextDocumentItem::toJson() const {
    QJsonObject obj;
    obj["uri"] = uri;
    obj["languageId"] = languageId;
    obj["version"] = version;
    obj["text"] = text;
    return obj;
}

TextDocumentItem TextDocumentItem::fromJson(const QJsonObject& json) {
    TextDocumentItem item;
    item.uri = json["uri"].toString();
    item.languageId = json["languageId"].toString();
    item.version = json["version"].toInt();
    item.text = json["text"].toString();
    return item;
}

} // namespace Lsp
