#include "TextDocumentIdentifier.h"

namespace Lsp {

QJsonObject TextDocumentIdentifier::toJson() const {
    QJsonObject obj;
    obj["uri"] = uri;
    return obj;
}

TextDocumentIdentifier TextDocumentIdentifier::fromJson(const QJsonObject& json) {
    TextDocumentIdentifier id;
    id.uri = json["uri"].toString();
    return id;
}

bool TextDocumentIdentifier::operator==(const TextDocumentIdentifier& other) const {
    return uri == other.uri;
}

} // namespace Lsp
