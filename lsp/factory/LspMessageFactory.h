#ifndef LSP_FACTORY_LSPMESSAGEFACTORY_H
#define LSP_FACTORY_LSPMESSAGEFACTORY_H

#include "../messages/base/LspMessage.h"
#include "LspMessageRegistry.h"
#include <QJsonObject>
#include <QString>
#include <QHash>
#include <QStringList>
#include <QMetaObject>
#include <QMetaType>
#include <functional>
#include <memory>

namespace Lsp {

/**
 * Factory class for creating LSP messages from JSON
 */
class LspMessageFactory {
public:
    using MessageCreator = std::function<std::unique_ptr<LspMessage>(const QJsonObject&)>;

    LspMessageFactory();
    ~LspMessageFactory() = default;

    /**
     * Register all LSP message classes automatically using Qt's MetaObject system
     */
    void registerAllMessageClasses();

    /**
     * Static method to register all LSP message types with Qt's MetaType system
     * This should be called once during application startup
     */
    static void registerLspMessageTypes();

    /**
     * Force registration of a specific LSP message class
     * This ensures the class is available for automatic discovery
     */
    template<typename T>
    static void forceRegisterClass() {
        // Access the static meta object to force registration
        const QMetaObject* meta = &T::staticMetaObject;
        Q_UNUSED(meta)
        // The act of accessing staticMetaObject registers it with Qt's type system
    }

private:
    /**
     * Static registry of LSP message classes for automatic discovery
     */
    static QList<const QMetaObject*>& getRegisteredMetaObjects();

    /**
     * Static registry of factory functions for automatic message creation
     */
    static QHash<QString, std::function<std::unique_ptr<LspMessage>(const QJsonObject&)>>& getFactoryRegistry();

public:
    /**
     * Register a meta object for automatic discovery
     * This is called by the LSP_MESSAGE_REGISTER macro
     */
    static void registerMetaObject(const QMetaObject* metaObject);

    /**
     * Register a factory function for a specific class
     * This is called by the LSP_MESSAGE_REGISTER macro
     */
    static void registerFactory(const QString& className, std::function<std::unique_ptr<LspMessage>(const QJsonObject&)> factory);

    /**
     * Create an LSP message from JSON
     * @param json The JSON object representing the message
     * @param registry Optional registry for response type lookup
     * @return Unique pointer to the created message, or nullptr if creation failed
     */
    std::unique_ptr<LspMessage> createMessage(const QJsonObject& json, LspMessageRegistry* registry = nullptr);

    /**
     * Register a custom message creator for a specific method
     * @param method The LSP method name
     * @param creator Function to create the message from JSON
     */
    void registerMessageCreator(const QString& method, MessageCreator creator);

    /**
     * Check if a method is supported
     * @param method The LSP method name
     * @return True if the method is supported
     */
    bool isMethodSupported(const QString& method) const;

    /**
     * Get all supported methods
     * @return List of all supported method names
     */
    QStringList getSupportedMethods() const;

    /**
     * Serialize an LSP message to JSON with proper LSP headers
     * @param message The message to serialize
     * @return The complete LSP message as bytes (including Content-Length header)
     */
    static QByteArray serializeMessage(const LspMessage& message);

    /**
     * Parse LSP message from raw bytes (including headers)
     * @param data The raw bytes containing LSP message with headers
     * @param bytesConsumed Output parameter indicating how many bytes were consumed
     * @param registry Optional registry for response type lookup
     * @return Parsed message or nullptr if parsing failed/incomplete
     */
    std::unique_ptr<LspMessage> parseMessage(const QByteArray& data, int& bytesConsumed, LspMessageRegistry* registry = nullptr);

private:
    /**
     * Register a single message class using its MetaObject
     */
    void registerMessageClass(const QMetaObject* metaObject);

    /**
     * Create a message creator function from a MetaObject
     */
    MessageCreator createMessageCreator(const QMetaObject* metaObject);

    QHash<QString, MessageCreator> m_requestCreators;
    QHash<QString, MessageCreator> m_responseCreators;
    QHash<QString, MessageCreator> m_notificationCreators;
};

/**
 * Utility functions for LSP message handling
 */
namespace MessageUtils {
    /**
     * Check if a JSON object represents a request
     */
    bool isRequest(const QJsonObject& json);

    /**
     * Check if a JSON object represents a response
     */
    bool isResponse(const QJsonObject& json);

    /**
     * Check if a JSON object represents a notification
     */
    bool isNotification(const QJsonObject& json);

    /**
     * Check if a JSON object represents an error response
     */
    bool isError(const QJsonObject& json);

    /**
     * Extract the method name from a JSON message
     */
    QString getMethod(const QJsonObject& json);

    /**
     * Extract the ID from a JSON message
     */
    QVariant getId(const QJsonObject& json);

    /**
     * Validate JSON-RPC 2.0 format
     */
    bool isValidJsonRpc(const QJsonObject& json);

    /**
     * Create a method not found error response
     */
    QJsonObject createMethodNotFoundError(const QVariant& id, const QString& method);

    /**
     * Create an invalid params error response
     */
    QJsonObject createInvalidParamsError(const QVariant& id, const QString& message = "");

    /**
     * Create an internal error response
     */
    QJsonObject createInternalError(const QVariant& id, const QString& message = "");
}

} // namespace Lsp

/**
 * Macro to automatically register an LSP message class for discovery
 * Usage: Add LSP_MESSAGE_REGISTER(ClassName) after the class definition
 * Note: Use this macro inside the Lsp namespace, with just the class name (no namespace prefix)
 */
#define LSP_MESSAGE_REGISTER(ClassName) \
    namespace { \
        struct ClassName##_AutoRegister { \
            ClassName##_AutoRegister() { \
                ::Lsp::LspMessageFactory::registerMetaObject(&::Lsp::ClassName::staticMetaObject); \
                ::Lsp::LspMessageFactory::registerFactory( \
                    QString::fromLatin1(#ClassName), \
                    [](const QJsonObject& json) -> std::unique_ptr<::Lsp::LspMessage> { \
                        return ::Lsp::ClassName::fromJson(json); \
                    } \
                ); \
            } \
        }; \
        static ClassName##_AutoRegister ClassName##_auto_register_instance; \
    }

#endif // LSP_FACTORY_LSPMESSAGEFACTORY_H
