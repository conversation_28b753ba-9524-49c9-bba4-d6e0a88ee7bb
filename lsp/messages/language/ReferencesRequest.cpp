#include "ReferencesRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString ReferencesRequest::METHOD = "textDocument/references";

ReferencesRequest::ReferencesRequest(const QVariant& id, const ReferenceParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}



std::unique_ptr<ReferencesRequest> ReferencesRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    ReferenceParams params = ReferenceParams::fromJson(json["params"].toObject());
    return std::make_unique<ReferencesRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ReferencesRequest)
