#include "CodeActionResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue CodeActionResponse::getResult() const {
    return m_result.toJson();
}

std::unique_ptr<CodeActionResponse> CodeActionResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    CodeAction result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        result = CodeAction::fromJson(json["result"].toObject());
    }
    
    return std::make_unique<CodeActionResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(CodeActionResponse)
