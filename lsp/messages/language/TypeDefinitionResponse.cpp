#include "TypeDefinitionResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue TypeDefinitionResponse::getResult() const {
    return m_result.toJson();
}

std::unique_ptr<TypeDefinitionResponse> TypeDefinitionResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    Location result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        result = Location::fromJson(json["result"].toObject());
    }
    
    return std::make_unique<TypeDefinitionResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(TypeDefinitionResponse)
