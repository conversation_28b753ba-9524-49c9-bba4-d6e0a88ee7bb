#ifndef LSP_MESSAGES_LANGUAGE_RENAMEREQUEST_H
#define LSP_MESSAGES_LANGUAGE_RENAMEREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/RenameParams.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * Rename request.
 */
class RenameRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/rename")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    RenameRequest() = default;
    RenameRequest(const QVariant& id, const RenameParams& params);

    // Getters
    const RenameParams& params() const { return m_params; }

    // Setters
    void setParams(const RenameParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<RenameRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    RenameParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_RENAMEREQUEST_H