#include "RenameRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString RenameRequest::METHOD = "textDocument/rename";

RenameRequest::RenameRequest(const QVariant& id, const RenameParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}



std::unique_ptr<RenameRequest> RenameRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    RenameParams params = RenameParams::fromJson(json["params"].toObject());
    return std::make_unique<RenameRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(RenameRequest)
