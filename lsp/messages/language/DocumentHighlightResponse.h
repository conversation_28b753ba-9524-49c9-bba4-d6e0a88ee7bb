#ifndef LSP_MESSAGES_LANGUAGE_DOCUMENTHIGHLIGHTRESPONSE_H
#define LSP_MESSAGES_LANGUAGE_DOCUMENTHIGHLIGHTRESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/DocumentHighlight.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * Response to a documenthighlight request.
 */
class DocumentHighlightResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/documentHighlight")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    DocumentHighlightResponse(const QVariant& id, const DocumentHighlight& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    DocumentHighlight getDocumentHighlight() const { return m_result; }

    // Factory method
    static std::unique_ptr<DocumentHighlightResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    DocumentHighlight m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_DOCUMENTHIGHLIGHTRESPONSE_H