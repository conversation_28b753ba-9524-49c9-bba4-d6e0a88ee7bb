#ifndef LSP_MESSAGES_LANGUAGE_SIGNATUREHELPREQUEST_H
#define LSP_MESSAGES_LANGUAGE_SIGNATUREHELPREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/TextDocumentPositionParams.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * SignatureHelp request.
 */
class SignatureHelpRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/signatureHelp")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    SignatureHelpRequest() = default;
    SignatureHelpRequest(const QVariant& id, const TextDocumentPositionParams& params);

    // Getters
    const TextDocumentPositionParams& params() const { return m_params; }

    // Setters
    void setParams(const TextDocumentPositionParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<SignatureHelpRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    TextDocumentPositionParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_SIGNATUREHELPREQUEST_H