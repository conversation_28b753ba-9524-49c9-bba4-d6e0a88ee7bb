#include "SemanticTokensRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString SemanticTokensRequest::METHOD = "textDocument/semanticTokens/full";

SemanticTokensRequest::SemanticTokensRequest(const QVariant& id, const SemanticTokensParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}



std::unique_ptr<SemanticTokensRequest> SemanticTokensRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    SemanticTokensParams params = SemanticTokensParams::fromJson(json["params"].toObject());
    return std::make_unique<SemanticTokensRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(SemanticTokensRequest)
