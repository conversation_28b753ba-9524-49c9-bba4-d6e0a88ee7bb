#include "DocumentHighlightResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue DocumentHighlightResponse::getResult() const {
    return m_result.toJson();
}

std::unique_ptr<DocumentHighlightResponse> DocumentHighlightResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    DocumentHighlight result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        result = DocumentHighlight::fromJson(json["result"].toObject());
    }
    
    return std::make_unique<DocumentHighlightResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(DocumentHighlightResponse)
