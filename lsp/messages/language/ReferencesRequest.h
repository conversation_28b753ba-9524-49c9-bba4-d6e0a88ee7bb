#ifndef LSP_MESSAGES_LANGUAGE_REFERENCESREQUEST_H
#define LSP_MESSAGES_LANGUAGE_REFERENCESREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/ReferenceParams.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * References request.
 */
class ReferencesRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/references")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    ReferencesRequest() = default;
    ReferencesRequest(const QVariant& id, const ReferenceParams& params);

    // Getters
    const ReferenceParams& params() const { return m_params; }

    // Setters
    void setParams(const ReferenceParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<ReferencesRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    ReferenceParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_REFERENCESREQUEST_H