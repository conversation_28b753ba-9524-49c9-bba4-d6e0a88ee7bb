#ifndef LSP_MESSAGES_LANGUAGE_DOCUMENTSYMBOLREQUEST_H
#define LSP_MESSAGES_LANGUAGE_DOCUMENTSYMBOLREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/TextDocumentIdentifier.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * Parameters for the document symbol request.
 */
class DocumentSymbolParams {
public:
    DocumentSymbolParams() = default;
    DocumentSymbolParams(const TextDocumentIdentifier& textDocument);

    // Getters
    const TextDocumentIdentifier& textDocument() const { return m_textDocument; }

    // Setters
    void setTextDocument(const TextDocumentIdentifier& textDocument) { m_textDocument = textDocument; }

    // JSON serialization
    QJsonObject toJson() const;
    static DocumentSymbolParams fromJson(const QJsonObject& json);

private:
    TextDocumentIdentifier m_textDocument;
};

/**
 * The document symbol request is sent from the client to the server to return a flat list of all symbols
 * found in a given text document. Neither the symbol's location range nor the symbol's container name
 * should be used to infer a hierarchy.
 */
class DocumentSymbolRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/documentSymbol")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    DocumentSymbolRequest() = default;
    DocumentSymbolRequest(const QVariant& id, const DocumentSymbolParams& params);

    // Getters
    const DocumentSymbolParams& params() const { return m_params; }

    // Setters
    void setParams(const DocumentSymbolParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<DocumentSymbolRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    DocumentSymbolParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_DOCUMENTSYMBOLREQUEST_H
