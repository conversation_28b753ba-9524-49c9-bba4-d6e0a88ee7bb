#ifndef LSP_MESSAGES_LANGUAGE_REFERENCESRESPONSE_H
#define LSP_MESSAGES_LANGUAGE_REFERENCESRESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/Location.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * Response to a references request.
 */
class ReferencesResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/references")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    ReferencesResponse(const QVariant& id, const Location& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    Location getReferences() const { return m_result; }

    // Factory method
    static std::unique_ptr<ReferencesResponse> from<PERSON>son(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    Location m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_REFERENCESRESPONSE_H