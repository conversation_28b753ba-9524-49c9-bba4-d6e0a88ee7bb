#include "TypeDefinitionRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString TypeDefinitionRequest::METHOD = "textDocument/typeDefinition";

TypeDefinitionRequest::TypeDefinitionRequest(const QVariant& id, const TextDocumentPositionParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}



std::unique_ptr<TypeDefinitionRequest> TypeDefinitionRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    TextDocumentPositionParams params = TextDocumentPositionParams::fromJson(json["params"].toObject());
    return std::make_unique<TypeDefinitionRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(TypeDefinitionRequest)
