#ifndef LSP_MESSAGES_LANGUAGE_SIGNATUREHELPRESPONSE_H
#define LSP_MESSAGES_LANGUAGE_SIGNATUREHELPRESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/SignatureHelp.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * Response to a signaturehelp request.
 */
class SignatureHelpResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/signatureHelp")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    SignatureHelpResponse(const QVariant& id, const SignatureHelp& result, QObject* parent = nullptr)
        : LspResponse(id, parent), m_result(result) {}

    QJsonValue getResult() const override;
    SignatureHelp getSignatureHelp() const { return m_result; }

    // Factory method
    static std::unique_ptr<SignatureHelpResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    SignatureHelp m_result;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_SIGNATUREHELPRESPONSE_H