#ifndef LSP_MESSAGES_LANGUAGE_CODEACTIONREQUEST_H
#define LSP_MESSAGES_LANGUAGE_CODEACTIONREQUEST_H

#include "../base/LspRequest.h"
#include "../../types/CodeActionParams.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {

/**
 * CodeAction request.
 */
class CodeActionRequest : public LspRequest {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/codeAction")
    Q_CLASSINFO("LSP_TYPE", "request")

public:
    static const QString METHOD;

    CodeActionRequest() = default;
    CodeActionRequest(const QVariant& id, const CodeActionParams& params);

    // Getters
    const CodeActionParams& params() const { return m_params; }

    // Setters
    void setParams(const CodeActionParams& params) { m_params = params; }

    // LspRequest interface
    QJsonObject getParams() const override { return m_params.toJson(); }

    // Factory method
    static std::unique_ptr<CodeActionRequest> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    CodeActionParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_CODEACTIONREQUEST_H