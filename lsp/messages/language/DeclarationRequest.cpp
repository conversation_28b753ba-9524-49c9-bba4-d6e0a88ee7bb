#include "DeclarationRequest.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

const QString DeclarationRequest::METHOD = "textDocument/declaration";

DeclarationRequest::DeclarationRequest(const QVariant& id, const TextDocumentPositionParams& params)
    : LspRequest(id, METHOD), m_params(params) {
}



std::unique_ptr<DeclarationRequest> DeclarationRequest::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    TextDocumentPositionParams params = TextDocumentPositionParams::fromJson(json["params"].toObject());
    return std::make_unique<DeclarationRequest>(id, params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(DeclarationRequest)
