#include "WorkspaceSymbolResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

QJsonValue WorkspaceSymbolResponse::getResult() const {
    return m_result.toJson();
}

std::unique_ptr<WorkspaceSymbolResponse> WorkspaceSymbolResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"];
    SymbolInformation result;
    
    if (json.contains("result") && !json["result"].isNull()) {
        result = SymbolInformation::fromJson(json["result"].toObject());
    }
    
    return std::make_unique<WorkspaceSymbolResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(WorkspaceSymbolResponse)
