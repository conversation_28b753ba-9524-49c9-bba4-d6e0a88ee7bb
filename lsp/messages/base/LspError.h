#ifndef LSP_MESSAGES_BASE_LSPERROR_H
#define LSP_MESSAGES_BASE_LSPERROR_H

#include "LspMessage.h"
#include "../../types/LspTypesCommon.h"
#include <QVariant>
#include <QJsonValue>
#include <optional>
#include <memory>

namespace Lsp {

/**
 * LSP error response message
 */
class LspError : public LspMessage {
    Q_OBJECT

public:
    struct ErrorData {
        integer code;
        QString message;
        std::optional<QJsonValue> data;

        QJsonObject toJson() const;
        static ErrorData fromJson(const QJsonObject& json);
    };

    LspError(const QVariant& id, const ErrorData& error) : m_id(id), m_error(error) {}
    virtual ~LspError() = default;

    QVariant getId() const { return m_id; }
    ErrorData getError() const { return m_error; }
    QString getMethod() const override { return ""; } // Errors don't have methods
    bool isError() const override { return true; }

    QJsonObject toJson() const override;
    static std::unique_ptr<LspError> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    QVariant m_id;
    ErrorData m_error;
};

// Error codes from LSP specification
namespace ErrorCodes {
    constexpr integer ParseError = -32700;
    constexpr integer InvalidRequest = -32600;
    constexpr integer MethodNotFound = -32601;
    constexpr integer InvalidParams = -32602;
    constexpr integer InternalError = -32603;
    constexpr integer ServerNotInitialized = -32002;
    constexpr integer UnknownErrorCode = -32001;
    constexpr integer RequestFailed = -32803;
    constexpr integer ServerCancelled = -32802;
    constexpr integer ContentModified = -32801;
    constexpr integer RequestCancelled = -32800;
}

// Message type constants
namespace MessageType {
    constexpr integer Error = 1;
    constexpr integer Warning = 2;
    constexpr integer Info = 3;
    constexpr integer Log = 4;
    constexpr integer Debug = 5;
}

} // namespace Lsp

#endif // LSP_MESSAGES_BASE_LSPERROR_H
