#include "InitializeResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// InitializeResponse implementation
std::unique_ptr<InitializeResponse> InitializeResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"].toVariant();
    InitializeResult result = InitializeResult::fromJson(json["result"].toObject());
    return std::make_unique<InitializeResponse>(id, result);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(InitializeResponse)
