#include "InitializedNotification.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// InitializedNotification implementation
std::unique_ptr<InitializedNotification> InitializedNotification::from<PERSON>son(const QJsonObject& json) {
    InitializedParams params;
    if (json.contains("params")) {
        params = InitializedParams::fromJson(json["params"].toObject());
    }
    return std::make_unique<InitializedNotification>(params);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(InitializedNotification)
