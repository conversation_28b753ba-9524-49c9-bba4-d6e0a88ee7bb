#include "ShutdownResponse.h"
#include "../../factory/LspMessageFactory.h"

namespace Lsp {

// ShutdownResponse implementation
std::unique_ptr<ShutdownResponse> ShutdownResponse::fromJson(const QJsonObject& json) {
    QVariant id = json["id"].toVariant();
    return std::make_unique<ShutdownResponse>(id);
}

} // namespace Lsp

// Automatically register this class for discovery
LSP_MESSAGE_REGISTER(ShutdownResponse)
