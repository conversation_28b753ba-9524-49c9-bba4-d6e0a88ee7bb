#ifndef LSP_MESSAGES_LIFECYCLE_INITIALIZEDNOTIFICATION_H
#define LSP_MESSAGES_LIFECYCLE_INITIALIZEDNOTIFICATION_H

#include "../base/LspNotification.h"
#include <memory>

namespace Lsp {

/**
 * Initialized notification
 */
class InitializedNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "initialized")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    struct InitializedParams {
        // Currently empty, but defined for future extensibility
        QJsonObject toJson() const { return QJsonObject(); }
        static InitializedParams fromJson(const QJsonObject&) { return InitializedParams(); }
    };

    InitializedNotification(QObject* parent = nullptr) : LspNotification("initialized", parent) {}
    InitializedNotification(const InitializedParams& params, QObject* parent = nullptr)
        : LspNotification("initialized", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    InitializedParams getInitializedParams() const { return m_params; }

    static std::unique_ptr<InitializedNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    InitializedParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_LIFECYCLE_INITIALIZEDNOTIFICATION_H
