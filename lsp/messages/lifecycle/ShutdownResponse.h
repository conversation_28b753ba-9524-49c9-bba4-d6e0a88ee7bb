#ifndef LSP_MESSAGES_LIFECYCLE_SHUTDOWNRESPONSE_H
#define LSP_MESSAGES_LIFECYCLE_SHUTDOWNRESPONSE_H

#include "../base/LspResponse.h"
#include <memory>

namespace Lsp {

/**
 * Shutdown response
 */
class ShutdownResponse : public LspResponse {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "shutdown")
    Q_CLASSINFO("LSP_TYPE", "response")

public:
    ShutdownResponse(const QVariant& id, QObject* parent = nullptr) : LspResponse(id, parent) {}

    QJsonValue getResult() const override { return QJsonValue::Null; } // Result is null

    static std::unique_ptr<ShutdownResponse> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = from<PERSON>son(json);
        return result.release(); // Transfer ownership to caller
    }
};

} // namespace Lsp

#endif // LSP_MESSAGES_LIFECYCLE_SHUTDOWNRESPONSE_H
