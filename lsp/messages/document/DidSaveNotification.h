#ifndef LSP_MESSAGES_DOCUMENT_DIDSAVENOTIFICATION_H
#define LSP_MESSAGES_DOCUMENT_DIDSAVENOTIFICATION_H

#include "../base/LspNotification.h"
#include "../../types/LspTypes.h"
#include <optional>
#include <memory>

namespace Lsp {

/**
 * Did save text document notification
 */
class DidSaveNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/didSave")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    struct DidSaveTextDocumentParams {
        TextDocumentIdentifier textDocument;
        std::optional<QString> text;

        QJsonObject toJson() const;
        static DidSaveTextDocumentParams fromJson(const QJsonObject& json);
    };

    DidSaveNotification(const DidSaveTextDocumentParams& params, QObject* parent = nullptr)
        : LspNotification("textDocument/didSave", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    DidSaveTextDocumentParams getDidSaveParams() const { return m_params; }

    static std::unique_ptr<DidSaveNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    DidSaveTextDocumentParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_DIDSAVENOTIFICATION_H
