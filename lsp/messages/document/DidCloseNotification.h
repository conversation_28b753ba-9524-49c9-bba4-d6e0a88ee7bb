#ifndef LSP_MESSAGES_DOCUMENT_DIDCLOSENOTIFICATION_H
#define LSP_MESSAGES_DOCUMENT_DIDCLOSENOTIFICATION_H

#include "../base/LspNotification.h"
#include "../../types/LspTypes.h"
#include <memory>

namespace Lsp {

/**
 * Did close text document notification
 */
class DidCloseNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/didClose")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    struct DidCloseTextDocumentParams {
        TextDocumentIdentifier textDocument;

        QJsonObject toJson() const;
        static DidCloseTextDocumentParams fromJson(const QJsonObject& json);
    };

    DidCloseNotification(const DidCloseTextDocumentParams& params, QObject* parent = nullptr)
        : LspNotification("textDocument/didClose", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    DidCloseTextDocumentParams getDidCloseParams() const { return m_params; }

    static std::unique_ptr<DidCloseNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    DidCloseTextDocumentParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_DIDCLOSENOTIFICATION_H
