#ifndef LSP_MESSAGES_DOCUMENT_DIDOPENNOTIFICATION_H
#define LSP_MESSAGES_DOCUMENT_DIDOPENNOTIFICATION_H

#include "../base/LspNotification.h"
#include "../../types/LspTypes.h"
#include <memory>

namespace Lsp {

/**
 * Did open text document notification
 */
class DidOpenNotification : public LspNotification {
    Q_OBJECT
    Q_CLASSINFO("LSP_METHOD", "textDocument/didOpen")
    Q_CLASSINFO("LSP_TYPE", "notification")

public:
    struct DidOpenTextDocumentParams {
        TextDocumentItem textDocument;

        QJsonObject toJson() const;
        static DidOpenTextDocumentParams fromJson(const QJsonObject& json);
    };

    DidOpenNotification(const DidOpenTextDocumentParams& params, QObject* parent = nullptr)
        : LspNotification("textDocument/didOpen", parent), m_params(params) {}

    QJsonObject getParams() const override { return m_params.toJson(); }
    DidOpenTextDocumentParams getDidOpenParams() const { return m_params; }

    static std::unique_ptr<DidOpenNotification> fromJson(const QJsonObject& json);

    // Factory method for automatic registration
    Q_INVOKABLE static QObject* createFromJsonPtr(const QJsonObject& json) {
        auto result = fromJson(json);
        return result.release(); // Transfer ownership to caller
    }

private:
    DidOpenTextDocumentParams m_params;
};

} // namespace Lsp

#endif // LSP_MESSAGES_DOCUMENT_DIDOPENNOTIFICATION_H
