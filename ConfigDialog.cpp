#include "ConfigDialog.h"

#include "TrySailGlobal.h"

#include <QDialogButtonBox>
#include <QFormLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QSettings>
#include <QTabWidget>
#include <QVBoxLayout>
#include <QString>
#include <QStringLiteral>
using namespace Qt::Literals::StringLiterals;

const auto SETTINGS_USER_NAME = u"UserName"_s;

QString QConfigDialog::userName()
{
    return QSettings{}.value( SETTINGS_USER_NAME, u""_s ).toString();
}

void QConfigDialog::accept()
{
    auto settings = QSettings{};

    const auto userName = m_txtUserName->text();
    settings.setValue( SETTINGS_USER_NAME, userName );

    QDialog::accept();
}

QConfigDialog::QConfigDialog(QWidget *parent)
    : QDialog{parent}
{
    setWindowTitle( tr("Configuration" ) );
    setWindowModality( Qt::WindowModality::ApplicationModal );
    setAttribute( Qt::WA_DeleteOnClose, true );

    auto layoutOverall = new QVBoxLayout;

    auto tabPages = new QTabWidget{ this };

    // general setup
    {
        auto pageUser = new QWidget{ this };

        auto layoutUser = new QFormLayout;

        m_txtUserName = new QLineEdit{ this };
        m_txtUserName->setText( userName() );
        m_txtUserName->setMinimumWidth( 300 );

        layoutUser->addRow( tr("User Name:"), m_txtUserName );

        pageUser->setLayout( layoutUser );

        tabPages->addTab( pageUser, *iconTrySail(), tr("User") );
    }

    // python setup
    {
        auto pagePython = new QWidget{ this };

        auto layoutPython = new QGridLayout;

        auto nRow              = int{ 0 };
        auto labelInstructions = new QLabel( tr("(Python does not require any setup.)"), this );
        labelInstructions->setWordWrap( true );
        labelInstructions->setMinimumHeight( 100 );
        layoutPython->addWidget( labelInstructions, nRow, 0, 0, 3 );

        pagePython->setLayout( layoutPython );

        tabPages->addTab( pagePython, *iconPython(), u"Python"_s );
    }

    // markdown setup
    {
        auto pageMarkdown = new QWidget{ this };

        auto layoutMarkdown = new QGridLayout;

        auto nRow              = int{ 0 };
        auto labelInstructions = new QLabel( tr("(Markdown does not require any setup.)"), this );
        labelInstructions->setWordWrap( true );
        labelInstructions->setMinimumHeight( 100 );
        layoutMarkdown->addWidget( labelInstructions, nRow, 0, 0, 3 );

        pageMarkdown->setLayout( layoutMarkdown );

        tabPages->addTab( pageMarkdown, *iconMarkdown(), u"Markdown"_s );
    }

    // sql setup
    {
        auto pageSql = new QWidget{ this };

        auto layoutSql = new QGridLayout;

        auto nRow              = int{ 0 };
        auto labelInstructions = new QLabel( tr("(SQL does not require any setup.)"), this );
        labelInstructions->setWordWrap( true );
        labelInstructions->setMinimumHeight( 100 );
        layoutSql->addWidget( labelInstructions, nRow, 0, 0, 3 );

        pageSql->setLayout( layoutSql );

        tabPages->addTab( pageSql, *iconSql(), u"SQL"_s );
    }
    layoutOverall->addWidget( tabPages, 1 );

    auto btnBox = new QDialogButtonBox{ QDialogButtonBox::Ok | QDialogButtonBox::Cancel, this };
    connect( btnBox, &QDialogButtonBox::accepted, this, &QConfigDialog::accept );
    connect( btnBox, &QDialogButtonBox::rejected, this, &QDialog::reject );
    layoutOverall->addWidget( btnBox );

    setLayout( layoutOverall );
}
