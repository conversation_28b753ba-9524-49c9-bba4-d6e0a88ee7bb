#!/usr/bin/env python3
"""
Script to generate LSP message classes for the remaining methods.
"""

import os
from pathlib import Path

# Define the message specifications
MESSAGES = [
    # Language Features
    {
        "name": "Declaration",
        "method": "textDocument/declaration",
        "type": "request",
        "params": "TextDocumentPositionParams",
        "result": "Location | Location[] | LocationLink[]"
    },
    {
        "name": "TypeDefinition", 
        "method": "textDocument/typeDefinition",
        "type": "request",
        "params": "TextDocumentPositionParams",
        "result": "Location | Location[] | LocationLink[]"
    },
    {
        "name": "DocumentHighlight",
        "method": "textDocument/documentHighlight", 
        "type": "request",
        "params": "TextDocumentPositionParams",
        "result": "DocumentHighlight[]"
    },
    {
        "name": "References",
        "method": "textDocument/references",
        "type": "request", 
        "params": "ReferenceParams",
        "result": "Location[]"
    },
    {
        "name": "Rename",
        "method": "textDocument/rename",
        "type": "request",
        "params": "RenameParams", 
        "result": "WorkspaceEdit"
    },
    {
        "name": "CodeAction",
        "method": "textDocument/codeAction",
        "type": "request",
        "params": "CodeActionParams",
        "result": "(Command | CodeAction)[]"
    },
    {
        "name": "SignatureHelp",
        "method": "textDocument/signatureHelp",
        "type": "request",
        "params": "TextDocumentPositionParams",
        "result": "SignatureHelp"
    },
    {
        "name": "SemanticTokens",
        "method": "textDocument/semanticTokens/full",
        "type": "request",
        "params": "SemanticTokensParams",
        "result": "SemanticTokens"
    },
    {
        "name": "CompletionItemResolve",
        "method": "completionItem/resolve",
        "type": "request",
        "params": "CompletionItem",
        "result": "CompletionItem"
    },
    # Workspace
    {
        "name": "WorkspaceSymbol",
        "method": "workspace/symbol",
        "type": "request",
        "params": "WorkspaceSymbolParams",
        "result": "SymbolInformation[]"
    }
]

def create_request_header(name, method, params_type):
    return f'''#ifndef LSP_MESSAGES_LANGUAGE_{name.upper()}REQUEST_H
#define LSP_MESSAGES_LANGUAGE_{name.upper()}REQUEST_H

#include "../base/LspRequest.h"
#include "../../types/{params_type}.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {{

/**
 * {name} request.
 */
class {name}Request : public LspRequest {{
public:
    static const QString METHOD;

    {name}Request() = default;
    {name}Request(const QVariant& id, const {params_type}& params);

    // Getters
    const {params_type}& params() const {{ return m_params; }}

    // Setters
    void setParams(const {params_type}& params) {{ m_params = params; }}

    // LspMessage interface
    QString method() const override {{ return METHOD; }}
    QJsonObject toJson() const override;

    // Factory method
    static std::unique_ptr<{name}Request> fromJson(const QJsonObject& json);

private:
    {params_type} m_params;
}};

}} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_{name.upper()}REQUEST_H'''

def create_request_cpp(name, method, params_type):
    return f'''#include "{name}Request.h"

namespace Lsp {{

const QString {name}Request::METHOD = "{method}";

{name}Request::{name}Request(const QVariant& id, const {params_type}& params)
    : LspRequest(id, METHOD), m_params(params) {{
}}

QJsonObject {name}Request::toJson() const {{
    QJsonObject obj = LspRequest::toJson();
    obj["params"] = m_params.toJson();
    return obj;
}}

std::unique_ptr<{name}Request> {name}Request::fromJson(const QJsonObject& json) {{
    auto request = std::make_unique<{name}Request>();
    request->setId(json["id"]);
    request->setParams({params_type}::fromJson(json["params"].toObject()));
    return request;
}}

}} // namespace Lsp'''

def create_response_header(name, result_type):
    return f'''#ifndef LSP_MESSAGES_LANGUAGE_{name.upper()}RESPONSE_H
#define LSP_MESSAGES_LANGUAGE_{name.upper()}RESPONSE_H

#include "../base/LspResponse.h"
#include "../../types/{result_type}.h"
#include <QJsonObject>
#include <memory>

namespace Lsp {{

/**
 * Response to a {name.lower()} request.
 */
class {name}Response : public LspResponse {{
public:
    {name}Response() = default;
    {name}Response(const QVariant& id, const {result_type}& result);

    // Getters
    const {result_type}& result() const {{ return m_result; }}
    bool hasResult() const {{ return m_hasResult; }}

    // Setters
    void setResult(const {result_type}& result) {{ m_result = result; m_hasResult = true; }}

    // LspMessage interface
    QJsonObject toJson() const override;

    // Factory method
    static std::unique_ptr<{name}Response> fromJson(const QJsonObject& json);

private:
    {result_type} m_result;
    bool m_hasResult = false;
}};

}} // namespace Lsp

#endif // LSP_MESSAGES_LANGUAGE_{name.upper()}RESPONSE_H'''

def create_response_cpp(name, result_type):
    return f'''#include "{name}Response.h"

namespace Lsp {{

{name}Response::{name}Response(const QVariant& id, const {result_type}& result)
    : LspResponse(id), m_result(result), m_hasResult(true) {{
}}

QJsonObject {name}Response::toJson() const {{
    QJsonObject obj = LspResponse::toJson();
    if (m_hasResult) {{
        obj["result"] = m_result.toJson();
    }} else {{
        obj["result"] = QJsonValue::Null;
    }}
    return obj;
}}

std::unique_ptr<{name}Response> {name}Response::fromJson(const QJsonObject& json) {{
    auto response = std::make_unique<{name}Response>();
    response->setId(json["id"]);
    
    if (json.contains("result") && !json["result"].isNull()) {{
        response->setResult({result_type}::fromJson(json["result"].toObject()));
    }}
    
    return response;
}}

}} // namespace Lsp'''

def main():
    base_dir = Path("lsp/messages/language")
    base_dir.mkdir(parents=True, exist_ok=True)
    
    for msg in MESSAGES:
        name = msg["name"]
        method = msg["method"]
        params_type = msg["params"]
        
        # Create request files
        request_h = create_request_header(name, method, params_type)
        request_cpp = create_request_cpp(name, method, params_type)
        
        with open(base_dir / f"{name}Request.h", "w") as f:
            f.write(request_h)
        with open(base_dir / f"{name}Request.cpp", "w") as f:
            f.write(request_cpp)
        
        # Create response files (simplified for now)
        if "result" in msg:
            result_type = msg["result"].split(" | ")[0]  # Take first type for simplicity
            if result_type.endswith("[]"):
                result_type = result_type[:-2]  # Remove array notation for now
            
            response_h = create_response_header(name, result_type)
            response_cpp = create_response_cpp(name, result_type)
            
            with open(base_dir / f"{name}Response.h", "w") as f:
                f.write(response_h)
            with open(base_dir / f"{name}Response.cpp", "w") as f:
                f.write(response_cpp)
        
        print(f"Generated {name} request/response")

if __name__ == "__main__":
    main()
