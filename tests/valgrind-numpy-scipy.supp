# Valgrind suppression file for NumPy & SciPy errors and leaks in pybind11 tests
#
# On updating a dependency, to get a list of "default" leaks in e.g. NumPy, run
# `PYTHONMALLOC=malloc valgrind --leak-check=full --show-leak-kinds=definite,indirect python3.9-dbg -c "import numpy"`
# To use these suppression files, add e.g. `--suppressions=valgrind-numpy-scipy.supp`

{
   Leaks when importing NumPy
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_GC_Alloc
   fun:_PyObject_GC_Malloc
   fun:_PyObject_GC_NewVar
   fun:tuple_alloc
   fun:PyTuple_Pack
   ...
   fun:__pyx_pymod_exec_*
}

{
   Leaks when importing NumPy (bis)
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_New
   fun:PyCode_NewWithPosOnlyArgs
   fun:PyCode_New
   ...
   fun:__pyx_pymod_exec_*
}

{
   Leaks when importing NumPy (ter)
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_GC_Alloc
   fun:_PyObject_GC_Malloc
   fun:_PyObject_GC_NewVar
   fun:tuple_alloc
   fun:_PyTuple_FromArray
   fun:_PyObject_MakeTpCall
   fun:_PyObject_VectorcallTstate
   fun:PyObject_Vectorcall
   fun:call_function
   fun:_PyEval_EvalFrameDefault
   fun:_PyEval_EvalFrame
   fun:function_code_fastcall
   fun:_PyFunction_Vectorcall
}

{
   Leaks when importing NumPy (quater)
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_GC_Alloc
   fun:_PyObject_GC_Malloc
   fun:_PyObject_GC_NewVar
   fun:tuple_alloc
   fun:_PyTuple_FromArray
   fun:_PyObject_MakeTpCall
   fun:_PyObject_VectorcallTstate
   fun:_PyObject_CallFunctionVa
   fun:PyObject_CallFunction
   fun:PyImport_Import
}

{
   Leaks when importing NumPy (quinquies)
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_GC_Alloc
   fun:_PyObject_GC_Malloc
   fun:_PyObject_GC_NewVar
   fun:tuple_alloc
   fun:PyTuple_New
   fun:r_object
   fun:r_object
   fun:r_object
   fun:r_object
}

{
   Leaks when importing NumPy (sexies)
   Memcheck:Leak
   fun:malloc
   fun:_PyMem_RawMalloc
   fun:PyObject_Malloc
   fun:_PyObject_GC_Alloc
   fun:_PyObject_GC_Malloc
   fun:_PyObject_GC_NewVar
   fun:tuple_alloc
   fun:PyTuple_New
   fun:dictiter_iternextitem
   fun:list_extend
   fun:_PyList_Extend
   fun:PySequence_List
}

{
   Leak when importing scipy.fft
   Memcheck:Leak
   fun:_Znwm
   fun:PyInit_pypocketfft
   fun:_PyImport_LoadDynamicModuleWithSpec
   fun:_imp_create_dynamic_impl*
   fun:_imp_create_dynamic
   fun:cfunction_vectorcall_FASTCALL
   fun:PyVectorcall_Call
   fun:_PyObject_Call
   fun:PyObject_Call
   fun:do_call_core
   fun:_PyEval_EvalFrameDefault
   fun:_PyEval_EvalFrame
   fun:_PyEval_EvalCode
}

{
   NumPy leaks when spawning a subprocess
   Memcheck:Leak
   fun:malloc
   ...
   fun:_buffer_get_info
   fun:array_getbuffer
   fun:PyObject_GetBuffer
   fun:__Pyx__GetBufferAndValidate*
   fun:__pyx_f_5numpy_6random_13bit_generator_12SeedSequence_mix_entropy
   fun:__pyx_pw_5numpy_6random_13bit_generator_12SeedSequence_1__init__
   fun:type_call
   fun:__Pyx__PyObject_CallOneArg
   fun:__pyx_pw_5numpy_6random_13bit_generator_12BitGenerator_1__init__
}
