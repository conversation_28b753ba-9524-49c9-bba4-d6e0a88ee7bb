#include "SqlEngine.h"

#include <QCborArray>
#include <QCborMap>
#include <QMetaType>
#include <QSqlDatabase>
#include <QSqlError>
#include <QSqlField>
#include <QSqlQuery>
#include <QSqlRecord>
#include <QString>
#include <QStringLiteral>
#include <QUuid>
using namespace Qt::Literals::StringLiterals;

QString translate( const QSqlField::RequiredStatus status )
{
    switch ( status ) {

    case QSqlField::RequiredStatus::Required:
        return u"Required"_s;

    case QSqlField::RequiredStatus::Optional:
        return u"optional"_s;

    case QSqlField::RequiredStatus::Unknown:
    default:
        return u"unknown"_s;
    }
}

QString translate(const QMetaType typeMeta)
{
    switch ( typeMeta.id() ) {
    case QMetaType::Void:
        return u"void"_s;
    case QMetaType::Bool:
        return u"boolean"_s;
    case QMetaType::Int:
        return u"integer"_s;
    case QMetaType::UInt:
        return u"unsigned integer"_s;
    case QMetaType::Double:
        return u"double"_s;
    case QMetaType::QChar:
        return u"character"_s;
    case QMetaType::QString:
        return u"string"_s;
    case QMetaType::QByteArray:
        return u"bytes"_s;
    case QMetaType::Nullptr:
        return u"null pointer"_s;
    case QMetaType::VoidStar:
        return u""_s;
    case QMetaType::Long:
        return u"long"_s;
    case QMetaType::LongLong:
        return u"long long"_s;
    case QMetaType::Short:
        return u"short"_s;
    case QMetaType::Char:
        return u"char"_s;
    case QMetaType::Char16:
        return u"char16"_s;
    case QMetaType::Char32:
        return u"char32"_s;
    case QMetaType::ULong:
        return u"unsigned long"_s;
    case QMetaType::ULongLong:
        return u"unsigned long long"_s;
    case QMetaType::UShort:
        return u"unsigned short"_s;
    case QMetaType::SChar:
        return u"short character"_s;
    case QMetaType::UChar:
        return u"unsigned character"_s;
    case QMetaType::Float:
        return u"float"_s;
    case QMetaType::Float16:
        return u"float 16"_s;
    case QMetaType::QObjectStar:
        return u""_s;
    case QMetaType::QCursor:
        return u""_s;
    case QMetaType::QDate:
        return u"date"_s;
    case QMetaType::QSize:
        return u""_s;
    case QMetaType::QTime:
        return u"time"_s;
    case QMetaType::QVariantList:
        return u""_s;
    case QMetaType::QPolygon:
        return u""_s;
    case QMetaType::QPolygonF:
        return u""_s;
    case QMetaType::QColor:
        return u""_s;
    case QMetaType::QColorSpace:
        return u""_s;
    case QMetaType::QSizeF:
        return u""_s;
    case QMetaType::QRectF:
        return u""_s;
    case QMetaType::QLine:
        return u""_s;
    case QMetaType::QTextLength:
        return u""_s;
    case QMetaType::QStringList:
        return u""_s;
    case QMetaType::QVariantMap:
        return u""_s;
    case QMetaType::QVariantHash:
        return u""_s;
    case QMetaType::QVariantPair:
        return u""_s;
    case QMetaType::QIcon:
        return u""_s;
    case QMetaType::QPen:
        return u""_s;
    case QMetaType::QLineF:
        return u""_s;
    case QMetaType::QTextFormat:
        return u""_s;
    case QMetaType::QRect:
        return u""_s;
    case QMetaType::QPoint:
        return u""_s;
    case QMetaType::QUrl:
        return u""_s;
    case QMetaType::QRegularExpression:
        return u""_s;
    case QMetaType::QDateTime:
        return u"datetime"_s;
    case QMetaType::QPointF:
        return u""_s;
    case QMetaType::QPalette:
        return u""_s;
    case QMetaType::QFont:
        return u""_s;
    case QMetaType::QBrush:
        return u""_s;
    case QMetaType::QRegion:
        return u""_s;
    case QMetaType::QBitArray:
        return u""_s;
    case QMetaType::QImage:
        return u""_s;
    case QMetaType::QKeySequence:
        return u""_s;
    case QMetaType::QSizePolicy:
        return u""_s;
    case QMetaType::QPixmap:
        return u""_s;
    case QMetaType::QLocale:
        return u""_s;
    case QMetaType::QBitmap:
        return u""_s;
    case QMetaType::QTransform:
        return u""_s;
    case QMetaType::QMatrix4x4:
        return u""_s;
    case QMetaType::QVector2D:
        return u""_s;
    case QMetaType::QVector3D:
        return u""_s;
    case QMetaType::QVector4D:
        return u""_s;
    case QMetaType::QQuaternion:
        return u""_s;
    case QMetaType::QEasingCurve:
        return u""_s;
    case QMetaType::QJsonValue:
        return u""_s;
    case QMetaType::QJsonObject:
        return u""_s;
    case QMetaType::QJsonArray:
        return u""_s;
    case QMetaType::QJsonDocument:
        return u""_s;
    case QMetaType::QCborValue:
        return u""_s;
    case QMetaType::QCborArray:
        return u""_s;
    case QMetaType::QCborMap:
        return u""_s;
    case QMetaType::QCborSimpleType:
        return u""_s;
    case QMetaType::QModelIndex:
        return u""_s;
    case QMetaType::QPersistentModelIndex:
        return u""_s;
    case QMetaType::QUuid:
        return u""_s;
    case QMetaType::QByteArrayList:
        return u""_s;
    case QMetaType::QVariant:
        return u""_s;
    case QMetaType::User:
        return u""_s;
    case QMetaType::UnknownType:
        return u""_s;
    default:
        return u"Unknown"_s;
    }
}

QSqlEngine::QSqlEngine()
    : QContentEngine()
    , m_dbConnectionName( QUuid::createUuid().toString() )
{
    m_supportsSetup = true;
#ifdef QT_DEBUG
    m_mapSetup.insert( "DATABASE_NAME", "/Users/<USER>/Dropbox/Projects/TrySail/hr_small.db" );
#endif
}

QSqlEngine::~QSqlEngine()
{
    QSqlDatabase::removeDatabase(m_dbConnectionName);
}

bool QSqlEngine::processCode( const int chunkHandle, const QString& chunkCode )
{
    if ( !QSqlDatabase::contains( m_dbConnectionName ) ) {

        const auto dbName = m_mapSetup.value( "DATABASE_NAME", "" );
        if ( dbName.isEmpty() ) {

            const auto sError = tr( "Please specify a database." );
            emit progressLineError( chunkHandle, sError, sError );
            return false;
        }

        auto db = QSqlDatabase::addDatabase( u"QSQLITE"_s, m_dbConnectionName );
        db.setDatabaseName( dbName );
        if ( ! db.open() ) {

            const auto sError = tr( "Unable to open database: %1" ).arg( dbName );
            emit progressLineError( chunkHandle, sError, sError );
            return false;
        }
    }

    auto bError   = false;
    auto db       = QSqlDatabase::database( m_dbConnectionName );
    auto query    = QSqlQuery{ db };

    query.setForwardOnly( true );

    const auto bExec = query.exec( chunkCode );

    if ( bExec ) {

        do {
            const auto rec          = query.record();
            const auto fieldcount   = rec.count();

            auto sDisplay = QString{};

            if ( fieldcount > 0 ) {

                sDisplay += u"<table>\n"_s;


                // header
                sDisplay                += u"<tr>\n"_s;
                for ( auto nField = 0; nField < fieldcount; ++nField ) {
                    const auto sName  = rec.fieldName( nField );
                    sDisplay          += u"<th>%1</th>\n"_s.arg( sName );
                }
                sDisplay += u"</tr>\n"_s;

                // rows
                while ( query.next() ) {

                    sDisplay += u"<tr>\n"_s;
                    for ( auto nField = 0; nField < fieldcount; ++nField ) {
                        const auto rec      = query.record();
                        const auto field     = rec.field( nField );
                        const auto vValue     = field.value();
                        const auto sValue      = vValue.toString();
                        sDisplay               += u"<td>%1</td>\n"_s.arg( sValue );
                    }
                    sDisplay += u"</tr>\n"_s;
                }
                sDisplay += u"</table>"_s;
            }
            emit progressLineText( chunkHandle, sDisplay, enumContentFormat::contentFormatHtml );

        } while ( query.nextResult() );

    } else {

        if ( query.lastError().isValid() ) {
            const auto sError = query.lastError().text().trimmed();

            // this isn't really an error; just a blank or comment line
            if ( sError != u"No query Unable to fetch row"_s ) {

                bError = true;
                emit progressLineError( chunkHandle, sError, sError );
            }
        }
    }


    query.finish();

    auto memory = getMemory();
    emit progressMemoryUpdate( memory, QStringList{} );
    return ( ! bError );
}

void QSqlEngine::processRestart()
{
    QSqlDatabase::removeDatabase( m_dbConnectionName );
    QContentEngine::processRestart();
}

QContentEngine::mapMemoryType QSqlEngine::getMemory()
{
    const auto db         = QSqlDatabase::database( m_dbConnectionName );
    auto       cborMemory = QContentEngine::mapMemoryType{};
    const auto listTables = db.tables();

    for ( const auto& sTable : listTables ) {

        auto cborArrayColumn = QCborArray{};
        auto rec             = db.record( sTable );
        for ( auto nRec = 0; nRec < rec.count(); ++nRec ) {

            auto       cborOneColumn = QCborMap{};
            const auto field         = rec.field( nRec );
            cborOneColumn.insert( CBOR_ColumnName, field.name() );
            if ( !field.defaultValue().isNull() )
                cborOneColumn.insert( CBOR_ColumnDefault, field.defaultValue().toString() );
            if ( field.isAutoValue() )
                cborOneColumn.insert( CBOR_ColumnAuto, tr("Automatic") );
            if ( field.isGenerated() )
                cborOneColumn.insert( CBOR_ColumnGenerated, tr("Generated") );
            if ( field.isNull() )
                cborOneColumn.insert( CBOR_ColumnNull, tr("Nullable") );
            if ( field.isReadOnly() )
                cborOneColumn.insert( CBOR_ColumnReadOnly, tr("Read-only") );
            if ( field.length() >= 0 )
                cborOneColumn.insert( CBOR_ColumnLength, field.length() );
            if ( field.requiredStatus() == QSqlField::RequiredStatus::Required )
                cborOneColumn.insert( CBOR_ColumnRequired, tr("Required") );
            cborOneColumn.insert( CBOR_ColumnType, translate( field.metaType() ) );
            cborArrayColumn.append( cborOneColumn );
        }
        cborMemory.insert( sTable, cborArrayColumn );
    }
    return cborMemory;
}
