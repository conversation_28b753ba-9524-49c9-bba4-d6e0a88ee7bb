#include "ContentEngine.h"

#include <QApplication>
#include <QMap>
#include <QString>

// Global variables for execution state - used by Python engine
int              globalCurrentExecutionHandle{ -1 };
QContentEngine*  globalCurrentExecutionEngine{ nullptr };

QContentEngine::QContentEngine()
{
}

bool QContentEngine::processSetup( const QMap< QString, QString >& mapSetup )
{
    m_mapSetup = mapSetup;
    return true;
}

void QContentEngine::processRestart()
{
    m_executionSequence = 0;
}

void QContentEngine::setup( const QMap< QString, QString >& mapSetup )
{
    processSetup( mapSetup );
}

void QContentEngine::execute( const QList< int >& chunkHandles, const QList< QString >& chunkCodes )
{
    m_stateActual = EngineState::Running;
    emit progressJobStart();

    auto bSuccess = true;

    globalCurrentExecutionEngine = this;

    for ( auto nChunk = 0; ( bSuccess ) && ( m_stateRequested == EngineState::Running ) && ( nChunk < chunkHandles.size() ); ++nChunk ) {

        const auto& chunkHandle = chunkHandles.at( nChunk );
        const auto& chunkCode   = chunkCodes.at( nChunk );

        globalCurrentExecutionHandle = chunkHandle;

        const auto chunkCodeTrim = trimFromRight( chunkCode, { QChar{ QChar::SpecialCharacter::Space }, QChar{'\n'} } );
        if ( chunkCodeTrim.isEmpty() )
            continue;

        m_executionSequence++;
        emit progressLineStart( chunkHandle, chunkCodeTrim, m_executionSequence );
        bSuccess = processCode( chunkHandle, chunkCodeTrim );

        emit progressLineEnd( chunkHandle, ! bSuccess );
    }

    m_stateActual    = EngineState::Waiting;
    m_stateRequested = EngineState::Waiting;
    emit progressJobEnd( bSuccess );
}

void QContentEngine::stop()
{
    m_stateRequested = QContentEngine::EngineState::Waiting;
}
void QContentEngine::kill()
{
    m_stateRequested = QContentEngine::EngineState::Waiting;
}

void QContentEngine::restart()
{
    m_stateActual = EngineState::Restarting;
    emit progressRestartBegin();

    processRestart();

    auto memory = getMemory();
    emit progressMemoryUpdate( memory, QStringList{} );

    m_stateActual    = EngineState::Waiting;
    m_stateRequested = EngineState::Waiting;
    emit progressRestartEnd();
}
