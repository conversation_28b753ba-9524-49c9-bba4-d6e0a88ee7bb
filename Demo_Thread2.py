# investigating thread isolation
import psutil
p = psutil.Process()
for t in p.threads():
   print( t )
import threading
z = threading.get_ident()
print(z)
# pause to see what is going on
import time
y = 28
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
time.sleep(2)
y = y + 1
